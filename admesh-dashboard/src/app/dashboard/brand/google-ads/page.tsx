"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, ExternalLink, Play, Pause, BarChart3 } from "lucide-react";
import { toast } from "sonner";

interface GoogleAdsCampaign {
  id: string;
  campaign_id: string;
  campaign_name: string;
  status: string;
  daily_budget_micros: number;
  target_url: string;
  keywords: string[];
  created_at: any;
}

interface AccountPerformance {
  customer_id: string;
  impressions: number;
  clicks: number;
  cost_micros: number;
  conversions: number;
  ctr: number;
  average_cpc: number;
  conversion_rate: number;
}

export default function GoogleAdsPage() {
  const { user, getIdToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState<GoogleAdsCampaign[]>([]);
  const [accountPerformance, setAccountPerformance] = useState<AccountPerformance | null>(null);
  const [customerId, setCustomerId] = useState("");
  const [isValidated, setIsValidated] = useState(false);

  useEffect(() => {
    if (user) {
      loadCampaigns();
    }
  }, [user]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/campaigns`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCampaigns(data.campaigns || []);
      }
    } catch (error) {
      console.error("Error loading campaigns:", error);
      toast.error("Failed to load Google Ads campaigns");
    } finally {
      setLoading(false);
    }
  };

  const validateAccount = async () => {
    if (!customerId.trim()) {
      toast.error("Please enter a Google Ads Customer ID");
      return;
    }

    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/validate-account`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ customer_id: customerId }),
      });

      if (response.ok) {
        setIsValidated(true);
        toast.success("Google Ads account validated successfully!");
        loadAccountPerformance();
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to validate Google Ads account");
      }
    } catch (error) {
      console.error("Error validating account:", error);
      toast.error("Failed to validate Google Ads account");
    } finally {
      setLoading(false);
    }
  };

  const loadAccountPerformance = async () => {
    if (!customerId) return;

    try {
      const token = await getIdToken();
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/performance/account/${customerId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setAccountPerformance(data);
      }
    } catch (error) {
      console.error("Error loading account performance:", error);
    }
  };

  const updateCampaignStatus = async (campaignId: string, status: string) => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/campaigns/${campaignId}/update-status`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status }),
        }
      );

      if (response.ok) {
        toast.success(`Campaign ${status.toLowerCase()} successfully`);
        loadCampaigns();
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to update campaign status");
      }
    } catch (error) {
      console.error("Error updating campaign status:", error);
      toast.error("Failed to update campaign status");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (micros: number) => {
    return `$${(micros / 1000000).toFixed(2)}`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Google Ads Integration</h1>
          <p className="text-muted-foreground">
            Manage your Google Ads campaigns and track performance
          </p>
        </div>
      </div>

      <Tabs defaultValue="setup" className="space-y-6">
        <TabsList>
          <TabsTrigger value="setup">Account Setup</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Connect Google Ads Account</CardTitle>
              <CardDescription>
                Enter your Google Ads Customer ID to connect your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="customer-id">Google Ads Customer ID</Label>
                <Input
                  id="customer-id"
                  placeholder="123-456-7890"
                  value={customerId}
                  onChange={(e) => setCustomerId(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  You can find your Customer ID in your Google Ads account settings
                </p>
              </div>
              
              <Button 
                onClick={validateAccount} 
                disabled={loading || !customerId.trim()}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Validate Account
              </Button>

              {isValidated && (
                <Alert>
                  <AlertDescription>
                    ✅ Google Ads account validated successfully! You can now create campaigns.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Your Campaigns</h2>
            <Button asChild>
              <a href="/dashboard/brand/offers">
                Create Campaign from Offer
              </a>
            </Button>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : campaigns.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">
                  No Google Ads campaigns found. Create your first campaign from an existing offer.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {campaigns.map((campaign) => (
                <Card key={campaign.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-semibold">{campaign.campaign_name}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant={campaign.status === "ENABLED" ? "default" : "secondary"}>
                            {campaign.status}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            Daily Budget: {formatCurrency(campaign.daily_budget_micros)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <ExternalLink className="h-4 w-4" />
                          <a 
                            href={campaign.target_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:underline"
                          >
                            {campaign.target_url}
                          </a>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Keywords: {campaign.keywords.join(", ")}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        {campaign.status === "PAUSED" ? (
                          <Button
                            size="sm"
                            onClick={() => updateCampaignStatus(campaign.campaign_id, "ENABLED")}
                            disabled={loading}
                          >
                            <Play className="h-4 w-4 mr-1" />
                            Resume
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateCampaignStatus(campaign.campaign_id, "PAUSED")}
                            disabled={loading}
                          >
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <h2 className="text-2xl font-semibold">Performance Overview</h2>
          
          {accountPerformance ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Impressions</p>
                      <p className="text-2xl font-bold">{accountPerformance.impressions.toLocaleString()}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Clicks</p>
                      <p className="text-2xl font-bold">{accountPerformance.clicks.toLocaleString()}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Spend</p>
                      <p className="text-2xl font-bold">{formatCurrency(accountPerformance.cost_micros)}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">CTR</p>
                      <p className="text-2xl font-bold">{formatPercentage(accountPerformance.ctr)}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">
                  Connect your Google Ads account to view performance data.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
