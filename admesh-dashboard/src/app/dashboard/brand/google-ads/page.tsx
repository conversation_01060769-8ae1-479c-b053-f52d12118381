"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  Loader2, 
  ExternalLink, 
  Play, 
  Pause, 
  BarChart3, 
  RefreshCw, 
  TrendingUp, 
  Target, 
  Zap, 
  Settings,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Users,
  MousePointer,
  Eye
} from "lucide-react";
import { toast } from "sonner";

interface ConnectionStatus {
  connected: boolean;
  user_info?: any;
  ads_accounts?: any[];
  message?: string;
}

interface AccountOverview {
  total_accounts: number;
  accounts: any[];
  aggregate_performance: any;
}

interface RealTimeMetrics {
  timestamp: string;
  today_metrics: any;
  hourly_trend: any[];
}

interface Recommendation {
  type: string;
  title: string;
  description: string;
  impact: string;
  effort: string;
  estimated_impact: any;
}

export default function GoogleAdsPage() {
  const { user, getIdToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({ connected: false });
  const [accountOverview, setAccountOverview] = useState<AccountOverview | null>(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (user) {
      checkConnectionStatus();
    }
  }, [user]);

  useEffect(() => {
    if (connectionStatus.connected) {
      loadDashboardData();
      
      // Set up real-time updates every 30 seconds
      const interval = setInterval(() => {
        if (activeTab === "overview" || activeTab === "realtime") {
          loadRealTimeData();
        }
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [connectionStatus.connected, activeTab]);

  const checkConnectionStatus = async () => {
    try {
      const token = await getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/oauth/status`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const status = await response.json();
        setConnectionStatus(status);
      }
    } catch (error) {
      console.error("Error checking connection status:", error);
    }
  };

  const initiateOAuth = async () => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/oauth/initiate`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        // Open OAuth URL in new window
        window.open(data.auth_url, "_blank", "width=600,height=700");
        
        // Poll for connection status
        const pollInterval = setInterval(async () => {
          await checkConnectionStatus();
          if (connectionStatus.connected) {
            clearInterval(pollInterval);
            toast.success("Google Ads account connected successfully!");
            loadDashboardData();
          }
        }, 2000);

        // Stop polling after 5 minutes
        setTimeout(() => clearInterval(pollInterval), 300000);
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to initiate OAuth");
      }
    } catch (error) {
      console.error("Error initiating OAuth:", error);
      toast.error("Failed to initiate OAuth");
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      const token = await getIdToken();
      
      // Load account overview
      const overviewResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/overview`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (overviewResponse.ok) {
        const overview = await overviewResponse.json();
        setAccountOverview(overview);
      }

      // Load campaigns
      const campaignsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/campaigns`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json();
        setCampaigns(campaignsData.campaigns || []);
      }

      // Load real-time data
      await loadRealTimeData();

    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  };

  const loadRealTimeData = async () => {
    try {
      if (!connectionStatus.ads_accounts?.[0]) return;

      const token = await getIdToken();
      const customerId = connectionStatus.ads_accounts[0].customer_id;
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/realtime/${customerId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const metrics = await response.json();
        setRealTimeMetrics(metrics);
      }

      // Load recommendations
      const recResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/recommendations/${customerId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (recResponse.ok) {
        const recData = await recResponse.json();
        setRecommendations(recData.recommendations || []);
      }

    } catch (error) {
      console.error("Error loading real-time data:", error);
    }
  };

  const syncAccountData = async () => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/google-ads/sync`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ force_refresh: true }),
      });

      if (response.ok) {
        toast.success("Account data synced successfully!");
        await loadDashboardData();
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to sync data");
      }
    } catch (error) {
      console.error("Error syncing data:", error);
      toast.error("Failed to sync data");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (micros: number) => {
    return `$${(micros / 1000000).toFixed(2)}`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  const formatNumber = (value: number) => {
    return value.toLocaleString();
  };

  if (!connectionStatus.connected) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Google Ads Integration</h1>
            <p className="text-muted-foreground">
              Connect your Google Ads account to unlock powerful automation and insights
            </p>
          </div>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <ExternalLink className="h-6 w-6" />
              Connect Google Ads Account
            </CardTitle>
            <CardDescription>
              Securely connect your Google Ads account using OAuth 2.0 to enable automated campaign management
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold">Automated Optimization</h3>
                <p className="text-sm text-muted-foreground">AI-powered bid and budget optimization</p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold">Real-time Analytics</h3>
                <p className="text-sm text-muted-foreground">Live performance tracking and insights</p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold">Smart Recommendations</h3>
                <p className="text-sm text-muted-foreground">Data-driven optimization suggestions</p>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Your Google Ads data will be securely accessed and never shared. You can disconnect at any time.
              </AlertDescription>
            </Alert>

            <Button 
              onClick={initiateOAuth} 
              disabled={loading}
              className="w-full"
              size="lg"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Connect Google Ads Account
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Google Ads Dashboard</h1>
          <p className="text-muted-foreground">
            Automated campaign management and real-time performance insights
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={syncAccountData} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Sync Data
          </Button>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
          <TabsTrigger value="recommendations">AI Insights</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {accountOverview && (
            <>
              {/* Account Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Spend</p>
                        <p className="text-2xl font-bold">
                          {formatCurrency(accountOverview.aggregate_performance.total_cost_micros)}
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Impressions</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(accountOverview.aggregate_performance.total_impressions)}
                        </p>
                      </div>
                      <Eye className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Clicks</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(accountOverview.aggregate_performance.total_clicks)}
                        </p>
                      </div>
                      <MousePointer className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Conversions</p>
                        <p className="text-2xl font-bold">
                          {accountOverview.aggregate_performance.total_conversions.toFixed(1)}
                        </p>
                      </div>
                      <Target className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Account Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Account Performance</CardTitle>
                  <CardDescription>Performance metrics across all connected accounts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Click-through Rate</span>
                        <span className="text-sm text-muted-foreground">
                          {formatPercentage(accountOverview.aggregate_performance.overall_ctr || 0)}
                        </span>
                      </div>
                      <Progress value={(accountOverview.aggregate_performance.overall_ctr || 0) * 1000} />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Conversion Rate</span>
                        <span className="text-sm text-muted-foreground">
                          {formatPercentage(accountOverview.aggregate_performance.overall_conversion_rate || 0)}
                        </span>
                      </div>
                      <Progress value={(accountOverview.aggregate_performance.overall_conversion_rate || 0) * 1000} />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Avg. CPC</span>
                        <span className="text-sm text-muted-foreground">
                          {formatCurrency(accountOverview.aggregate_performance.overall_average_cpc || 0)}
                        </span>
                      </div>
                      <Progress value={Math.min((accountOverview.aggregate_performance.overall_average_cpc || 0) / 50000, 100)} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Performance</CardTitle>
              <CardDescription>Detailed performance metrics for all campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              {campaigns.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No campaigns found. Sync your account to load campaign data.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {campaigns.slice(0, 10).map((campaign) => (
                    <div key={campaign.campaign_id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">{campaign.campaign_name}</h3>
                          <Badge variant={campaign.status === "ENABLED" ? "default" : "secondary"}>
                            {campaign.status}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-muted-foreground">Daily Budget</p>
                          <p className="font-medium">{formatCurrency(campaign.budget?.amount_micros || 0)}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Impressions</p>
                          <p className="font-medium">{formatNumber(campaign.performance?.impressions || 0)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Clicks</p>
                          <p className="font-medium">{formatNumber(campaign.performance?.clicks || 0)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">CTR</p>
                          <p className="font-medium">{formatPercentage(campaign.performance?.ctr || 0)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Cost</p>
                          <p className="font-medium">{formatCurrency(campaign.performance?.cost_micros || 0)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          {realTimeMetrics && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Today's Performance
                  </CardTitle>
                  <CardDescription>
                    Real-time metrics updated every 30 seconds
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatNumber(realTimeMetrics.today_metrics.impressions)}</p>
                      <p className="text-sm text-muted-foreground">Impressions Today</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatNumber(realTimeMetrics.today_metrics.clicks)}</p>
                      <p className="text-sm text-muted-foreground">Clicks Today</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatCurrency(realTimeMetrics.today_metrics.cost_micros)}</p>
                      <p className="text-sm text-muted-foreground">Spend Today</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatPercentage(realTimeMetrics.today_metrics.ctr)}</p>
                      <p className="text-sm text-muted-foreground">CTR Today</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Hourly Performance Trend</CardTitle>
                  <CardDescription>Performance breakdown by hour of day</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {realTimeMetrics.hourly_trend.slice(0, 12).map((hour) => (
                      <div key={hour.hour} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{hour.hour}:00</span>
                        <div className="flex items-center gap-4 text-sm">
                          <span>{formatNumber(hour.impressions)} imp</span>
                          <span>{formatNumber(hour.clicks)} clicks</span>
                          <span>{formatCurrency(hour.cost_micros)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                AI-Powered Recommendations
              </CardTitle>
              <CardDescription>
                Smart optimization suggestions based on your campaign performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recommendations.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Loading recommendations...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">{rec.title}</h3>
                          <p className="text-sm text-muted-foreground">{rec.description}</p>
                        </div>
                        <div className="flex gap-2">
                          <Badge variant={rec.impact === "HIGH" ? "default" : rec.impact === "MEDIUM" ? "secondary" : "outline"}>
                            {rec.impact} Impact
                          </Badge>
                          <Badge variant="outline">{rec.effort} Effort</Badge>
                        </div>
                      </div>
                      
                      {rec.estimated_impact && (
                        <div className="mt-3 p-3 bg-muted rounded">
                          <p className="text-sm font-medium mb-1">Estimated Impact:</p>
                          <div className="text-sm text-muted-foreground">
                            {Object.entries(rec.estimated_impact).map(([key, value]) => (
                              <span key={key} className="mr-4">
                                {key.replace(/_/g, ' ')}: {value}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="flex justify-end mt-3">
                        <Button size="sm">Apply Recommendation</Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Automation Settings
              </CardTitle>
              <CardDescription>
                Configure automated optimization rules and schedules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Automated Bid Optimization</h3>
                    <p className="text-sm text-muted-foreground">
                      Automatically adjust keyword bids based on performance
                    </p>
                  </div>
                  <Badge variant="default">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Budget Reallocation</h3>
                    <p className="text-sm text-muted-foreground">
                      Move budget from low-performing to high-performing campaigns
                    </p>
                  </div>
                  <Badge variant="default">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Keyword Discovery</h3>
                    <p className="text-sm text-muted-foreground">
                      Automatically add high-performing search terms as keywords
                    </p>
                  </div>
                  <Badge variant="default">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Ad Copy Testing</h3>
                    <p className="text-sm text-muted-foreground">
                      Create and test new ad variations automatically
                    </p>
                  </div>
                  <Badge variant="secondary">
                    <Pause className="h-3 w-3 mr-1" />
                    Paused
                  </Badge>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <Button className="w-full">
                  <Zap className="mr-2 h-4 w-4" />
                  Run Full Optimization Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
