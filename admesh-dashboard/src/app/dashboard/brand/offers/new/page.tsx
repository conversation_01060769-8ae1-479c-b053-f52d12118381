"use client";

import { Suspense } from "react";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { dollarsToCents } from "@/lib/utils";

import { useAuth } from "@/hooks/use-auth";
import {
  ArrowLeft,
  ChevronRight,
  AlertCircle,
  FileText,
  Loader2,
} from "lucide-react";
import OfferIncentiveForm from "@/app/dashboard/brand/onboarding/components/ui/OfferIncentiveForm";
import { OfferIncentive } from "@/types/onboarding";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Info, ExternalLink } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import PixelImplementationExamples from "@/components/PixelImplementationExamples";

function NewOfferPage() {
  const router = useRouter();
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const [products, setProducts] = useState<{ id: string; title: string }[]>([]);
  const [activeTab, setActiveTab] = useState("details");
  const [serverCodeLanguage, setServerCodeLanguage] = useState("nodejs");
  const [createdOfferId, setCreatedOfferId] = useState<string>("");
  const [showHowItWorks, setShowHowItWorks] = useState(false);

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  // Define the form type
  interface OfferForm {
    product_id: string;
    goal: string;
    reward_note: string;
    payout_amount: string;
    payout_currency: string;
    payout_model: string;
    budget: string;
    categories: string;
    keywords: string;
    tracking_method: string;
    redirect_url: string;
    webhook_url: string;
    tracking_notes: string;
  }

  const initialForm: OfferForm = {
    // Offer details
    product_id: "",
    goal: "signup", // Default conversion goal
    reward_note: "$1.00 per signup", // Initial value, will be auto-calculated
    payout_amount: "1.00",
    payout_currency: "USD",
    payout_model: "CPA",
    budget: "100.00",
    categories: "",
    keywords: "",

    // Integration details
    tracking_method: "redirect_pixel",
    redirect_url: "https://yourbrand.com/signup",
    webhook_url: "",
    tracking_notes: "",
  };

  const [form, setForm] = useState<OfferForm>(initialForm);
  const [offerIncentive, setOfferIncentive] = useState<
    OfferIncentive | undefined
  >(undefined);

  // Auto-fill product_id from query param
  useEffect(() => {
    // Check for both parameter names (product_id and productId) for compatibility
    const pid = searchParams.get("product_id") || searchParams.get("productId");
    if (pid) {
      setForm((prev) => ({ ...prev, product_id: pid }));
    }
  }, [searchParams]);

  // Fetch all products and use the selected product to pre-fill the redirect URL
  useEffect(() => {
    if (!user || !form.product_id) return;

    const fetchProductDetails = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/all`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (res.ok) {
          const data = await res.json();
          const selectedProduct = data.products?.find(
            (p: { id: string; url?: string }) => p.id === form.product_id
          );

          if (selectedProduct?.url) {
            // Use the product URL to create a signup URL
            const baseUrl = selectedProduct.url.replace(/\/$/, "");
            setForm((prev) => ({
              ...prev,
              redirect_url: `${baseUrl}/signup`,
            }));
          }
        }
      } catch (error) {
        console.error("Error fetching product details:", error);
        // If we can't fetch the details, we'll just use the default redirect URL
      }
    };

    fetchProductDetails();
  }, [user, form.product_id]);

  useEffect(() => {
    const fetchProducts = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const idToken = await user.getIdToken();

        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/names`,
          {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
          }
        );

        if (res.ok) {
          setProducts((await res.json()).products || []);
        } else {
          throw new Error("Failed to fetch products");
        }
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user]);

  // Check if user already has active offers - but don't show errors yet
  useEffect(() => {
    if (!user) return;

    const checkActiveOffers = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (res.ok) {
          const data = await res.json();
          const activeOffers = data.offers.filter(
            (offer: { offer_status: string }) => offer.offer_status === "active"
          );

          if (activeOffers.length > 0) {
            setHasActiveOffer(true);
            // Don't show error message yet - we'll show it only when they try to submit
          }
        }
      } catch (err) {
        console.error("Error checking active offers:", err);
      }
    };

    checkActiveOffers();
  }, [user]);

  const updateRewardNote = (formData: typeof form) => {
    // Get currency symbol
    let currencySymbol = "$";
    if (formData.payout_currency === "EUR") currencySymbol = "€";
    else if (formData.payout_currency === "GBP") currencySymbol = "£";

    // Format: "$X.XX per signup"
    return `${currencySymbol}${formData.payout_amount} per ${formData.goal}`;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    setForm((prev) => {
      const updatedForm = { ...prev, [name]: value };

      // If payout_amount changes, update the reward_note
      if (name === "payout_amount") {
        return {
          ...updatedForm,
          reward_note: updateRewardNote(updatedForm),
        };
      }

      return updatedForm;
    });

    // Clear errors when user makes changes
    setErrors([]);
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm((prev) => {
      const updatedForm = { ...prev, [name]: value };

      // If goal or currency changes, update the reward note
      if (name === "goal" || name === "payout_currency") {
        return {
          ...updatedForm,
          reward_note: updateRewardNote(updatedForm),
        };
      }

      return updatedForm;
    });

    // Clear errors when user makes changes
    setErrors([]);
  };

  const validateForm = () => {
    const newErrors = [];

    if (!form.product_id) {
      newErrors.push("Please select a product");
    }

    if (!form.payout_amount || parseFloat(form.payout_amount) <= 0) {
      newErrors.push("Please enter a valid payout amount");
    }

    if (!form.budget || parseFloat(form.budget) <= 0) {
      newErrors.push("Please enter a valid budget");
    }

    setErrors(newErrors);
    return newErrors;
  };

  const handleSubmit = async () => {
    const validationErrors = validateForm();

    if (validationErrors.length > 0) {
      return;
    }

    if (!user) {
      toast.error("You must be logged in to create an offer");
      return;
    }

    // Check for active offers before submitting
    if (hasActiveOffer) {
      toast.error(
        "You can only have one active offer at a time. Please deactivate your existing offer before creating a new one."
      );
      return;
    }

    setLoading(true);

    try {
      // Get the auth token
      const token = await user.getIdToken();

      // Prepare the offer data
      const offerData = {
        product_id: form.product_id,
        reward_note: form.reward_note,
        payout: {
          // Convert payout amount from dollars to cents for storage
          amount: dollarsToCents(form.payout_amount),
          currency: form.payout_currency,
          model: form.payout_model,
        },
        // Convert budget from dollars to cents for storage
        offer_total_budget_allocated: dollarsToCents(form.budget),
        goal: form.goal,
        active: false, // Set offers as inactive by default
        // Additional required fields from the API
        title:
          products.find((p) => p.id === form.product_id)?.title ||
          "Untitled Offer",
        description: form.reward_note,
        url: form.redirect_url,
        categories: form.categories.split(",").map(c => c.trim()).filter(c => c.length > 0),
        keywords: form.keywords.split(",").map(k => k.trim()).filter(k => k.length > 0),
        offer_trust_score: 100, // Set default offer_trust_score
        offer_views: {
          test: 0,
          production: 0,
          total: 0,
        },
        offer_incentive: offerIncentive, // Include offer incentive if provided
      };

      // Call the API to create the offer
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(offerData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to create offer");
      }

      const data = await response.json();
      setCreatedOfferId(data.offer_id);
      toast.success("Offer created! Now let's set up tracking.");

      // Move to tracking tab
      setActiveTab("tracking");
    } catch (error) {
      console.error("Error creating offer:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create offer"
      );
    } finally {
      setLoading(false);
    }
  };

  const setupIntegration = async () => {
    if (!user) {
      toast.error("You must be logged in to set up tracking");
      return;
    }

    if (!createdOfferId) {
      toast.error("No offer ID found. Please try again.");
      return;
    }

    // Validate integration fields
    const validationErrors: string[] = [];
    if (form.tracking_method === "redirect_pixel" && !form.redirect_url) {
      validationErrors.push(
        "Redirect URL is required for Redirect + Pixel tracking"
      );
    }
    if (form.tracking_method === "server_api" && !form.webhook_url) {
      validationErrors.push(
        "Webhook URL is required for Server-Side API tracking"
      );
    }

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      toast.error("Please fix the errors before setting up tracking");
      return;
    }

    setLoading(true);

    try {
      // Get the auth token
      const token = await user.getIdToken();

      // Set up integration
      const integrationData = {
        offer_id: createdOfferId,
        product_id: form.product_id,
        method: form.tracking_method,
        redirect_url: form.redirect_url,
        webhook_url: form.webhook_url,
        notes: form.tracking_notes,
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/integration/setup`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(integrationData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to set up tracking");
      }

      toast.success("Tracking setup completed successfully!");

      // Redirect to offers page
      router.push("/dashboard/brand/offers");
    } catch (error) {
      console.error("Error setting up tracking:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to set up tracking"
      );
    } finally {
      setLoading(false);
    }
  };

  const payoutModelDescriptions = {
    CPA: "Pay when a specific action is completed (e.g., signup, purchase)",
    CPC: "Pay for each click on your affiliate links",
    CPL: "Pay when a qualified lead is generated",
    CPS: "Pay when a sale is completed",
    RevShare: "Share a percentage of revenue with affiliates",
  };

  const trackingMethodOptions = [
    {
      value: "redirect_pixel",
      label: "Redirect + Pixel",
      description:
        "Easiest setup. We'll provide a redirect URL and tracking pixel for your thank-you page.",
      bestFor: "Most websites with a thank you/confirmation page",
      setupTime: "5-10 minutes",
    },
    // {
    //   value: "server_api",
    //   label: "Server-Side API",
    //   description: "Most secure. We'll send conversion data to your server via webhook.",
    //   bestFor: "SaaS products, apps with backend integration",
    //   setupTime: "15-30 minutes"
    // },
    // {
    //   value: "manual",
    //   label: "Manual Approval",
    //   description: "You'll manually approve conversions in your dashboard. Best for custom campaigns.",
    //   bestFor: "Low-volume campaigns or unique conversion flows",
    //   setupTime: "No setup required"
    // }
  ];

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 space-y-6">
      <div className="flex items-center justify-between mb-2">
        <div
          className="flex items-center gap-2 cursor-pointer text-muted-foreground hover:text-foreground transition"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm font-medium">Back to Offers</span>
        </div>

        <Badge
          variant="outline"
          className="bg-blue-50 text-blue-700 border-blue-200"
        >
          Draft
        </Badge>
      </div>

      <Card className="border shadow-sm">
        <CardHeader className="bg-slate-50 border-b pb-4">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl">Create New Offer</CardTitle>
              <CardDescription className="mt-1">
                Set up a new affiliate offer by defining payout details and
                tracking methods
              </CardDescription>
            </div>
            <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-200">
              <FileText className="h-3 w-3 mr-1" />
              Draft
            </Badge>
          </div>
        </CardHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="px-6 pt-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger
                value="details"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                1. Offer Details
              </TabsTrigger>
              <TabsTrigger
                value="tracking"
                disabled={errors.length > 0}
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                2. Tracking Setup
              </TabsTrigger>
              <TabsTrigger
                value="google-ads"
                disabled={errors.length > 0}
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                3. Google Ads (Optional)
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="details" className="mt-0 p-0">
            <CardContent className="pt-6 space-y-6">
              {errors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc pl-5 mt-2">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              <div className="grid gap-6 md:grid-cols-3">
                <div>
                  <Label
                    htmlFor="product_id"
                    className="text-sm font-medium flex items-center gap-1"
                  >
                    Product
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-64">
                            Select the product that affiliates will promote
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Select
                    value={form.product_id}
                    onValueChange={(value) =>
                      handleSelectChange("product_id", value)
                    }
                    disabled={loading}
                  >
                    <SelectTrigger id="product_id" className="mt-1">
                      <SelectValue placeholder="Choose a product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.length === 0 ? (
                        <SelectItem value="no-products" disabled>
                          No products available
                        </SelectItem>
                      ) : (
                        products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.title}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label
                    htmlFor="goal"
                    className="text-sm font-medium flex items-center gap-1"
                  >
                    Conversion Goal
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-64">
                            What action should count as a conversion?
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Select
                    value={form.goal}
                    onValueChange={(value) => handleSelectChange("goal", value)}
                    disabled={loading}
                  >
                    <SelectTrigger id="goal" className="mt-1">
                      <SelectValue placeholder="Select goal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="signup">Signup</SelectItem>
                      {/* <SelectItem value="purchase">Purchase</SelectItem>
                      <SelectItem value="lead">Lead</SelectItem>
                      <SelectItem value="app_install">App Install</SelectItem>
                      <SelectItem value="click">Click</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    The action that triggers a payout
                  </p>
                </div>

                <div>
                  <Label
                    htmlFor="payout_model"
                    className="text-sm font-medium flex items-center gap-1"
                  >
                    Payout Model
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-64">
                            {payoutModelDescriptions[
                              form.payout_model as keyof typeof payoutModelDescriptions
                            ] || "How affiliates will be compensated"}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Select
                    value={form.payout_model}
                    onValueChange={(value) =>
                      handleSelectChange("payout_model", value)
                    }
                    disabled={loading}
                  >
                    <SelectTrigger id="payout_model" className="mt-1">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CPA">
                        CPA - Cost Per Acquisition
                      </SelectItem>
                      {/* <SelectItem value="CPC">CPC - Cost Per Click</SelectItem> */}
                      {/* <SelectItem value="CPL">CPL - Cost Per Lead</SelectItem>
                      <SelectItem value="CPS">CPS - Cost Per Sale</SelectItem>
                      <SelectItem value="RevShare">Revenue Share</SelectItem> */}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-3">
                <div>
                  <Label
                    htmlFor="payout_amount"
                    className="text-sm font-medium"
                  >
                    Payout Amount
                  </Label>
                  <div className="relative mt-1">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="payout_amount"
                      name="payout_amount"
                      value={form.payout_amount}
                      onChange={handleChange}
                      placeholder="1.00"
                      type="number"
                      min="0"
                      step="0.01"
                      className="pl-8"
                      disabled={loading}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Amount paid per conversion
                  </p>
                </div>

                <div>
                  <Label
                    htmlFor="payout_currency"
                    className="text-sm font-medium"
                  >
                    Currency
                  </Label>
                  <Select
                    value={form.payout_currency}
                    onValueChange={(value) =>
                      handleSelectChange("payout_currency", value)
                    }
                    disabled={loading}
                  >
                    <SelectTrigger id="payout_currency" className="mt-1">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      {/* <SelectItem value="EUR">EUR - Euro</SelectItem>
                      <SelectItem value="GBP">GBP - British Pound</SelectItem>
                      <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                      <SelectItem value="AUD">AUD - Australian Dollar</SelectItem> */}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="budget" className="text-sm font-medium">
                    Total Budget
                  </Label>
                  <div className="relative mt-1">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="budget"
                      name="budget"
                      value={form.budget}
                      onChange={handleChange}
                      type="number"
                      min="0"
                      step="0.01"
                      className="pl-8"
                      placeholder="100.00"
                      disabled={loading}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Maximum spend for this campaign
                  </p>
                </div>
              </div>

              <Separator className="my-4" />

              {/* Categories and Keywords Section */}
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <Label htmlFor="categories" className="text-sm font-medium">
                    Categories (comma-separated, max 3)
                  </Label>
                  <Input
                    id="categories"
                    name="categories"
                    value={form.categories}
                    onChange={handleChange}
                    placeholder="e.g. Marketing, SaaS, Analytics"
                    className="mt-1"
                    disabled={loading}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Help agents find your offer by categorizing it (max 3 categories)
                  </p>
                </div>

                <div>
                  <Label htmlFor="keywords" className="text-sm font-medium">
                    Keywords (comma-separated)
                  </Label>
                  <Input
                    id="keywords"
                    name="keywords"
                    value={form.keywords}
                    onChange={handleChange}
                    placeholder="e.g. productivity, automation, workflow"
                    className="mt-1"
                    disabled={loading}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Keywords help agents understand what your product does
                  </p>
                </div>
              </div>

              <Separator className="my-4" />

              {/* Offer Incentive Section */}
              <div>
                <OfferIncentiveForm
                  incentive={offerIncentive}
                  onChange={setOfferIncentive}
                />
              </div>

              <Separator className="my-4" />

              <div>
                <Label
                  htmlFor="reward_note"
                  className="text-sm font-medium flex items-center gap-1"
                >
                  Reward Note
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-64">
                          Automatically calculated based on payout amount and
                          goal
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="reward_note"
                  name="reward_note"
                  value={form.reward_note}
                  className="mt-1 bg-gray-50"
                  readOnly
                />
                <p className="text-xs text-muted-foreground mt-1">
                  This reward note will be visible to affiliates when they view
                  your offer
                </p>
              </div>

              <div className="bg-primary/5 border border-primary/20 rounded-lg p-4 mt-2">
                <h3 className="font-medium text-sm">Offer Summary</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  You&apos;ll pay{" "}
                  <strong>${form.payout_amount || "..."}</strong> per
                  <strong> {form.goal || "conversion"}</strong> using{" "}
                  <strong>{form.payout_model || "..."}</strong> model with a
                  total budget of <strong>${form.budget || "..."}</strong>.
                </p>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between border-t p-6 bg-gray-50">
              <Button
                variant="outline"
                onClick={() => router.back()}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading}
                className="px-8"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Next: Setup Tracking
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </CardFooter>
          </TabsContent>

          <TabsContent value="tracking" className="mt-0 p-0">
            <CardContent className="pt-6 space-y-6">
              {errors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc pl-5 mt-2">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">
                  Choose How to Track Conversions
                </h3>
                <p className="text-muted-foreground">
                  Select how you&apos;d like AdMesh to verify when a conversion
                  happens.
                </p>
              </div>

              <div className="space-y-6">
                <div>
                  <Label className="text-sm font-medium">
                    Verification Method
                  </Label>
                  <div className="grid gap-4 mt-3">
                    {trackingMethodOptions.map((option) => (
                      <div
                        key={option.value}
                        className={`
                          relative flex items-start p-4 rounded-lg border-2 cursor-pointer
                          ${
                            form.tracking_method === option.value
                              ? "border-primary bg-primary/5"
                              : "border-border hover:border-primary/50"
                          }
                        `}
                        onClick={() =>
                          handleSelectChange("tracking_method", option.value)
                        }
                      >
                        <div className="min-w-0 flex-1 text-sm">
                          <div className="font-medium flex items-center">
                            <div
                              className={`w-4 h-4 mr-3 rounded-full flex items-center justify-center ${
                                form.tracking_method === option.value
                                  ? "bg-primary"
                                  : "border border-gray-300"
                              }`}
                            >
                              {form.tracking_method === option.value && (
                                <div className="w-2 h-2 rounded-full bg-white" />
                              )}
                            </div>
                            {option.label}
                          </div>
                          <p className="text-muted-foreground mt-1">
                            {option.description}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center">
                                <span className="font-medium mr-1">
                                  Best for:
                                </span>{" "}
                                {option.bestFor || "Most websites"}
                              </div>
                              <div className="flex items-center">
                                <span className="font-medium mr-1">
                                  Setup time:
                                </span>{" "}
                                {option.setupTime || "5-15 minutes"}
                              </div>
                            </div>
                            <button
                              type="button"
                              className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowHowItWorks((prev) => !prev);
                              }}
                            >
                              How it works
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {showHowItWorks && (
                  <div className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200 animate-fadeIn">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-sm font-medium text-blue-800">
                        How It Works
                      </h4>
                      <button
                        type="button"
                        className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                        onClick={() => setShowHowItWorks(false)}
                      >
                        Close
                      </button>
                    </div>
                    {form.tracking_method === "redirect_pixel" ? (
                      <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                        <li>
                          Someone clicks on a promotional link for your product
                        </li>
                        <li>
                          They visit your website where you&apos;ve added our
                          simple tracking code
                        </li>
                        <li>
                          When they complete a purchase or sign up, we
                          automatically record this success
                        </li>
                        <li>
                          The promoter gets credit, and you see all results in
                          your dashboard
                        </li>
                      </ol>
                    ) : form.tracking_method === "server_api" ? (
                      <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                        <li>
                          Someone clicks on a promotional link for your product
                        </li>
                        <li>
                          When they make a purchase, your server sends this
                          information to our API
                        </li>
                        <li>
                          We verify the purchase and credit the promoter
                          automatically
                        </li>
                        <li>
                          You can see all conversions and payouts in your
                          dashboard
                        </li>
                      </ol>
                    ) : (
                      <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                        <li>
                          Someone clicks on a promotional link for your product
                        </li>
                        <li>
                          You&apos;ll see potential sales in your dashboard
                        </li>
                        <li>
                          You simply approve or decline each sale as it happens
                        </li>
                        <li>
                          Approved conversions are credited to the promoter
                          automatically
                        </li>
                      </ol>
                    )}
                  </div>
                )}

                <div className="mt-6 space-y-6">
                  {form.tracking_method === "redirect_pixel" && (
                    <div className="space-y-4">
                      <div>
                        <Label
                          htmlFor="redirect_url"
                          className="text-sm font-medium"
                        >
                          Redirect URL
                        </Label>
                        <Input
                          id="redirect_url"
                          name="redirect_url"
                          value={form.redirect_url}
                          onChange={handleChange}
                          placeholder="https://yourbrand.com/signup"
                          className="mt-1"
                          disabled={loading}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Where users will be sent after clicking affiliate
                          links (typically your signup or conversion page)
                        </p>
                      </div>

                      {createdOfferId && (
                        <div className="flex gap-2 mt-3">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => {
                              // Use the /click/r/{ad_id} endpoint to record a test click and get a clickId
                              const apiBaseUrl =
                                process.env.NEXT_PUBLIC_API_BASE_URL ||
                                "https://api.useadmesh.com";

                              // Just use the offer ID as the product ID for test clicks
                              const productId = createdOfferId;

                              // Create a properly formatted redirect URL with query parameters
                              // IMPORTANT: Make sure to use '?' for the first parameter and '&' for subsequent ones
                              const redirectUrl = `${window.location.origin}/test-conversion?offer_id=${createdOfferId}&test=true&admesh=true`;

                              // Create the final URL that will record the click and redirect to the test conversion page
                              const finalUrl = `${apiBaseUrl}/click/r/${createdOfferId}?test=true&product_id=${productId}&offer_id=${createdOfferId}&redirect_url=${encodeURIComponent(
                                redirectUrl
                              )}`;

                              // Log the URL for debugging
                              console.log("Test click URL:", finalUrl);
                              console.log(
                                "Redirect URL (decoded):",
                                redirectUrl
                              );

                              // Open the URL in a new tab - this will record the click and redirect to the test conversion page
                              window.open(finalUrl, "_blank");
                            }}
                          >
                            Test Conversion
                          </Button>
                        </div>
                      )}

                      {/* Implementation Examples */}
                      <div className="mt-4">
                        <PixelImplementationExamples />
                      </div>

                      {/* Need Help Section */}
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-md flex items-start mt-4">
                        <Info className="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-800 mb-1">
                            Need Help?
                          </h4>
                          <p className="text-sm text-blue-700 mb-2">
                            Contact us for implementation assistance:
                          </p>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                              onClick={() =>
                                window.open(
                                  "https://calendly.com/gounimanikumar/30min",
                                  "_blank"
                                )
                              }
                            >
                              Schedule Implementation Call
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                              onClick={() =>
                                window.open(
                                  "mailto:<EMAIL>?subject=AdMesh%20Implementation%20Assistance&body=Hi%20Mani%2C%0A%0AI%20need%20help%20with%20implementing%20AdMesh%20tracking%20for%20my%20offer.%20Could%20you%20please%20assist%20me%20with%20the%20following%3A%0A%0A-%20%5BDescribe%20your%20implementation%20question%20or%20issue%5D%0A%0AThanks%2C%0A%5BYour%20Name%5D"
                                )
                              }
                            >
                              Email: <EMAIL>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                              onClick={() =>
                                window.open(
                                  "https://www.linkedin.com/in/manikumargouni/",
                                  "_blank"
                                )
                              }
                            >
                              LinkedIn Profile
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {form.tracking_method === "server_api" && (
                    <div className="space-y-4">
                      <div>
                        <Label
                          htmlFor="webhook_url"
                          className="text-sm font-medium"
                        >
                          Webhook URL
                        </Label>
                        <Input
                          id="webhook_url"
                          name="webhook_url"
                          value={form.webhook_url}
                          onChange={handleChange}
                          placeholder="https://yourbrand.com/api/verify-conversion"
                          className="mt-1"
                          disabled={loading}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          AdMesh will send signed conversion POST requests to
                          this endpoint
                        </p>
                      </div>

                      <div className="border border-border rounded-lg overflow-hidden mt-4">
                        <div className="p-4 border-b">
                          <h4 className="text-sm font-medium mb-3">
                            Implementation Examples
                          </h4>
                          <Tabs
                            value={serverCodeLanguage}
                            onValueChange={setServerCodeLanguage}
                            className="w-full"
                          >
                            <TabsList className="w-full grid grid-cols-4">
                              <TabsTrigger value="nodejs" className="text-xs">
                                Node.js
                              </TabsTrigger>
                              <TabsTrigger value="python" className="text-xs">
                                Python
                              </TabsTrigger>
                              <TabsTrigger value="java" className="text-xs">
                                Java
                              </TabsTrigger>
                              <TabsTrigger value="prompt" className="text-xs">
                                AI Prompt
                              </TabsTrigger>
                            </TabsList>

                            <TabsContent value="nodejs" className="mt-3">
                              <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                                <pre>{`// When a user converts on your platform
async function recordConversion(userId, conversionData) {
  // Get the stored tracking parameters for this user
  const { ad_id, click_id } = getUserTrackingParams(userId);

  // Call the AdMesh API to record the conversion
  const response = await fetch("https://api.useadmesh.com/conversion/log", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer YOUR_API_KEY"
    },
    body: JSON.stringify({
      offer_id: "${createdOfferId}",
      ad_id: ad_id,
      click_id: click_id,
      conversion_value: conversionData.value,
      conversion_type: "${form.goal || "signup"}"
    })
  });

  return response.json();
}`}</pre>
                                <Button
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => {
                                    const code = `// When a user converts on your platform
async function recordConversion(userId, conversionData) {
  // Get the stored tracking parameters for this user
  const { ad_id, click_id } = getUserTrackingParams(userId);

  // Call the AdMesh API to record the conversion
  const response = await fetch("https://api.admesh.io/conversion/log", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer YOUR_API_KEY"
    },
    body: JSON.stringify({
      offer_id: "${createdOfferId}",
      ad_id: ad_id,
      click_id: click_id,
      conversion_value: conversionData.value,
      conversion_type: "${form.goal || "signup"}"
    })
  });

  return response.json();
}`;
                                    navigator.clipboard
                                      .writeText(code)
                                      .then(() =>
                                        toast.success("Code copied!")
                                      );
                                  }}
                                >
                                  Copy Code
                                </Button>
                              </div>
                            </TabsContent>

                            <TabsContent value="python" className="mt-3">
                              <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                                <pre>{`# When a user converts on your platform
import requests
import json

def record_conversion(user_id, conversion_data):
    # Get the stored tracking parameters for this user
    tracking_params = get_user_tracking_params(user_id)
    ad_id = tracking_params.get('ad_id')
    click_id = tracking_params.get('click_id')

    # Call the AdMesh API to record the conversion
    response = requests.post(
        "https://api.useadmesh.com/conversion/log",
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer YOUR_API_KEY"
        },
        json={
            "offer_id": "${createdOfferId}",
            "ad_id": ad_id,
            "click_id": click_id,
            "conversion_value": conversion_data.get('value'),
            "conversion_type": "${form.goal || "signup"}"
        }
    )

    return response.json()`}</pre>
                                <Button
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => {
                                    const code = `# When a user converts on your platform
import requests
import json

def record_conversion(user_id, conversion_data):
    # Get the stored tracking parameters for this user
    tracking_params = get_user_tracking_params(user_id)
    ad_id = tracking_params.get('ad_id')
    click_id = tracking_params.get('click_id')

    # Call the AdMesh API to record the conversion
    response = requests.post(
        "https://api.admesh.io/conversion/log",
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer YOUR_API_KEY"
        },
        json={
            "offer_id": "${createdOfferId}",
            "ad_id": ad_id,
            "click_id": click_id,
            "conversion_value": conversion_data.get('value'),
            "conversion_type": "${form.goal || "signup"}"
        }
    )

    return response.json()`;
                                    navigator.clipboard
                                      .writeText(code)
                                      .then(() =>
                                        toast.success("Code copied!")
                                      );
                                  }}
                                >
                                  Copy Code
                                </Button>
                              </div>
                            </TabsContent>

                            <TabsContent value="java" className="mt-3">
                              <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                                <pre>{`// When a user converts on your platform
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.json.JSONObject;

public JSONObject recordConversion(String userId, JSONObject conversionData) throws Exception {
    // Get the stored tracking parameters for this user
    JSONObject trackingParams = getUserTrackingParams(userId);
    String adId = trackingParams.getString("ad_id");
    String clickId = trackingParams.getString("click_id");

    // Create the request body
    JSONObject requestBody = new JSONObject();
    requestBody.put("offer_id", "${createdOfferId}");
    requestBody.put("ad_id", adId);
    requestBody.put("click_id", clickId);
    requestBody.put("conversion_value", conversionData.getDouble("value"));
    requestBody.put("conversion_type", "${form.goal || "signup"}");

    // Call the AdMesh API to record the conversion
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create("https://api.useadmesh.com/conversion/log"))
        .header("Content-Type", "application/json")
        .header("Authorization", "Bearer YOUR_API_KEY")
        .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
        .build();

    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
    return new JSONObject(response.body());
}`}</pre>
                                <Button
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => {
                                    const code = `// When a user converts on your platform
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.json.JSONObject;

public JSONObject recordConversion(String userId, JSONObject conversionData) throws Exception {
    // Get the stored tracking parameters for this user
    JSONObject trackingParams = getUserTrackingParams(userId);
    String adId = trackingParams.getString("ad_id");
    String clickId = trackingParams.getString("click_id");

    // Create the request body
    JSONObject requestBody = new JSONObject();
    requestBody.put("offer_id", "${createdOfferId}");
    requestBody.put("ad_id", adId);
    requestBody.put("click_id", clickId);
    requestBody.put("conversion_value", conversionData.getDouble("value"));
    requestBody.put("conversion_type", "${form.goal || "signup"}");

    // Call the AdMesh API to record the conversion
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create("https://api.admesh.io/conversion/log"))
        .header("Content-Type", "application/json")
        .header("Authorization", "Bearer YOUR_API_KEY")
        .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
        .build();

    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
    return new JSONObject(response.body());
}`;
                                    navigator.clipboard
                                      .writeText(code)
                                      .then(() =>
                                        toast.success("Code copied!")
                                      );
                                  }}
                                >
                                  Copy Code
                                </Button>
                              </div>
                            </TabsContent>

                            <TabsContent value="prompt" className="mt-3">
                              <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                                <pre>{`I need to implement server-side conversion tracking for AdMesh in my application. Here are the details:

- Tracking Method: Server-side API
- API Endpoint: https://api.useadmesh.com/conversion/log
- Offer ID: ${createdOfferId}
- Conversion Type: ${form.goal || "signup"}
- Programming Language: [SPECIFY YOUR LANGUAGE: Node.js, Python, Java, PHP, Ruby, etc.]

When a user completes a conversion on our platform, we need to:
1. Retrieve the tracking parameters (ad_id and click_id) that were stored when the user initially clicked the affiliate link
2. Send these parameters along with the conversion data to the AdMesh API

Please write the code to implement this server-side conversion tracking. Include:
- A function to record conversions
- The API call with proper headers and payload
- Error handling
- Any necessary imports or dependencies`}</pre>
                                <Button
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => {
                                    const code = `I need to implement server-side conversion tracking for AdMesh in my application. Here are the details:

- Tracking Method: Server-side API
- API Endpoint: https://api.useadmesh.com/conversion/log
- Offer ID: ${createdOfferId}
- Conversion Type: ${form.goal || "signup"}
- Programming Language: [SPECIFY YOUR LANGUAGE: Node.js, Python, Java, PHP, Ruby, etc.]

When a user completes a conversion on our platform, we need to:
1. Retrieve the tracking parameters (ad_id and click_id) that were stored when the user initially clicked the affiliate link
2. Send these parameters along with the conversion data to the AdMesh API

Please write the code to implement this server-side conversion tracking. Include:
- A function to record conversions
- The API call with proper headers and payload
- Error handling
- Any necessary imports or dependencies`;
                                    navigator.clipboard
                                      .writeText(code)
                                      .then(() =>
                                        toast.success("Prompt copied!")
                                      );
                                  }}
                                >
                                  Copy Prompt
                                </Button>
                              </div>
                            </TabsContent>
                          </Tabs>
                        </div>
                      </div>
                    </div>
                  )}

                  {form.tracking_method === "manual" && (
                    <div className="space-y-4">
                      <div>
                        <Label
                          htmlFor="tracking_notes"
                          className="text-sm font-medium"
                        >
                          Tracking Notes
                        </Label>
                        <Textarea
                          id="tracking_notes"
                          name="tracking_notes"
                          value={form.tracking_notes}
                          onChange={handleChange}
                          placeholder="Describe how you'll track and verify conversions manually"
                          className="mt-1"
                          rows={3}
                          disabled={loading}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Provide details about your manual verification process
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between border-t p-6 bg-gray-50">
              <Button
                variant="outline"
                onClick={() => setActiveTab("details")}
                disabled={loading}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous: Offer Details
              </Button>
              <Button
                onClick={setupIntegration}
                disabled={loading}
                className="px-8"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Complete Setup"
                )}
              </Button>
            </CardFooter>
          </TabsContent>

          <TabsContent value="google-ads" className="mt-0 p-0">
            <CardContent className="pt-6 space-y-6">
              <div className="space-y-4">
                <Alert>
                  <ExternalLink className="h-4 w-4" />
                  <AlertTitle>Google Ads Integration</AlertTitle>
                  <AlertDescription>
                    Automatically create Google Ads campaigns from your offers with AI-powered optimization.
                    Connect your Google Ads account first to enable this feature.
                  </AlertDescription>
                </Alert>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="google-ads-enabled"
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="google-ads-enabled" className="text-sm font-medium">
                    Create Google Ads campaign for this offer
                  </Label>
                </div>

                <div className="pl-6 space-y-4 opacity-50">
                  <p className="text-sm text-muted-foreground">
                    Google Ads integration is coming soon! This will allow you to:
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-2 list-disc list-inside">
                    <li>Automatically create campaigns from your offers</li>
                    <li>AI-powered keyword suggestions and bid optimization</li>
                    <li>Real-time performance tracking and optimization</li>
                    <li>Automated budget allocation based on performance</li>
                  </ul>

                  <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <ExternalLink className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Ready to get started?</span>
                    </div>
                    <p className="text-sm text-blue-700 mb-3">
                      Connect your Google Ads account now to unlock powerful automation features.
                    </p>
                    <Button
                      size="sm"
                      onClick={() => window.open("/dashboard/brand/google-ads", "_blank")}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect Google Ads
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setActiveTab("tracking")}
                className="px-8"
              >
                ← Back to Tracking
              </Button>
              <Button
                onClick={setupIntegration}
                disabled={loading}
                className="px-8"
              >
                {loading ? (
                  <>
                    <div className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-white border-r-transparent mr-2"></div>
                    Processing...
                  </>
                ) : (
                  "Complete Setup"
                )}
              </Button>
            </CardFooter>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}

export default function Page() {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center h-screen">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent"></div>
        </div>
      }
    >
      <NewOfferPage />
    </Suspense>
  );
}
