// LICENSE is MIT
//
// Copyright (c) 2018
//   <PERSON> <http://www.keendevelopment.ch>
//   <PERSON><PERSON> <https://github.com/adidahiya>
//   <PERSON> <https://github.com/J<PERSON><PERSON><PERSON>>
//   <PERSON> <https://github.com/seansfkelley>
//   <PERSON><PERSON> <https://github.com/mradamczyk>
//   <PERSON> <https://github.com/marvinhagemeister>

declare namespace classNames {
  type Value = string | number | boolean | undefined | null;
  type Mapping = Record<string, any>;
  interface ArgumentArray extends Array<Argument> {}
  interface ReadonlyArgumentArray extends ReadonlyArray<Argument> {}
  type Argument = Value | Mapping | ArgumentArray | ReadonlyArgumentArray;
}

interface ClassNames {
	(...args: classNames.ArgumentArray): string;

	default: ClassNames;
}

/**
 * A simple JavaScript utility for conditionally joining classNames together.
 */
declare const classNames: ClassNames;

export as namespace classNames;

export = classNames;
