import { TrackingData, UseAdMeshTrackerReturn } from '../types/index';
interface TrackingConfig {
    apiBaseUrl?: string;
    enabled?: boolean;
    debug?: boolean;
    retryAttempts?: number;
    retryDelay?: number;
}
export declare const setAdMeshTrackerConfig: (config: Partial<TrackingConfig>) => void;
export declare const useAdMeshTracker: (config?: Partial<TrackingConfig>) => UseAdMeshTrackerReturn;
export declare const buildAdMeshLink: (baseLink: string, adId: string, additionalParams?: Record<string, string>) => string;
export declare const extractTrackingData: (recommendation: {
    ad_id: string;
    admesh_link: string;
    product_id: string;
}, additionalData?: Partial<TrackingData>) => TrackingData;
export {};
//# sourceMappingURL=useAdMeshTracker.d.ts.map