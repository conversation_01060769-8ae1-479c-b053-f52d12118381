import { default as React } from 'react';
import { AdMeshRecommendation, AdMeshTheme } from '../types';
export interface AdMeshExpandableUnitProps {
    /** Product recommendation data */
    recommendation: AdMeshRecommendation;
    /** Theme configuration */
    theme?: AdMeshTheme;
    /** Custom CSS class name */
    className?: string;
    /** Custom inline styles */
    style?: React.CSSProperties;
    /** Show "powered by AdMesh" branding */
    showPoweredBy?: boolean;
    /** Initial expanded state */
    initialExpanded?: boolean;
    /** Custom sections to display */
    sections?: {
        title: string;
        description: string;
        icon?: string;
    }[];
    /** Custom call-to-action text */
    ctaText?: string;
    /** Show collapse/expand functionality */
    collapsible?: boolean;
}
/**
 * AdMeshExpandableUnit - A comprehensive ad unit with expandable sections
 *
 * Similar to the Temporal ad format, this component displays:
 * - Header with product name and sponsor info
 * - Multiple expandable sections with descriptions
 * - Primary call-to-action button
 * - Optional powered by branding
 */
export declare const AdMeshExpandableUnit: React.FC<AdMeshExpandableUnitProps>;
export default AdMeshExpandableUnit;
//# sourceMappingURL=AdMeshExpandableUnit.d.ts.map