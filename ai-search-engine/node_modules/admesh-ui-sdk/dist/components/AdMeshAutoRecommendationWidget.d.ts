import { default as React } from 'react';
import { AdMeshRecommendation, AdMeshTheme } from '../types/index';
export interface AdMeshAutoRecommendationWidgetProps {
    recommendations: AdMeshRecommendation[];
    trigger?: string;
    theme?: AdMeshTheme;
    title?: string;
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    size?: 'sm' | 'md' | 'lg';
    autoShow?: boolean;
    showDelay?: number;
    onRecommendationClick?: (adId: string, admeshLink: string) => void;
    onDismiss?: () => void;
    className?: string;
}
export declare const AdMeshAutoRecommendationWidget: React.FC<AdMeshAutoRecommendationWidgetProps>;
//# sourceMappingURL=AdMeshAutoRecommendationWidget.d.ts.map