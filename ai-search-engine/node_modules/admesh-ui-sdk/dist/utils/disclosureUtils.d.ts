import { AdMeshRecommendation } from '../types/index';
/**
 * Utility functions for generating compliant disclosure labels and tooltips
 */
export interface DisclosureConfig {
    showTooltips?: boolean;
    compactMode?: boolean;
    customLabels?: {
        smartPick?: string;
        partnerMatch?: string;
        promotedOption?: string;
        relatedOption?: string;
    };
}
/**
 * Generate appropriate label based on match score and recommendation quality
 */
export declare const getRecommendationLabel: (recommendation: AdMeshRecommendation, config?: DisclosureConfig) => string;
/**
 * Generate tooltip text for recommendation labels
 */
export declare const getLabelTooltip: (recommendation: AdMeshRecommendation, _label: string) => string;
/**
 * Generate section-level disclosure text
 */
export declare const getSectionDisclosure: (hasHighMatches?: boolean, isExpanded?: boolean) => string;
/**
 * Generate inline disclosure text for product cards
 */
export declare const getInlineDisclosure: (recommendation: AdMeshRecommendation, compact?: boolean) => string;
/**
 * Generate detailed tooltip for inline disclosures
 */
export declare const getInlineTooltip: () => string;
/**
 * Generate badge text without emojis
 */
export declare const getBadgeText: (badgeType: string) => string;
/**
 * Generate appropriate CTA text
 */
export declare const getCtaText: (recommendation: AdMeshRecommendation, context?: "button" | "link") => string;
/**
 * Check if recommendations have high match scores
 */
export declare const hasHighQualityMatches: (recommendations: AdMeshRecommendation[]) => boolean;
/**
 * Generate compliant powered-by text
 */
export declare const getPoweredByText: (compact?: boolean) => string;
//# sourceMappingURL=disclosureUtils.d.ts.map