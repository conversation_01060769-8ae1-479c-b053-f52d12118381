import { AdMeshTheme } from '../types';
/**
 * Utility functions for theme customization in AdMesh UI SDK
 */
/**
 * Creates a theme with sensible defaults and custom overrides
 */
export declare const createAdMeshTheme: (customTheme?: Partial<AdMeshTheme>) => AdMeshTheme;
/**
 * Creates a dark theme variant
 */
export declare const createDarkTheme: (customTheme?: Partial<AdMeshTheme>) => AdMeshTheme;
/**
 * Predefined theme presets for common AI platforms
 */
export declare const themePresets: {
    minimal: AdMeshTheme;
    vibrant: AdMeshTheme;
    corporate: AdMeshTheme;
    highContrast: AdMeshTheme;
};
/**
 * Utility to merge multiple theme objects
 */
export declare const mergeThemes: (...themes: Partial<AdMeshTheme>[]) => AdMeshTheme;
/**
 * Utility to create a theme from CSS custom properties
 */
export declare const themeFromCSSProperties: (element: HTMLElement) => Partial<AdMeshTheme>;
//# sourceMappingURL=themeUtils.d.ts.map