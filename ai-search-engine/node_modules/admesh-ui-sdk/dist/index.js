"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const b=require("react");function Te(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var ee={exports:{}},Q={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var he;function Le(){if(he)return Q;he=1;var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(i,n,a){var d=null;if(a!==void 0&&(d=""+a),n.key!==void 0&&(d=""+n.key),"key"in n){a={};for(var c in n)c!=="key"&&(a[c]=n[c])}else a=n;return n=a.ref,{$$typeof:t,type:i,key:d,ref:n!==void 0?n:null,props:a}}return Q.Fragment=r,Q.jsx=s,Q.jsxs=s,Q}var X={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ue;function Ae(){return ue||(ue=1,process.env.NODE_ENV!=="production"&&function(){function t(l){if(l==null)return null;if(typeof l=="function")return l.$$typeof===P?null:l.displayName||l.name||null;if(typeof l=="string")return l;switch(l){case h:return"Fragment";case w:return"Profiler";case g:return"StrictMode";case S:return"Suspense";case A:return"SuspenseList";case E:return"Activity"}if(typeof l=="object")switch(typeof l.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),l.$$typeof){case v:return"Portal";case C:return(l.displayName||"Context")+".Provider";case f:return(l._context.displayName||"Context")+".Consumer";case T:var N=l.render;return l=l.displayName,l||(l=N.displayName||N.name||"",l=l!==""?"ForwardRef("+l+")":"ForwardRef"),l;case j:return N=l.displayName||null,N!==null?N:t(l.type)||"Memo";case L:N=l._payload,l=l._init;try{return t(l(N))}catch{}}return null}function r(l){return""+l}function s(l){try{r(l);var N=!1}catch{N=!0}if(N){N=console;var R=N.error,B=typeof Symbol=="function"&&Symbol.toStringTag&&l[Symbol.toStringTag]||l.constructor.name||"Object";return R.call(N,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",B),r(l)}}function i(l){if(l===h)return"<>";if(typeof l=="object"&&l!==null&&l.$$typeof===L)return"<...>";try{var N=t(l);return N?"<"+N+">":"<...>"}catch{return"<...>"}}function n(){var l=I.A;return l===null?null:l.getOwner()}function a(){return Error("react-stack-top-frame")}function d(l){if($.call(l,"key")){var N=Object.getOwnPropertyDescriptor(l,"key").get;if(N&&N.isReactWarning)return!1}return l.key!==void 0}function c(l,N){function R(){k||(k=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",N))}R.isReactWarning=!0,Object.defineProperty(l,"key",{get:R,configurable:!0})}function u(){var l=t(this.type);return z[l]||(z[l]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),l=this.props.ref,l!==void 0?l:null}function m(l,N,R,B,q,D,oe,ne){return R=D.ref,l={$$typeof:y,type:l,key:N,props:D,_owner:q},(R!==void 0?R:null)!==null?Object.defineProperty(l,"ref",{enumerable:!1,get:u}):Object.defineProperty(l,"ref",{enumerable:!1,value:null}),l._store={},Object.defineProperty(l._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(l,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(l,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:oe}),Object.defineProperty(l,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:ne}),Object.freeze&&(Object.freeze(l.props),Object.freeze(l)),l}function p(l,N,R,B,q,D,oe,ne){var W=N.children;if(W!==void 0)if(B)if(U(W)){for(B=0;B<W.length;B++)x(W[B]);Object.freeze&&Object.freeze(W)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else x(W);if($.call(N,"key")){W=t(l);var G=Object.keys(N).filter(function(Se){return Se!=="key"});B=0<G.length?"{key: someKey, "+G.join(": ..., ")+": ...}":"{key: someKey}",J[W+B]||(G=0<G.length?"{"+G.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,B,W,G,W),J[W+B]=!0)}if(W=null,R!==void 0&&(s(R),W=""+R),d(N)&&(s(N.key),W=""+N.key),"key"in N){R={};for(var le in N)le!=="key"&&(R[le]=N[le])}else R=N;return W&&c(R,typeof l=="function"?l.displayName||l.name||"Unknown":l),m(l,W,D,q,n(),R,oe,ne)}function x(l){typeof l=="object"&&l!==null&&l.$$typeof===y&&l._store&&(l._store.validated=1)}var o=b,y=Symbol.for("react.transitional.element"),v=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),C=Symbol.for("react.context"),T=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),A=Symbol.for("react.suspense_list"),j=Symbol.for("react.memo"),L=Symbol.for("react.lazy"),E=Symbol.for("react.activity"),P=Symbol.for("react.client.reference"),I=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=Object.prototype.hasOwnProperty,U=Array.isArray,_=console.createTask?console.createTask:function(){return null};o={"react-stack-bottom-frame":function(l){return l()}};var k,z={},F=o["react-stack-bottom-frame"].bind(o,a)(),K=_(i(a)),J={};X.Fragment=h,X.jsx=function(l,N,R,B,q){var D=1e4>I.recentlyCreatedOwnerStacks++;return p(l,N,R,!1,B,q,D?Error("react-stack-top-frame"):F,D?_(i(l)):K)},X.jsxs=function(l,N,R,B,q){var D=1e4>I.recentlyCreatedOwnerStacks++;return p(l,N,R,!0,B,q,D?Error("react-stack-top-frame"):F,D?_(i(l)):K)}}()),X}var pe;function Re(){return pe||(pe=1,process.env.NODE_ENV==="production"?ee.exports=Le():ee.exports=Ae()),ee.exports}var e=Re(),ie={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var ge;function Ee(){return ge||(ge=1,function(t){(function(){var r={}.hasOwnProperty;function s(){for(var a="",d=0;d<arguments.length;d++){var c=arguments[d];c&&(a=n(a,i(c)))}return a}function i(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return s.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var d="";for(var c in a)r.call(a,c)&&a[c]&&(d=n(d,c));return d}function n(a,d){return d?a?a+" "+d:a+d:a}t.exports?(s.default=s,t.exports=s):window.classNames=s})()}(ie)),ie.exports}var Ie=Ee();const M=Te(Ie),Pe="https://api.useadmesh.com/track";let ce={apiBaseUrl:Pe,enabled:!0,debug:!1,retryAttempts:3,retryDelay:1e3};const ze=t=>{ce={...ce,...t}},fe=t=>{const[r,s]=b.useState(!1),[i,n]=b.useState(null),a=b.useMemo(()=>({...ce,...t}),[t]),d=b.useCallback((x,o)=>{a.debug&&console.log(`[AdMesh Tracker] ${x}`,o)},[a.debug]),c=b.useCallback(async(x,o)=>{if(!a.enabled){d("Tracking disabled, skipping event",{eventType:x,data:o});return}if(!o.adId||!o.admeshLink){const g="Missing required tracking data: adId and admeshLink are required";d(g,o),n(g);return}s(!0),n(null);const y={event_type:x,ad_id:o.adId,admesh_link:o.admeshLink,product_id:o.productId,user_id:o.userId,session_id:o.sessionId,revenue:o.revenue,conversion_type:o.conversionType,metadata:o.metadata,timestamp:new Date().toISOString(),user_agent:navigator.userAgent,referrer:document.referrer,page_url:window.location.href};d(`Sending ${x} event`,y);let v=null;for(let g=1;g<=(a.retryAttempts||3);g++)try{const w=await fetch(`${a.apiBaseUrl}/events`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)});if(!w.ok)throw new Error(`HTTP ${w.status}: ${w.statusText}`);const f=await w.json();d(`${x} event tracked successfully`,f),s(!1);return}catch(w){v=w,d(`Attempt ${g} failed for ${x} event`,w),g<(a.retryAttempts||3)&&await new Promise(f=>setTimeout(f,(a.retryDelay||1e3)*g))}const h=`Failed to track ${x} event after ${a.retryAttempts} attempts: ${v==null?void 0:v.message}`;d(h,v),n(h),s(!1)},[a,d]),u=b.useCallback(async x=>c("click",x),[c]),m=b.useCallback(async x=>c("view",x),[c]),p=b.useCallback(async x=>(!x.revenue&&!x.conversionType&&d("Warning: Conversion tracking without revenue or conversion type",x),c("conversion",x)),[c,d]);return{trackClick:u,trackView:m,trackConversion:p,isTracking:r,error:i}},Be=(t,r,s)=>{try{const i=new URL(t);return i.searchParams.set("ad_id",r),i.searchParams.set("utm_source","admesh"),i.searchParams.set("utm_medium","recommendation"),s&&Object.entries(s).forEach(([n,a])=>{i.searchParams.set(n,a)}),i.toString()}catch(i){return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:",t,i),t}},We=(t,r)=>({adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,...r}),O=({adId:t,admeshLink:r,productId:s,children:i,trackingData:n,className:a,style:d})=>{const{trackClick:c,trackView:u}=fe(),m=b.useRef(null),p=b.useRef(!1);b.useEffect(()=>{if(!m.current||p.current)return;const o=new IntersectionObserver(y=>{y.forEach(v=>{v.isIntersecting&&!p.current&&(p.current=!0,u({adId:t,admeshLink:r,productId:s,...n}).catch(console.error))})},{threshold:.5,rootMargin:"0px"});return o.observe(m.current),()=>{o.disconnect()}},[t,r,s,n,u]);const x=b.useCallback(async o=>{try{await c({adId:t,admeshLink:r,productId:s,...n})}catch(h){console.error("Failed to track click:",h)}o.target.closest("a")||window.open(r,"_blank","noopener,noreferrer")},[t,r,s,n,c]);return e.jsx("div",{ref:m,className:a,onClick:x,style:{cursor:"pointer",...d},children:i})};O.displayName="AdMeshLinkTracker";const Fe=`
/* AdMesh UI SDK - Complete Self-Contained Styles */

/* CSS Reset for AdMesh components */
.admesh-component, .admesh-component * {
  box-sizing: border-box;
}

/* CSS Variables */
.admesh-component {
  --admesh-primary: #6366f1;
  --admesh-primary-hover: #4f46e5;
  --admesh-secondary: #8b5cf6;
  --admesh-accent: #06b6d4;
  --admesh-background: #ffffff;
  --admesh-surface: #ffffff;
  --admesh-border: #e2e8f0;
  --admesh-text: #0f172a;
  --admesh-text-muted: #64748b;
  --admesh-text-light: #94a3b8;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --admesh-radius: 0.75rem;
  --admesh-radius-sm: 0.375rem;
  --admesh-radius-lg: 1rem;
  --admesh-radius-xl: 1.5rem;
}

.admesh-component[data-admesh-theme="dark"] {
  --admesh-background: #111827;
  --admesh-surface: #1f2937;
  --admesh-border: #374151;
  --admesh-text: #f9fafb;
  --admesh-text-muted: #9ca3af;
  --admesh-text-light: #6b7280;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Layout Styles */
.admesh-layout {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--admesh-text);
  background-color: var(--admesh-background);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  box-shadow: var(--admesh-shadow);
  border: 1px solid var(--admesh-border);
}

.admesh-layout__header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.admesh-layout__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
}

.admesh-layout__subtitle {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

.admesh-layout__cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admesh-layout__more-indicator {
  text-align: center;
  padding: 1rem;
  color: var(--admesh-text-muted);
  font-size: 0.875rem;
}

.admesh-layout__empty {
  text-align: center;
  padding: 3rem 1rem;
}

.admesh-layout__empty-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text-muted);
  margin-bottom: 0.5rem;
}

.admesh-layout__empty-content p {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

/* Product Card Styles */
.admesh-product-card {
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.admesh-product-card:hover {
  box-shadow: var(--admesh-shadow-lg);
  transform: translateY(-2px);
  border-color: var(--admesh-primary);
}

.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.admesh-product-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.admesh-product-card__reason {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.admesh-product-card__match-score {
  margin-bottom: 1rem;
}

.admesh-product-card__match-score-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--admesh-text-muted);
  margin-bottom: 0.25rem;
}

.admesh-product-card__match-score-bar {
  width: 100%;
  height: 0.375rem;
  background-color: var(--admesh-border);
  border-radius: var(--admesh-radius-sm);
  overflow: hidden;
}

.admesh-product-card__match-score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);
  border-radius: var(--admesh-radius-sm);
  transition: width 0.3s ease-in-out;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admesh-product-card__badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--admesh-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--admesh-radius-sm);
}

.admesh-product-card__badge--secondary {
  background-color: var(--admesh-secondary);
}

.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.admesh-product-card__keyword {
  padding: 0.125rem 0.375rem;
  background-color: var(--admesh-border);
  color: var(--admesh-text-muted);
  font-size: 0.75rem;
  border-radius: var(--admesh-radius-sm);
}

/* Dark mode specific enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-product-card__keyword {
  background-color: #4b5563;
  color: #d1d5db;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card:hover {
  border-color: var(--admesh-primary);
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card__button:hover {
  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));
}

.admesh-product-card__footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

/* Mobile-specific sidebar improvements */
@media (max-width: 640px) {
  .admesh-sidebar {
    /* Ensure proper mobile viewport handling */
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */
    max-height: 100vh !important;
    max-height: 100dvh !important;
    width: 100vw !important;
    max-width: 90vw !important;
    overflow: hidden !important;
  }

  .admesh-sidebar.relative {
    height: 100% !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Improve touch scrolling */
  .admesh-sidebar .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
  }

  /* Prevent body scroll when sidebar is open */
  body:has(.admesh-sidebar[data-mobile-open="true"]) {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }
}

/* Tablet improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .admesh-sidebar {
    max-width: 400px !important;
  }
}

/* Mobile responsiveness improvements for all components */
@media (max-width: 640px) {
  /* Product cards mobile optimization */
  .admesh-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Inline recommendations mobile optimization */
  .admesh-inline-recommendation {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Conversation summary mobile optimization */
  .admesh-conversation-summary {
    padding: 1rem !important;
  }

  /* Percentage text mobile improvements */
  .admesh-component .text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
  }

  .admesh-component .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }

  /* Button mobile improvements */
  .admesh-component button {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    min-height: 2rem !important;
    touch-action: manipulation !important;
  }

  /* Badge mobile improvements */
  .admesh-component .rounded-full {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
    line-height: 1rem !important;
  }

  /* Progress bar mobile improvements */
  .admesh-component .bg-gray-200,
  .admesh-component .bg-slate-600 {
    height: 0.25rem !important;
  }

  /* Flex layout mobile improvements */
  .admesh-component .flex {
    flex-wrap: wrap !important;
  }

  .admesh-component .gap-2 {
    gap: 0.375rem !important;
  }

  .admesh-component .gap-3 {
    gap: 0.5rem !important;
  }
}

.admesh-product-card__button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: var(--admesh-radius);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.admesh-product-card__button:hover {
  transform: translateY(-1px);
  box-shadow: var(--admesh-shadow-lg);
}

/* Utility Classes */
.admesh-text-xs { font-size: 0.75rem; }
.admesh-text-sm { font-size: 0.875rem; }
.admesh-text-base { font-size: 1rem; }
.admesh-text-lg { font-size: 1.125rem; }
.admesh-text-xl { font-size: 1.25rem; }

.admesh-font-medium { font-weight: 500; }
.admesh-font-semibold { font-weight: 600; }
.admesh-font-bold { font-weight: 700; }

.admesh-text-muted { color: var(--admesh-text-muted); }

/* Comparison Table Styles */
.admesh-compare-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  overflow: hidden;
}

.admesh-compare-table th,
.admesh-compare-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admesh-border);
}

.admesh-compare-table th {
  background-color: var(--admesh-background);
  font-weight: 600;
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table td {
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table tr:hover {
  background-color: var(--admesh-border);
}

/* Dark mode table enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-compare-table th {
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-compare-table tr:hover {
  background-color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admesh-layout {
    padding: 1rem;
  }

  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admesh-product-card {
    padding: 1rem;
  }

  .admesh-compare-table {
    font-size: 0.75rem;
  }

  .admesh-compare-table th,
  .admesh-compare-table td {
    padding: 0.5rem;
  }
}

/* Essential Utility Classes for Self-Contained SDK - High Specificity */
.admesh-component .relative { position: relative !important; }
.admesh-component .absolute { position: absolute !important; }
.admesh-component .flex { display: flex !important; }
.admesh-component .inline-flex { display: inline-flex !important; }
.admesh-component .grid { display: grid !important; }
.admesh-component .hidden { display: none !important; }
.admesh-component .block { display: block !important; }
.admesh-component .inline-block { display: inline-block !important; }

/* Flexbox utilities */
.admesh-component .flex-col { flex-direction: column !important; }
.admesh-component .flex-row { flex-direction: row !important; }
.admesh-component .flex-wrap { flex-wrap: wrap !important; }
.admesh-component .items-center { align-items: center !important; }
.admesh-component .items-start { align-items: flex-start !important; }
.admesh-component .items-end { align-items: flex-end !important; }
.admesh-component .justify-center { justify-content: center !important; }
.admesh-component .justify-between { justify-content: space-between !important; }
.admesh-component .justify-end { justify-content: flex-end !important; }
.admesh-component .flex-1 { flex: 1 1 0% !important; }
.admesh-component .flex-shrink-0 { flex-shrink: 0 !important; }

/* Grid utilities */
.admesh-component .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.admesh-component .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.admesh-component .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Spacing utilities */
.admesh-component .gap-1 { gap: 0.25rem; }
.admesh-component .gap-2 { gap: 0.5rem; }
.admesh-component .gap-3 { gap: 0.75rem; }
.admesh-component .gap-4 { gap: 1rem; }
.admesh-component .gap-6 { gap: 1.5rem; }
.admesh-component .gap-8 { gap: 2rem; }

/* Padding utilities */
.admesh-component .p-1 { padding: 0.25rem; }
.admesh-component .p-2 { padding: 0.5rem; }
.admesh-component .p-3 { padding: 0.75rem; }
.admesh-component .p-4 { padding: 1rem; }
.admesh-component .p-5 { padding: 1.25rem; }
.admesh-component .p-6 { padding: 1.5rem; }
.admesh-component .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.admesh-component .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.admesh-component .px-4 { padding-left: 1rem; padding-right: 1rem; }
.admesh-component .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.admesh-component .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.admesh-component .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.admesh-component .pt-2 { padding-top: 0.5rem; }
.admesh-component .pt-3 { padding-top: 0.75rem; }
.admesh-component .pb-2 { padding-bottom: 0.5rem; }
.admesh-component .pb-3 { padding-bottom: 0.75rem; }

/* Margin utilities */
.admesh-component .m-0 { margin: 0; }
.admesh-component .mb-1 { margin-bottom: 0.25rem; }
.admesh-component .mb-2 { margin-bottom: 0.5rem; }
.admesh-component .mb-3 { margin-bottom: 0.75rem; }
.admesh-component .mb-4 { margin-bottom: 1rem; }
.admesh-component .mb-6 { margin-bottom: 1.5rem; }
.admesh-component .mt-1 { margin-top: 0.25rem; }
.admesh-component .mt-2 { margin-top: 0.5rem; }
.admesh-component .mt-4 { margin-top: 1rem; }
.admesh-component .mt-6 { margin-top: 1.5rem; }
.admesh-component .mt-auto { margin-top: auto; }
.admesh-component .ml-1 { margin-left: 0.25rem; }
.admesh-component .mr-1 { margin-right: 0.25rem; }
.admesh-component .mr-2 { margin-right: 0.5rem; }

/* Width and height utilities */
.admesh-component .w-2 { width: 0.5rem; }
.admesh-component .w-3 { width: 0.75rem; }
.admesh-component .w-4 { width: 1rem; }
.admesh-component .w-5 { width: 1.25rem; }
.admesh-component .w-6 { width: 1.5rem; }
.admesh-component .w-full { width: 100%; }
.admesh-component .w-fit { width: fit-content; }
.admesh-component .h-2 { height: 0.5rem; }
.admesh-component .h-3 { height: 0.75rem; }
.admesh-component .h-4 { height: 1rem; }
.admesh-component .h-5 { height: 1.25rem; }
.admesh-component .h-6 { height: 1.5rem; }
.admesh-component .h-full { height: 100%; }
.admesh-component .min-w-0 { min-width: 0px; }

/* Border utilities */
.admesh-component .border { border-width: 1px; }
.admesh-component .border-t { border-top-width: 1px; }
.admesh-component .border-gray-100 { border-color: #f3f4f6; }
.admesh-component .border-gray-200 { border-color: #e5e7eb; }
.admesh-component .border-gray-300 { border-color: #d1d5db; }
.admesh-component .border-blue-200 { border-color: #bfdbfe; }
.admesh-component .border-green-200 { border-color: #bbf7d0; }

/* Border radius utilities */
.admesh-component .rounded { border-radius: 0.25rem !important; }
.admesh-component .rounded-md { border-radius: 0.375rem !important; }
.admesh-component .rounded-lg { border-radius: 0.5rem !important; }
.admesh-component .rounded-xl { border-radius: 0.75rem !important; }
.admesh-component .rounded-full { border-radius: 9999px !important; }

/* Background utilities */
.admesh-component .bg-white { background-color: #ffffff; }
.admesh-component .bg-gray-50 { background-color: #f9fafb; }
.admesh-component .bg-gray-100 { background-color: #f3f4f6; }
.admesh-component .bg-blue-50 { background-color: #eff6ff; }
.admesh-component .bg-blue-100 { background-color: #dbeafe; }
.admesh-component .bg-green-100 { background-color: #dcfce7; }
.admesh-component .bg-green-500 { background-color: #22c55e; }
.admesh-component .bg-blue-500 { background-color: #3b82f6; }

/* Gradients */
.admesh-component .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.admesh-component .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.admesh-component .from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
.admesh-component .to-gray-50 { --tw-gradient-to: #f9fafb; }
.admesh-component .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0)); }
.admesh-component .to-pink-500 { --tw-gradient-to: #ec4899; }
.admesh-component .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }
.admesh-component .to-blue-500 { --tw-gradient-to: #3b82f6; }

/* Text utilities */
.admesh-component .text-xs { font-size: 0.75rem; line-height: 1rem; }
.admesh-component .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.admesh-component .text-base { font-size: 1rem; line-height: 1.5rem; }
.admesh-component .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.admesh-component .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.admesh-component .font-medium { font-weight: 500; }
.admesh-component .font-semibold { font-weight: 600; }
.admesh-component .font-bold { font-weight: 700; }
.admesh-component .leading-relaxed { line-height: 1.625; }

/* Text colors */
.admesh-component .text-white { color: #ffffff; }
.admesh-component .text-gray-400 { color: #9ca3af; }
.admesh-component .text-gray-500 { color: #6b7280; }
.admesh-component .text-gray-600 { color: #4b5563; }
.admesh-component .text-gray-700 { color: #374151; }
.admesh-component .text-gray-800 { color: #1f2937; }
.admesh-component .text-blue-600 { color: #2563eb; }
.admesh-component .text-blue-700 { color: #1d4ed8; }
.admesh-component .text-green-700 { color: #15803d; }

/* Shadow utilities */
.admesh-component .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.admesh-component .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }

/* Transition utilities */
.admesh-component .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.admesh-component .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.admesh-component .duration-200 { transition-duration: 200ms; }
.admesh-component .duration-300 { transition-duration: 300ms; }

/* Transform utilities */
.admesh-component .hover\\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.admesh-component .hover\\:scale-105:hover { transform: scale(1.05); }

/* Hover utilities */
.admesh-component .hover\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.admesh-component .hover\\:bg-gray-100:hover { background-color: #f3f4f6; }
.admesh-component .hover\\:text-blue-800:hover { color: #1e40af; }

/* Cursor utilities */
.admesh-component .cursor-pointer { cursor: pointer; }

/* Overflow utilities */
.admesh-component .overflow-hidden { overflow: hidden; }
.admesh-component .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

/* Text decoration */
.admesh-component .underline { text-decoration-line: underline; }

/* Whitespace */
.admesh-component .whitespace-nowrap { white-space: nowrap; }

/* Dark mode utilities */
@media (prefers-color-scheme: dark) {
  .admesh-component .dark\\:bg-slate-800 { background-color: #1e293b; }
  .admesh-component .dark\\:bg-slate-900 { background-color: #0f172a; }
  .admesh-component .dark\\:border-slate-700 { border-color: #334155; }
  .admesh-component .dark\\:text-white { color: #ffffff; }
  .admesh-component .dark\\:text-gray-200 { color: #e5e7eb; }
  .admesh-component .dark\\:text-gray-300 { color: #d1d5db; }
  .admesh-component .dark\\:text-gray-400 { color: #9ca3af; }
  .admesh-component .dark\\:text-blue-400 { color: #60a5fa; }
}

/* Responsive utilities */
@media (min-width: 640px) {
  .admesh-component .sm\\:p-5 { padding: 1.25rem; }
  .admesh-component .sm\\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .admesh-component .sm\\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .admesh-component .sm\\:flex-row { flex-direction: row; }
  .admesh-component .sm\\:items-center { align-items: center; }
  .admesh-component .sm\\:justify-between { justify-content: space-between; }
}

@media (min-width: 768px) {
  .admesh-component .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .admesh-component .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .admesh-component .lg\\:col-span-1 { grid-column: span 1 / span 1; }
}
`;let de=!1;const re=()=>{b.useEffect(()=>{if(de)return;const t=document.createElement("style");return t.id="admesh-ui-sdk-styles",t.textContent=Fe,document.getElementById("admesh-ui-sdk-styles")||(document.head.appendChild(t),de=!0),()=>{const r=document.getElementById("admesh-ui-sdk-styles");r&&document.head.contains(r)&&(document.head.removeChild(r),de=!1)}},[])},H=(t,r={})=>{const s=t.intent_match_score||0,i=r.customLabels||{};return s>=.8?i.smartPick||"Smart Pick":s>=.6?i.partnerMatch||"Partner Match":s>=.3?i.promotedOption||"Promoted Option":i.relatedOption||"Related Option"},Z=(t,r)=>{const s=t.intent_match_score||0;return s>=.8?"This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query.":s>=.6?"Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals.":s>=.3?"This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions.":"This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals."},Oe=(t=!0,r=!1)=>t?r?"These curated recommendations are from partners who compensate us for referrals.":"Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs.":"Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.",te=(t,r=!1)=>{const s=t.intent_match_score||0;return r?"Promoted":s>=.8?"Smart Recommendation":s>=.6?"Partner Match":"Promoted"},se=()=>"We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.",be=t=>({"Top Match":"Top Match","Smart Pick":"Smart Pick","Perfect Fit":"Perfect Fit","Great Match":"Great Match",Recommended:"Recommended","Good Fit":"Good Fit",Featured:"Featured","Popular Choice":"Popular Choice","Premium Pick":"Premium Pick","Free Tier":"Free Tier","AI Powered":"AI Powered",Popular:"Popular",New:"New","Trial Available":"Trial Available","Related Option":"Related Option","Alternative Solution":"Alternative Solution","Expanded Match":"Expanded Match"})[t]||t,$e=(t,r="button")=>{const s=t.recommendation_title||t.title;return r==="link"?s:t.trial_days&&t.trial_days>0?`Try ${s}`:"Learn More"},De=t=>t.some(r=>(r.intent_match_score||0)>=.8),Ve=(t=!1)=>t?"Powered by AdMesh":"Recommendations powered by AdMesh",ae=({recommendation:t,theme:r,showMatchScore:s=!1,showBadges:i=!0,variation:n="default",expandable:a=!1,className:d,style:c})=>{var f,C,T,S,A,j,L,E,P,I,$,U,_;re();const[u,m]=b.useState(!1),p=b.useMemo(()=>{var J;const k=[];H(t)==="Smart Pick"&&k.push("Top Match"),t.trial_days&&t.trial_days>0&&k.push("Trial Available");const F=["ai","artificial intelligence","machine learning","ml","automation"];return(((J=t.keywords)==null?void 0:J.some(l=>F.some(N=>l.toLowerCase().includes(N))))||t.title.toLowerCase().includes("ai"))&&k.push("AI Powered"),t.badges&&t.badges.length>0&&t.badges.forEach(l=>{["Top Match","Free Tier","AI Powered","Popular","New","Trial Available"].includes(l)&&!k.includes(l)&&k.push(l)}),k},[t]),x=te(t,!1),o=se(),y=Math.round(t.intent_match_score*100),h=(()=>{const k=t.content_variations;return n==="simple"?{title:t.recommendation_title||t.title,description:t.recommendation_description||t.description||t.reason,ctaText:t.recommendation_title||t.title,isSimple:!0}:n==="question"&&(k!=null&&k.question)?{title:k.question.cta||t.recommendation_title||t.title,description:k.question.text,ctaText:k.question.cta||t.recommendation_title||t.title}:n==="statement"&&(k!=null&&k.statement)?{title:t.recommendation_title||t.title,description:k.statement.text,ctaText:k.statement.cta||t.recommendation_title||t.title}:{title:t.recommendation_title||t.title,description:t.recommendation_description||t.description||t.reason,ctaText:t.recommendation_title||t.title}})(),g=M("admesh-component","admesh-card","relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1",d),w=r?{"--admesh-primary":r.primaryColor||r.accentColor||"#3b82f6","--admesh-secondary":r.secondaryColor||"#10b981","--admesh-accent":r.accentColor||"#3b82f6","--admesh-background":r.backgroundColor,"--admesh-surface":r.surfaceColor,"--admesh-border":r.borderColor,"--admesh-text":r.textColor,"--admesh-text-secondary":r.textSecondaryColor,"--admesh-radius":r.borderRadius||"12px","--admesh-shadow-sm":(f=r.shadows)==null?void 0:f.small,"--admesh-shadow-md":(C=r.shadows)==null?void 0:C.medium,"--admesh-shadow-lg":(T=r.shadows)==null?void 0:T.large,"--admesh-spacing-sm":(S=r.spacing)==null?void 0:S.small,"--admesh-spacing-md":(A=r.spacing)==null?void 0:A.medium,"--admesh-spacing-lg":(j=r.spacing)==null?void 0:j.large,"--admesh-font-size-sm":(L=r.fontSize)==null?void 0:L.small,"--admesh-font-size-base":(E=r.fontSize)==null?void 0:E.base,"--admesh-font-size-lg":(P=r.fontSize)==null?void 0:P.large,"--admesh-font-size-title":(I=r.fontSize)==null?void 0:I.title,fontFamily:r.fontFamily}:void 0;return n==="simple"?e.jsxs("div",{className:M("admesh-component admesh-simple-ad","inline-block text-sm leading-relaxed",d),style:{fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...($=r==null?void 0:r.components)==null?void 0:$.productCard,...c},"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsx("span",{style:{fontSize:"11px",fontWeight:"600",color:(r==null?void 0:r.accentColor)||"#2563eb",backgroundColor:(r==null?void 0:r.mode)==="dark"?"#374151":"#f3f4f6",padding:"2px 6px",borderRadius:"4px",marginRight:"8px"},title:Z(t,H(t)),children:H(t)}),e.jsxs("span",{style:{color:(r==null?void 0:r.mode)==="dark"?"#f3f4f6":"#374151",marginRight:"4px"},children:[h.description," "]}),e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score,component:"simple_ad_cta"},children:e.jsx("span",{style:{color:(r==null?void 0:r.accentColor)||"#2563eb",textDecoration:"underline",cursor:"pointer",fontSize:"inherit",fontFamily:"inherit"},children:h.ctaText})}),e.jsxs("span",{style:{fontSize:"10px",color:(r==null?void 0:r.mode)==="dark"?"#9ca3af":"#6b7280",marginLeft:"8px"},title:o,children:["(",x,")"]})]}):n==="question"||n==="statement"?e.jsx("div",{className:M("admesh-component admesh-expandable-variation transition-all duration-300",a&&u?"p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg":"p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md",d),style:{fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(U=r==null?void 0:r.components)==null?void 0:U.productCard,...c},"data-admesh-theme":r==null?void 0:r.mode,children:!a||!u?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-2",children:e.jsx("span",{style:{fontSize:"11px",fontWeight:"600",color:(r==null?void 0:r.accentColor)||"#2563eb",backgroundColor:(r==null?void 0:r.mode)==="dark"?"#374151":"#f3f4f6",padding:"2px 6px",borderRadius:"4px"},title:Z(t,H(t)),children:H(t)})}),e.jsxs("div",{className:"flex items-center justify-between gap-3",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed",children:[h.description," ",e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score,component:"simple_variation_cta"},children:e.jsx("span",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors",children:h.ctaText})})]})}),a&&e.jsx("div",{className:"flex items-center gap-3 flex-shrink-0",children:e.jsxs("button",{onClick:()=>m(!0),className:"flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600",title:"View more details",children:[e.jsx("span",{children:"More Details"}),e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})]})})]})]}):e.jsxs("div",{className:"h-full flex flex-col",style:w,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0",children:[i&&p.includes("Top Match")&&e.jsx("span",{className:"text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md",style:{backgroundColor:(r==null?void 0:r.primaryColor)||(r==null?void 0:r.accentColor)||"#f59e0b",borderRadius:(r==null?void 0:r.borderRadius)||"9999px"},title:Z(t),children:be("Top Match")}),e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[t.product_logo&&e.jsx("img",{src:t.product_logo.url,alt:`${t.title} logo`,className:"w-6 h-6 rounded flex-shrink-0",onError:k=>{k.target.style.display="none"}}),e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate",children:h.title})]})]}),e.jsxs("div",{className:"flex gap-3 flex-shrink-0",children:[e.jsxs("button",{onClick:()=>m(!1),className:"flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600",title:"Show less details",children:[e.jsx("span",{children:"Less Details"}),e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})]}),e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score,component:"product_card_cta"},children:e.jsxs("button",{className:"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg",children:[n==="question"?"Try":"Visit"," ",h.ctaText,e.jsx("svg",{className:"ml-1 h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 leading-relaxed",children:h.description})}),e.jsx("div",{className:"mb-6",children:e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 leading-relaxed",title:o,children:x})}),s&&typeof t.intent_match_score=="number"&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2",children:[e.jsx("span",{className:"font-medium",children:"Match Score"}),e.jsxs("span",{className:"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap",children:[y,"% match"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",style:{width:`${y}%`}})})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 text-xs mb-3",children:[t.pricing&&e.jsxs("span",{className:"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),t.pricing]}),t.trial_days&&t.trial_days>0&&e.jsxs("span",{className:"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"})}),t.trial_days,"-day trial"]})]}),t.features&&t.features.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",children:"✨ Key Features"}),e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[t.features.slice(0,4).map((k,z)=>e.jsxs("span",{className:"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-indigo-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),k]},z)),t.features.length>4&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 px-2 py-1",children:["+",t.features.length-4," more"]})]})]}),t.integrations&&t.integrations.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",children:"🔗 Integrations"}),e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[t.integrations.slice(0,3).map((k,z)=>e.jsxs("span",{className:"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),k]},z)),t.integrations.length>3&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 px-2 py-1",children:["+",t.integrations.length-3," more"]})]})]}),e.jsx("div",{className:"flex justify-end mt-auto pt-2",children:e.jsx("span",{className:"text-xs text-gray-400 dark:text-gray-500",children:"Powered by AdMesh"})})]})}):e.jsx("div",{className:g,style:{fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(_=r==null?void 0:r.components)==null?void 0:_.productCard,...c},"data-admesh-theme":r==null?void 0:r.mode,children:e.jsxs("div",{className:"h-full flex flex-col",style:w,children:[e.jsx("div",{className:"mb-3",children:e.jsx("span",{style:{fontSize:"11px",fontWeight:"600",color:(r==null?void 0:r.accentColor)||"#2563eb",backgroundColor:(r==null?void 0:r.mode)==="dark"?"#374151":"#f3f4f6",padding:"2px 6px",borderRadius:"4px"},title:Z(t,H(t)),children:H(t)})}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[t.product_logo&&e.jsx("img",{src:t.product_logo.url,alt:`${t.title} logo`,className:"w-6 h-6 rounded flex-shrink-0",onError:k=>{k.target.style.display="none"}}),e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate",children:h.title})]}),e.jsx("div",{className:"flex gap-2 flex-shrink-0",children:e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score,component:"product_card_cta"},children:e.jsxs("button",{className:"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg",children:["Visit ",h.ctaText,e.jsx("svg",{className:"ml-1 h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})})]}),e.jsx("div",{className:"mb-6",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 leading-relaxed",children:h.description})}),s&&typeof t.intent_match_score=="number"&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2",children:[e.jsx("span",{className:"font-medium",children:"Match Score"}),e.jsxs("span",{className:"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap",children:[y,"% match"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",style:{width:`${y}%`}})})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 text-xs mb-3",children:[t.pricing&&e.jsxs("span",{className:"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),t.pricing]}),t.trial_days&&t.trial_days>0&&e.jsxs("span",{className:"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"})}),t.trial_days,"-day trial"]})]}),t.features&&t.features.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",children:"✨ Key Features"}),e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[t.features.slice(0,4).map((k,z)=>e.jsxs("span",{className:"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-indigo-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),k]},z)),t.features.length>4&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 px-2 py-1",children:["+",t.features.length-4," more"]})]})]}),t.integrations&&t.integrations.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",children:"🔗 Integrations"}),e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[t.integrations.slice(0,3).map((k,z)=>e.jsxs("span",{className:"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),k]},z)),t.integrations.length>3&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 px-2 py-1",children:["+",t.integrations.length-3," more"]})]})]}),e.jsx("div",{className:"mt-auto pt-3 border-t border-gray-100 dark:border-slate-700",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{title:o,children:x}),e.jsx("span",{className:"text-gray-400 dark:text-gray-500",children:"Powered by AdMesh"})]})})]})})};ae.displayName="AdMeshProductCard";const ye=({recommendations:t,theme:r,maxProducts:s=3,showMatchScores:i=!0,showFeatures:n=!0,className:a,style:d})=>{var p,x;re();const c=b.useMemo(()=>t.slice(0,s),[t,s]),u=M("admesh-component","admesh-compare-layout",a),m=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0;return c.length===0?e.jsx("div",{className:u,style:{...m,fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(p=r==null?void 0:r.components)==null?void 0:p.compareTable,...d},"data-admesh-theme":r==null?void 0:r.mode,children:e.jsx("div",{className:"p-8 text-center text-gray-500 dark:text-gray-400",children:e.jsx("p",{children:"No products to compare"})})}):e.jsx("div",{className:u,style:{...m,fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(x=r==null?void 0:r.components)==null?void 0:x.compareTable,...d},"data-admesh-theme":r==null?void 0:r.mode,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[e.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200",children:"Smart Comparison"})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[c.length," intelligent matches found"]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:c.map((o,y)=>e.jsxs("div",{className:"relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[y===0&&e.jsx("span",{className:"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full",children:"Top Match"}),e.jsxs("span",{className:"text-xs text-gray-400 dark:text-gray-500",children:["#",y+1]})]}),i&&e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap",children:[Math.round(o.intent_match_score*100),"% match"]})]}),e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-2",children:o.title}),i&&e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx("span",{children:"Match Score"}),e.jsxs("span",{className:"whitespace-nowrap",children:[Math.round(o.intent_match_score*100),"% match"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden",children:e.jsx("div",{className:"bg-black h-1.5",style:{width:`${Math.round(o.intent_match_score*100)}%`}})})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 text-xs mb-3",children:[o.pricing&&e.jsxs("span",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),o.pricing]}),o.trial_days&&o.trial_days>0&&e.jsxs("span",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"})}),o.trial_days,"-day trial"]})]}),n&&o.features&&o.features.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1",children:"Key Features:"}),e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[o.features.slice(0,4).map((v,h)=>e.jsxs("span",{className:"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),v]},h)),(o.features.length||0)>4&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 italic",children:["+",o.features.length-4," more"]})]})]}),e.jsx(O,{adId:o.ad_id,admeshLink:o.admesh_link,productId:o.product_id,trackingData:{title:o.title,matchScore:o.intent_match_score,component:"compare_table_cta"},children:e.jsxs("button",{className:"w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors",children:["Visit Offer",e.jsx("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})]},o.product_id||y))}),e.jsx("div",{className:"flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50",children:e.jsxs("span",{className:"flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500",children:[e.jsx("svg",{className:"w-3 h-3 text-indigo-500",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"})}),e.jsx("span",{className:"font-medium",children:"Powered by"}),e.jsx("span",{className:"font-semibold text-indigo-600 dark:text-indigo-400",children:"AdMesh"})]})})]})})};ye.displayName="AdMeshCompareTable";const Ue={"Top Match":"primary","Free Tier":"success","AI Powered":"secondary",Popular:"warning",New:"primary","Trial Available":"success"},qe={"Top Match":"★","Free Tier":"◆","AI Powered":"◉",Popular:"▲",New:"●","Trial Available":"◈"},ke=({type:t,variant:r,size:s="md",className:i,style:n})=>{const a=r||Ue[t]||"secondary",d=qe[t],c=M("admesh-component","admesh-badge",`admesh-badge--${a}`,`admesh-badge--${s}`,i);return e.jsxs("span",{className:c,style:n,children:[d&&e.jsx("span",{className:"admesh-badge__icon",children:d}),e.jsx("span",{className:"admesh-badge__text",children:t})]})};ke.displayName="AdMeshBadge";const He=({recommendation:t,theme:r={mode:"light"},className:s="",style:i,showPoweredBy:n=!0,initialExpanded:a=!1,sections:d,ctaText:c,collapsible:u=!0})=>{var T,S,A,j,L,E,P,I,$,U;re();const[m,p]=b.useState(a),x=()=>{u&&p(!m)},o=t.feature_sections||[],y=[{title:"Documentation",description:`Learn more about ${t.recommendation_title||t.title}. Start exploring the features and capabilities.`,icon:"◆"},{title:"Talk To An Expert",description:`Ready to learn more about ${t.recommendation_title||t.title}? Reach out to a platform specialist for personalized guidance.`,icon:"◉"},{title:`${t.recommendation_title||t.title} Features`,description:t.recommendation_description||t.description||`${t.recommendation_title||t.title} offers comprehensive solutions for your needs. Discover the full potential.`,icon:"▲"},{title:"How it Works",description:`Learn how to get started with ${t.recommendation_title||t.title}. Begin your journey today.`,icon:"●"}],v=te(t,!1),h=se(),g=d||(o.length>0?o:y),w=c||`Try ${t.recommendation_title||t.title}`,f={background:r.backgroundColor||(r.mode==="dark"?"#1f2937":"#ffffff"),surface:r.surfaceColor||(r.mode==="dark"?"#374151":"#f9fafb"),border:r.borderColor||(r.mode==="dark"?"#4b5563":"#e5e7eb"),text:r.textColor||(r.mode==="dark"?"#f9fafb":"#111827"),textSecondary:r.textSecondaryColor||(r.mode==="dark"?"#9ca3af":"#6b7280"),accent:r.accentColor||r.primaryColor||"#3b82f6",secondary:r.secondaryColor||"#10b981",headerBg:((T=r.gradients)==null?void 0:T.primary)||(r.mode==="dark"?"#374151":"#f8fafc"),sectionBg:((S=r.gradients)==null?void 0:S.secondary)||(r.mode==="dark"?"#4b5563":"#ffffff")},C=r.disableDefaultStyles?{}:{fontFamily:r.fontFamily||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',borderRadius:r.borderRadius||"12px",border:`1px solid ${f.border}`,background:f.background,overflow:"hidden",maxWidth:"420px",boxShadow:((A=r.shadows)==null?void 0:A.medium)||(r.mode==="dark"?"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"),position:"relative",transition:"all 0.2s ease"};return e.jsxs("div",{className:`admesh-component admesh-expandable-unit ${s}`,style:{...C,...(j=r.components)==null?void 0:j.expandableUnit,...i},"data-admesh-theme":r.mode,children:[e.jsx("div",{style:{background:f.headerBg,padding:"20px",borderBottom:m||!u?`1px solid ${f.border}`:"none",position:"relative",transition:"all 0.2s ease"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",gap:"16px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px",flex:1,minWidth:0},children:[e.jsx("div",{style:{width:"40px",height:"40px",borderRadius:r.borderRadius||"8px",background:f.accent,display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:((L=r.fontSize)==null?void 0:L.base)||"16px",fontWeight:"600",boxShadow:((E=r.shadows)==null?void 0:E.small)||"0 2px 4px rgba(0, 0, 0, 0.1)",border:`1px solid ${f.border}`},children:(t.recommendation_title||t.title).charAt(0).toUpperCase()}),e.jsxs("div",{style:{flex:1,minWidth:0},children:[e.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:f.text,lineHeight:"1.4",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:t.recommendation_title||t.title}),e.jsxs("p",{style:{margin:"8px 0 0 0",fontSize:"13px",color:f.textSecondary,fontWeight:"400",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},title:h,children:[v," • ",new URL(t.url||t.admesh_link).hostname]})]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[!m&&u&&e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.recommendation_title||t.title,component:"expandable_unit",expanded:!1,location:"header"},children:e.jsx("button",{style:{padding:(P=r.spacing)!=null&&P.small?`${r.spacing.small} ${r.spacing.medium||"12px"}`:"6px 12px",backgroundColor:f.accent,color:"white",border:"none",borderRadius:r.borderRadius||"6px",fontSize:((I=r.fontSize)==null?void 0:I.small)||"12px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",boxShadow:(($=r.shadows)==null?void 0:$.small)||"0 1px 3px rgba(0, 0, 0, 0.1)",whiteSpace:"nowrap",...(U=r.components)==null?void 0:U.button},onMouseOver:_=>{var k;r.disableDefaultStyles||(_.currentTarget.style.transform="translateY(-1px)",_.currentTarget.style.boxShadow=((k=r.shadows)==null?void 0:k.medium)||"0 2px 6px rgba(0, 0, 0, 0.15)")},onMouseOut:_=>{var k;r.disableDefaultStyles||(_.currentTarget.style.transform="translateY(0)",_.currentTarget.style.boxShadow=((k=r.shadows)==null?void 0:k.small)||"0 1px 3px rgba(0, 0, 0, 0.1)")},children:w})}),u&&e.jsxs("button",{onClick:x,style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 12px",background:r.mode==="dark"?"#374151":"#f3f4f6",border:`1px solid ${r.mode==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"8px",cursor:"pointer",color:r.accentColor||"#2563eb",fontSize:"12px",fontWeight:"600",transition:"all 0.2s ease"},onMouseEnter:_=>{_.currentTarget.style.background=r.mode==="dark"?"#4b5563":"#e5e7eb",_.currentTarget.style.borderColor=r.accentColor||"#2563eb"},onMouseLeave:_=>{_.currentTarget.style.background=r.mode==="dark"?"#374151":"#f3f4f6",_.currentTarget.style.borderColor=r.mode==="dark"?"#4b5563":"#d1d5db"},"aria-label":m?"Show less details":"Show more details",children:[e.jsx("span",{children:m?"Less Details":"More Details"}),e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:m?e.jsx("path",{d:"M5 12h14"}):e.jsxs(e.Fragment,{children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("path",{d:"M12 16v-4"}),e.jsx("path",{d:"M12 8h.01"})]})})]})]})]})}),(m||!u)&&e.jsxs("div",{style:{padding:"0"},children:[g.map((_,k)=>e.jsxs("div",{style:{padding:"24px",backgroundColor:k%2===0?f.background:f.sectionBg,borderBottom:k<g.length-1?`1px solid ${f.border}`:"none"},children:[e.jsxs("h4",{style:{margin:"0 0 12px 0",fontSize:"15px",fontWeight:"600",color:f.text,display:"flex",alignItems:"center",gap:"12px"},children:[_.icon&&e.jsx("span",{children:_.icon}),_.title]}),e.jsx("p",{style:{margin:0,fontSize:"14px",color:f.textSecondary,lineHeight:"1.6"},children:_.description})]},k)),(m||!u)&&e.jsx("div",{style:{padding:"24px",borderTop:`1px solid ${f.border}`,backgroundColor:f.background},children:e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,component:"expandable_unit",expanded:m,location:"footer"},children:e.jsx("button",{style:{width:"100%",padding:"14px 28px",background:f.accent,color:"white",border:"none",borderRadius:"12px",fontSize:"15px",fontWeight:"600",cursor:"pointer",transition:"all 0.3s ease",boxShadow:"0 4px 12px rgba(99, 102, 241, 0.3)",position:"relative",overflow:"hidden"},onMouseOver:_=>{_.currentTarget.style.transform="translateY(-2px) scale(1.02)",_.currentTarget.style.boxShadow="0 8px 20px rgba(99, 102, 241, 0.4)"},onMouseOut:_=>{_.currentTarget.style.transform="translateY(0) scale(1)",_.currentTarget.style.boxShadow="0 4px 12px rgba(99, 102, 241, 0.3)"},children:w})})}),n&&e.jsx("div",{style:{padding:"8px 16px",borderTop:`1px solid ${f.border}`,backgroundColor:f.headerBg},children:e.jsxs("div",{style:{fontSize:"11px",color:f.textSecondary,textAlign:"center"},children:["powered by ",e.jsx("strong",{style:{color:f.text},children:"AdMesh"})]})})]})]})},V=({recommendation:t,theme:r,compact:s=!1,showReason:i=!0,className:n,style:a})=>{var x;const d=Math.round(t.intent_match_score*100),c=te(t,s),u=se(),m=M("admesh-inline-recommendation","group cursor-pointer transition-all duration-200",{"p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700":!s,"p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30":s},n),p=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0;return e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score},className:m,style:{fontFamily:(r==null?void 0:r.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(x=r==null?void 0:r.components)==null?void 0:x.inlineRecommendation,...a},children:e.jsxs("div",{className:"flex items-start gap-3",style:p,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsx("div",{className:"flex-shrink-0 mt-0.5",children:t.offer_images&&t.offer_images.length>0?e.jsx("div",{className:"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600",children:e.jsx("img",{src:t.offer_images[0].url,alt:t.recommendation_title||t.title,className:"w-full h-full object-cover"})}):t.product_logo?e.jsx("div",{className:"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600",children:e.jsx("img",{src:t.product_logo.url,alt:t.recommendation_title||t.title,className:"w-full h-full object-cover"})}):t.intent_match_score>=.8?e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}):e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row",children:[e.jsx("h4",{className:M("font-medium transition-colors duration-200 flex-shrink-0","text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300","cursor-pointer hover:underline",s?"text-sm sm:text-base":"text-base sm:text-lg"),children:t.recommendation_title||t.title}),t.intent_match_score>=.7&&e.jsxs("span",{className:M("inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap",t.intent_match_score>=.8?"bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100":"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"),children:[d,"% match"]})]}),i&&(t.recommendation_description||t.reason)&&e.jsx("p",{className:M("text-gray-600 dark:text-gray-400 line-clamp-2",s?"text-xs":"text-sm"),children:t.recommendation_description||t.reason}),e.jsx("p",{className:M("text-gray-500 dark:text-gray-400 mt-1","text-xs"),title:u,children:c}),!s&&t.keywords&&t.keywords.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1 mt-2",children:[t.keywords.slice(0,3).map((o,y)=>e.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300",children:o},y)),t.keywords.length>3&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",t.keywords.length-3," more"]})]}),!s&&t.trial_days&&t.trial_days>0&&e.jsx("div",{className:"flex items-center gap-2 mt-2",children:e.jsxs("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",children:[t.trial_days,"-day trial"]})})]}),e.jsx("div",{className:"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("svg",{className:"w-4 h-4 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})},ve=({recommendations:t,conversationSummary:r,theme:s,showTopRecommendations:i=3,onRecommendationClick:n,onStartNewConversation:a,className:d})=>{const c=t.sort((p,x)=>x.intent_match_score-p.intent_match_score).slice(0,i),u=M("admesh-conversation-summary","bg-white dark:bg-black","rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6","font-sans",d),m=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return e.jsxs("div",{className:u,style:m,"data-admesh-theme":s==null?void 0:s.mode,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h3",{className:"text-base sm:text-lg font-semibold text-black dark:text-white",children:"Conversation Summary"}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300",children:"Here's what we discussed and found for you"})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:e.jsx("p",{className:"text-gray-800 dark:text-gray-200 leading-relaxed",children:r})})}),c.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("svg",{className:"w-5 h-5 text-black dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.jsx("h4",{className:"font-medium text-black dark:text-white",children:"Top Recommendations"})]}),e.jsx("div",{className:"space-y-2",children:c.map((p,x)=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-2 top-2 z-10",children:e.jsx("div",{className:M("w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold",x===0?"bg-black dark:bg-white text-white dark:text-black":x===1?"bg-gray-600 dark:bg-gray-400 text-white dark:text-black":"bg-gray-800 dark:bg-gray-200 text-white dark:text-black"),children:x+1})}),e.jsx("div",{className:"ml-4",children:e.jsx(V,{recommendation:p,theme:s,compact:!0,showReason:!0,onClick:n})})]},p.ad_id||x))})]}),t.length>i&&e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-black dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsxs("span",{className:"text-sm font-medium text-gray-800 dark:text-gray-200",children:[t.length-i," additional recommendation",t.length-i>1?"s":""," available"]})]})})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[a&&e.jsxs("button",{onClick:a,className:"flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),"Start New Conversation"]}),e.jsxs("button",{onClick:()=>{c.length>0&&(n==null||n(c[0].ad_id,c[0].admesh_link))},className:"flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})}),"View Top Pick"]})]}),e.jsx("div",{className:"flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Powered by AdMesh"})})]})},me=({recommendation:t,citationNumber:r,citationStyle:s="numbered",theme:i,showTooltip:n=!0,onHover:a,className:d,style:c})=>{const[u,m]=b.useState(!1),p=()=>{m(!0),a==null||a(t)},x=()=>{m(!1)},o=()=>{switch(s){case"bracketed":return`[${r}]`;case"superscript":return r.toString();case"numbered":default:return r.toString()}},y=M("admesh-citation-reference","inline-flex items-center justify-center","cursor-pointer transition-all duration-200","text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300","font-medium",{"w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50":s==="numbered","px-1 text-sm hover:underline":s==="bracketed","text-xs align-super hover:underline":s==="superscript"},d),v=i!=null&&i.accentColor?{"--admesh-primary":i.accentColor}:void 0;return e.jsxs("span",{className:"relative inline-block",children:[e.jsx(O,{adId:t.ad_id,admeshLink:t.admesh_link,productId:t.product_id,trackingData:{title:t.title,matchScore:t.intent_match_score,citationNumber:r,citationStyle:s},className:y,style:c,children:e.jsx("span",{style:v,"data-admesh-theme":i==null?void 0:i.mode,onMouseEnter:p,onMouseLeave:x,children:o()})}),n&&u&&e.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50",children:e.jsxs("div",{className:"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs",children:[e.jsx("div",{className:"font-semibold mb-1",children:t.title}),t.reason&&e.jsx("div",{className:"text-gray-300 dark:text-gray-600 text-xs",children:t.reason.length>100?`${t.reason.substring(0,100)}...`:t.reason}),t.intent_match_score>=.7&&e.jsxs("div",{className:"text-green-400 dark:text-green-600 text-xs mt-1",children:[Math.round(t.intent_match_score*100),"% match"]}),e.jsx("div",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1 italic",children:"Click to visit product page"}),e.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"})]})})]})},we=({recommendations:t,conversationText:r,theme:s,showCitationList:i=!0,citationStyle:n="numbered",onCitationHover:a,className:d,style:c})=>{var v;const[u,m]=b.useState(null),p=b.useMemo(()=>{if(!r||t.length===0)return{text:r,citationMap:new Map};let h=r;const g=new Map;return[...t].sort((f,C)=>C.intent_match_score-f.intent_match_score).forEach((f,C)=>{const T=C+1,S=f.title;g.set(T,f);const A=new RegExp(`\\b${S.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"gi");if(A.test(h))h=h.replace(A,j=>`${j}{{CITATION_${T}}}`);else{const j=f.keywords||[];let L=!1;for(const E of j){const P=new RegExp(`\\b${E.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"gi");if(P.test(h)&&!L){h=h.replace(P,I=>(L=!0,`${I}{{CITATION_${T}}}`));break}}L||(h+=`{{CITATION_${T}}}`)}}),{text:h,citationMap:g}},[r,t]),x=()=>{const{text:h,citationMap:g}=p;return h.split(/(\{\{CITATION_\d+\}\})/).map((f,C)=>{const T=f.match(/\{\{CITATION_(\d+)\}\}/);if(T){const S=parseInt(T[1]),A=g.get(S);if(A)return e.jsx(me,{recommendation:A,citationNumber:S,citationStyle:n,theme:s,showTooltip:!0,onHover:j=>{m(j),a==null||a(j)}},`citation-${S}-${C}`)}return e.jsx("span",{children:f},C)})},o=M("admesh-citation-unit","space-y-4",d),y=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return e.jsxs("div",{className:o,style:{...y,fontFamily:(s==null?void 0:s.fontFamily)||'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',...(v=s==null?void 0:s.components)==null?void 0:v.citationUnit,...c},"data-admesh-theme":s==null?void 0:s.mode,children:[e.jsx("div",{className:"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed",children:x()}),i&&t.length>0&&e.jsx("div",{className:"admesh-citation-list",children:e.jsxs("div",{className:"border-t border-gray-200 dark:border-slate-700 pt-4",children:[e.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})}),"References"]}),e.jsx("div",{className:"space-y-2",children:t.sort((h,g)=>g.intent_match_score-h.intent_match_score).map((h,g)=>e.jsxs("div",{className:M("flex items-start gap-3 p-2 rounded-lg transition-colors duration-200",{"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800":(u==null?void 0:u.ad_id)===h.ad_id,"hover:bg-gray-50 dark:hover:bg-slate-800/50":(u==null?void 0:u.ad_id)!==h.ad_id}),children:[e.jsx("div",{className:"flex-shrink-0 mt-1",children:e.jsx(me,{recommendation:h,citationNumber:g+1,citationStyle:n,theme:s,showTooltip:!1})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsx(V,{recommendation:h,theme:s,compact:!0,showReason:!1})})]},h.ad_id||g))})]})})]})},xe=({recommendations:t,config:r,theme:s,conversationSummary:i,sessionId:n,onRecommendationClick:a,onDismiss:d,className:c})=>{const[u,m]=b.useState(r.autoShow!==!1),[p,x]=b.useState(!1);if(b.useEffect(()=>{if(r.delayMs&&r.delayMs>0){const C=setTimeout(()=>{m(!0),x(!0)},r.delayMs);return()=>clearTimeout(C)}else x(!0)},[r.delayMs]),!u||t.length===0)return null;const o=r.maxRecommendations||3,y=t.slice(0,o),v=(C,T)=>{a==null||a(C,T)},h=()=>{m(!1),d==null||d()},g=()=>{switch(r.displayMode){case"summary":return i?e.jsx(ve,{recommendations:y,conversationSummary:i,theme:s,showTopRecommendations:o,onRecommendationClick:v,onStartNewConversation:d}):null;case"inline":return e.jsx("div",{className:"space-y-2",children:y.map((C,T)=>e.jsx(V,{recommendation:C,theme:s,compact:!0,showReason:!0,onClick:v},C.ad_id||T))});case"minimal":return y.length>0?e.jsxs("div",{className:"admesh-minimal-unit",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[y.length," intelligent match",y.length>1?"es":""," found"]})]}),e.jsx(V,{recommendation:y[0],theme:s,compact:!0,showReason:!1,onClick:v}),y.length>1&&e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["+",y.length-1," more recommendation",y.length>2?"s":""]})]}):null;case"citation":return i?e.jsx(we,{recommendations:y,conversationText:i,theme:s,showCitationList:!0,citationStyle:"numbered",onRecommendationClick:v}):null;case"floating":return e.jsxs("div",{className:"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200",children:"Recommended for you"})]}),d&&e.jsx("button",{onClick:h,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"space-y-2",children:y.map((C,T)=>e.jsx(V,{recommendation:C,theme:s,compact:!0,showReason:!1,onClick:v},C.ad_id||T))})]});default:return e.jsx("div",{className:"space-y-3",children:y.map((C,T)=>e.jsx(ae,{recommendation:C,theme:s,showMatchScore:!1,showBadges:!0,onClick:v},C.ad_id||T))})}},w=M("admesh-conversational-unit","transition-all duration-300 ease-in-out",{"opacity-0 translate-y-2":!p,"opacity-100 translate-y-0":p,"fixed bottom-4 right-4 max-w-sm z-50":r.displayMode==="floating","my-3":r.displayMode==="inline","mt-4 pt-4 border-t border-gray-200 dark:border-slate-700":r.displayMode==="summary"},c),f=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return e.jsxs("div",{className:w,style:f,"data-admesh-theme":s==null?void 0:s.mode,"data-admesh-context":r.context,"data-session-id":n,children:[g(),r.showPoweredBy!==!1&&e.jsx("div",{className:"flex justify-end mt-2",children:e.jsx("span",{className:"text-xs text-gray-400 dark:text-gray-500",children:"Powered by AdMesh"})})]})},je=({message:t,theme:r,onRecommendationClick:s,className:i})=>{const n=t.role==="user",a=t.role==="assistant",d=M("admesh-chat-message","flex items-start gap-3",{"flex-row-reverse":n},i),c=M("max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm",{"bg-gradient-to-r from-blue-600 to-indigo-600 text-white":n,"bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100":a,"bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100":t.role==="system"}),u=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0,m=p=>p.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return e.jsxs("div",{className:d,style:u,"data-admesh-theme":r==null?void 0:r.mode,children:[!n&&e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),n&&e.jsx("div",{className:"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),e.jsxs("div",{className:`flex flex-col ${n?"items-end":"items-start"} flex-1`,children:[e.jsx("div",{className:c,children:e.jsx("div",{className:"whitespace-pre-wrap break-words",children:t.content})}),e.jsx("div",{className:M("text-xs text-gray-500 dark:text-gray-400 mt-1",{"text-right":n}),children:m(t.timestamp)}),t.recommendations&&t.recommendations.length>0&&e.jsxs("div",{className:"mt-3 w-full max-w-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("svg",{className:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.recommendations.length," recommendation",t.recommendations.length>1?"s":""," found"]})]}),e.jsx(xe,{recommendations:t.recommendations,config:{displayMode:"inline",context:"chat",maxRecommendations:3,showPoweredBy:!1,autoShow:!0,delayMs:300},theme:r,onRecommendationClick:s,className:"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700"})]})]})]})},Ne=({placeholder:t="Type your message...",disabled:r=!1,suggestions:s=[],theme:i,onSendMessage:n,className:a})=>{const[d,c]=b.useState(""),[u,m]=b.useState(!1),[p,x]=b.useState([]),o=b.useRef(null),y=S=>{const A=S.target.value;if(c(A),A.trim()&&s.length>0){const j=s.filter(L=>L.toLowerCase().includes(A.toLowerCase()));x(j),m(j.length>0)}else m(!1);o.current&&(o.current.style.height="auto",o.current.style.height=`${Math.min(o.current.scrollHeight,120)}px`)},v=S=>{S.key==="Enter"&&!S.shiftKey&&(S.preventDefault(),h())},h=()=>{const S=d.trim();S&&!r&&n&&(n(S),c(""),m(!1),o.current&&(o.current.style.height="auto"))},g=S=>{c(S),m(!1),o.current&&o.current.focus()},w=M("admesh-chat-input","relative",a),f=M("w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600","bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100","placeholder-gray-500 dark:placeholder-gray-400","focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent","transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600","pr-12 pl-4 py-3 text-sm leading-5",{"opacity-50 cursor-not-allowed":r}),C=M("absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200","flex items-center justify-center",{"bg-blue-600 hover:bg-blue-700 text-white":d.trim()&&!r,"bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed":!d.trim()||r}),T=i!=null&&i.accentColor?{"--admesh-primary":i.accentColor}:void 0;return e.jsxs("div",{className:w,style:T,"data-admesh-theme":i==null?void 0:i.mode,children:[u&&p.length>0&&e.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10",children:p.slice(0,5).map((S,A)=>e.jsx("button",{onClick:()=>g(S),className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg",children:S},A))}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{ref:o,value:d,onChange:y,onKeyDown:v,placeholder:t,disabled:r,rows:1,className:f,style:{minHeight:"44px",maxHeight:"120px"}}),e.jsx("button",{onClick:h,disabled:!d.trim()||r,className:C,"aria-label":"Send message",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})]}),e.jsxs("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{children:"Press Enter to send, Shift+Enter for new line"}),e.jsxs("span",{className:M("transition-opacity duration-200",{"opacity-0":d.length<100}),children:[d.length,"/500"]})]})]})},_e=({messages:t,config:r,theme:s,isLoading:i=!1,onSendMessage:n,onRecommendationClick:a,className:d})=>{const c=b.useRef(null),u=b.useRef(null);b.useEffect(()=>{c.current&&c.current.scrollIntoView({behavior:"smooth"})},[t]);const m=M("admesh-chat-interface","flex flex-col h-full bg-white dark:bg-slate-900",d),p=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0,x=r.maxMessages?t.slice(-r.maxMessages):t;return e.jsxs("div",{className:m,style:p,"data-admesh-theme":s==null?void 0:s.mode,children:[e.jsx("div",{ref:u,className:"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",children:x.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Welcome to AdMesh AI"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 max-w-xs",children:"Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!"})]}):e.jsxs(e.Fragment,{children:[x.map(o=>e.jsx(je,{message:o,theme:s,onRecommendationClick:a},o.id)),i&&r.enableTypingIndicator!==!1&&e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("div",{className:"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs",children:e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),e.jsx("div",{ref:c})]})}),r.enableSuggestions&&r.suggestions&&r.suggestions.length>0&&t.length===0&&e.jsxs("div",{className:"px-4 pb-2",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"Quick suggestions:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:r.suggestions.slice(0,3).map((o,y)=>e.jsx("button",{onClick:()=>n==null?void 0:n(o),className:"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors",children:o},y))})]}),r.showInputField!==!1&&n&&e.jsx("div",{className:"border-t border-gray-200 dark:border-slate-700 p-4",children:e.jsx(Ne,{placeholder:r.placeholder||"Ask me about products, tools, or services...",disabled:i,suggestions:r.suggestions,theme:s,onSendMessage:n})})]})},Ye=({config:t,theme:r,title:s="AI Assistant",subtitle:i="Get personalized recommendations",isOpen:n,onToggle:a,onSendMessage:d,onRecommendationClick:c,autoRecommendations:u,autoRecommendationTrigger:m,showInputField:p=!0,autoShowRecommendations:x=!1,onAutoRecommendationDismiss:o,className:y})=>{const[v,h]=b.useState(t.autoOpen||!1),[g,w]=b.useState([]),[f,C]=b.useState(!1),[T,S]=b.useState(!1),A=n!==void 0?n:v;b.useEffect(()=>{if(t.showWelcomeMessage&&t.welcomeMessage&&g.length===0){const _={id:"welcome",role:"assistant",content:t.welcomeMessage,timestamp:new Date};w([_])}},[t.showWelcomeMessage,t.welcomeMessage,g.length]),b.useEffect(()=>{if(u&&u.length>0&&x){const _={id:`auto-${Date.now()}`,role:"assistant",content:m?`Based on "${m}", here are some relevant recommendations:`:"I found some relevant recommendations for you:",timestamp:new Date,recommendations:u};n===void 0&&h(!0),w(k=>k.some(F=>F.id.startsWith("auto-"))?k.map(F=>F.id.startsWith("auto-")?_:F):[...k,_])}},[u,x,m,n]);const j=()=>{a?a():h(!v),S(!0)},L=async _=>{if(!d)return;const k={id:`user-${Date.now()}`,role:"user",content:_,timestamp:new Date};w(z=>[...z,k]),C(!0);try{await d(_)}catch(z){console.error("Error sending message:",z);const F={id:`error-${Date.now()}`,role:"assistant",content:"Sorry, I encountered an error. Please try again.",timestamp:new Date};w(K=>[...K,F])}finally{C(!1)}},E=()=>{switch(t.size){case"sm":return"w-80 h-96";case"md":return"w-96 h-[32rem]";case"lg":return"w-[28rem] h-[36rem]";case"xl":return"w-[32rem] h-[40rem]";default:return"w-96 h-[32rem]"}},I=M("admesh-floating-chat","fixed z-50 transition-all duration-300 ease-in-out",(()=>{switch(t.position){case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";default:return"bottom-4 right-4"}})(),y),$=M("bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",E(),{"opacity-0 scale-95 pointer-events-none":!A,"opacity-100 scale-100":A}),U=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0;return e.jsxs("div",{className:I,style:U,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsx("div",{className:$,children:A&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm",children:s}),e.jsx("p",{className:"text-xs text-blue-100",children:i})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[u&&u.length>0&&o&&e.jsx("button",{onClick:()=>{o(),w(_=>_.filter(k=>!k.id.startsWith("auto-")))},className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Dismiss recommendations",title:"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})})}),e.jsx("button",{onClick:j,className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Close chat",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsx(_e,{messages:g,config:{...t,showInputField:p},theme:r,isLoading:f,onSendMessage:p?L:()=>{},onRecommendationClick:c,className:"h-full"})]})}),!A&&e.jsxs("button",{onClick:j,className:M("w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700","text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200","flex items-center justify-center relative"),"aria-label":"Open chat",children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),!T&&e.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),A&&e.jsx("div",{className:"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm",children:"Powered by AdMesh"})]})},Ce=({title:t,theme:r,collapsible:s=!1,isCollapsed:i=!1,onToggle:n,onSearch:a,showSearch:d=!1,className:c})=>{const[u,m]=b.useState(""),[p,x]=b.useState(!1),[o,y]=b.useState(!1);b.useEffect(()=>{const f=()=>{y(window.innerWidth<640)};return f(),window.addEventListener("resize",f),()=>window.removeEventListener("resize",f)},[]);const v=f=>{const C=f.target.value;m(C),a==null||a(C)},h=()=>{m(""),a==null||a("")},g=M("admesh-sidebar-header","flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",c),w=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0;return e.jsxs("div",{className:g,style:w,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate",children:t}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&n&&e.jsx("button",{onClick:n,className:"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden",title:"Close sidebar",children:e.jsx("svg",{className:"w-4 h-4 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),s&&e.jsx("button",{onClick:n,className:"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block",title:i?"Expand sidebar":"Collapse sidebar",children:e.jsx("svg",{className:M("w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200",{"rotate-180":i}),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})]})]}),d&&!i&&e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:M("relative flex items-center transition-all duration-200",{"ring-2 ring-blue-500 dark:ring-blue-400":p}),children:[e.jsx("div",{className:"absolute left-3 pointer-events-none",children:e.jsx("svg",{className:"w-4 h-4 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("input",{type:"text",value:u,onChange:v,onFocus:()=>x(!0),onBlur:()=>x(!1),placeholder:"Search recommendations...",className:M("w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg","placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent","transition-all duration-200")}),u&&e.jsx("button",{onClick:h,className:"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors",title:"Clear search",children:e.jsx("svg",{className:"w-3 h-3 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),u&&e.jsx("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:"Search results will be filtered in real-time"})]}),!i&&e.jsxs("div",{className:"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{children:"Live recommendations"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.jsx("span",{children:"AI-powered"})]})]})]})},Me=({recommendations:t,displayMode:r,theme:s,maxRecommendations:i,onRecommendationClick:n,className:a})=>{const[d,c]=b.useState(!1),[u,m]=b.useState("all"),p=i?t.slice(0,i):t,o=(()=>{switch(u){case"top":return p.filter(g=>g.intent_match_score>=.8).slice(0,5);case"recent":return p.slice(0,3);default:return p}})(),y=M("admesh-sidebar-content","flex flex-col h-full",a),v=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0,h=()=>{if(o.length===0)return e.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No recommendations found"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Try adjusting your search or filters"})]});switch(r){case"recommendations":return e.jsx("div",{className:"space-y-3",children:o.map((g,w)=>e.jsx(V,{recommendation:g,theme:s,compact:!0,showReason:!0,onClick:n},g.ad_id||w))});case"history":return e.jsx("div",{className:"space-y-2",children:o.map((g,w)=>e.jsxs("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:g.title}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[Math.round(g.intent_match_score*100),"% match"]})]})]},g.ad_id||w))});case"favorites":return e.jsx("div",{className:"space-y-3",children:o.slice(0,3).map((g,w)=>e.jsxs("div",{className:"relative",children:[e.jsx(V,{recommendation:g,theme:s,compact:!0,showReason:!1,onClick:n}),e.jsx("button",{className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",children:e.jsx("svg",{className:"w-3 h-3 text-yellow-500",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})})]},g.ad_id||w))});case"mixed":return e.jsxs("div",{className:"space-y-4",children:[o[0]&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",children:"Top Pick"}),e.jsx(ae,{recommendation:o[0],theme:s,showMatchScore:!0,showBadges:!0,onClick:n,className:"text-xs"})]}),o.slice(1).length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",children:"More Options"}),e.jsx("div",{className:"space-y-2",children:o.slice(1,4).map((g,w)=>e.jsx(V,{recommendation:g,theme:s,compact:!0,showReason:!1,onClick:n},g.ad_id||w))})]})]});default:return e.jsx("div",{className:"space-y-3",children:o.map((g,w)=>e.jsx(V,{recommendation:g,theme:s,compact:!0,showReason:!0,onClick:n},g.ad_id||w))})}};return e.jsxs("div",{className:y,style:v,"data-admesh-theme":s==null?void 0:s.mode,children:[e.jsxs("div",{className:"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900",children:[e.jsxs("button",{onClick:()=>m("all"),className:M("flex-1 px-3 py-2 text-xs font-medium transition-colors",u==="all"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:["All (",t.length,")"]}),e.jsx("button",{onClick:()=>m("top"),className:M("flex-1 px-3 py-2 text-xs font-medium transition-colors",u==="top"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:"Top"}),e.jsx("button",{onClick:()=>m("recent"),className:M("flex-1 px-3 py-2 text-xs font-medium transition-colors",u==="recent"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:"Recent"})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4 min-h-0",style:{WebkitOverflowScrolling:"touch",overscrollBehavior:"contain"},children:h()}),e.jsx("div",{className:"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",children:e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsxs("span",{className:"text-gray-500 dark:text-gray-400",children:[o.length," recommendation",o.length!==1?"s":""]}),e.jsx("button",{onClick:()=>c(!d),className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors",children:"Filters"})]})})]})},Ge=({recommendations:t,config:r,theme:s,title:i="Recommendations",isOpen:n=!0,onToggle:a,onRecommendationClick:d,onSearch:c,className:u,containerMode:m=!1})=>{const[p,x]=b.useState(r.defaultCollapsed||!1),[o,y]=b.useState(""),[v]=b.useState({}),[h,g]=b.useState(!1);b.useEffect(()=>{const j=()=>{g(window.innerWidth<640)};return j(),window.addEventListener("resize",j),()=>window.removeEventListener("resize",j)},[]),b.useEffect(()=>{if(h&&n&&!p&&!m){const j=window.getComputedStyle(document.body).overflow;return document.body.style.overflow="hidden",document.body.style.position="fixed",document.body.style.width="100%",()=>{document.body.style.overflow=j,document.body.style.position="",document.body.style.width=""}}},[h,n,p,m]),b.useEffect(()=>{if(r.autoRefresh&&r.refreshInterval){const j=setInterval(()=>{console.log("Auto-refreshing recommendations...")},r.refreshInterval);return()=>clearInterval(j)}},[r.autoRefresh,r.refreshInterval]);const w=b.useMemo(()=>{let j=[...t];if(o.trim()){const L=o.toLowerCase();j=j.filter(E=>{var P;return E.title.toLowerCase().includes(L)||E.reason.toLowerCase().includes(L)||((P=E.keywords)==null?void 0:P.some(I=>I.toLowerCase().includes(L)))})}return v.categories&&v.categories.length>0&&(j=j.filter(L=>{var E;return(E=L.categories)==null?void 0:E.some(P=>{var I;return(I=v.categories)==null?void 0:I.includes(P)})})),v.hasFreeTier,v.hasTrial&&(j=j.filter(L=>L.trial_days&&L.trial_days>0)),v.minMatchScore!==void 0&&(j=j.filter(L=>L.intent_match_score>=v.minMatchScore)),j.sort((L,E)=>E.intent_match_score-L.intent_match_score),r.maxRecommendations&&(j=j.slice(0,r.maxRecommendations)),j},[t,o,v,r.maxRecommendations]),f=()=>{r.collapsible&&(x(!p),a==null||a())},C=j=>{y(j),c==null||c(j)},S=M("admesh-sidebar","flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out",(()=>{if(p)return"w-12";switch(r.size){case"sm":return"w-full sm:w-64 max-w-[90vw] sm:max-w-sm";case"md":return"w-full sm:w-80 max-w-[90vw] sm:max-w-md";case"lg":return"w-full sm:w-96 max-w-[90vw] sm:max-w-lg";case"xl":return"w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl";default:return"w-full sm:w-80 max-w-[90vw] sm:max-w-md"}})(),{"border-r":r.position==="left","border-l":r.position==="right","fixed top-0 bottom-0 z-[9999]":!m,"relative h-full":m,"left-0":r.position==="left"&&!m,"right-0":r.position==="right"&&!m,"transform -translate-x-full":r.position==="left"&&!n&&!m,"transform translate-x-full":r.position==="right"&&!n&&!m,"min-h-0":!0,"overflow-hidden":!m},u),A=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return!n&&!r.collapsible?null:e.jsxs(e.Fragment,{children:[n&&!p&&e.jsx("div",{className:M("bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300",m?"absolute inset-0":"fixed inset-0"),onClick:()=>a==null?void 0:a(),style:{position:m?"absolute":"fixed",top:0,left:0,right:0,bottom:0,touchAction:"none"}}),e.jsxs("div",{className:S,style:A,"data-admesh-theme":s==null?void 0:s.mode,"data-sidebar-position":r.position,"data-sidebar-size":r.size,"data-mobile-open":h&&n&&!p?"true":"false","data-container-mode":m?"true":"false",children:[r.showHeader!==!1&&e.jsx(Ce,{title:i,theme:s,collapsible:r.collapsible,isCollapsed:p,onToggle:f,onSearch:r.showSearch?C:void 0,showSearch:r.showSearch&&!p}),!p&&e.jsx(Me,{recommendations:w,displayMode:r.displayMode,theme:s,maxRecommendations:r.maxRecommendations,onRecommendationClick:d,className:"flex-1 overflow-hidden min-h-0"}),p&&r.collapsible&&e.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center p-2",children:[e.jsx("button",{onClick:f,className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors",title:"Expand sidebar",children:e.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),e.jsx("div",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap",children:w.length})]}),!p&&e.jsx("div",{className:"p-3 border-t border-gray-200 dark:border-slate-700",children:e.jsx("div",{className:"text-xs text-gray-400 dark:text-gray-500 text-center",children:"Powered by AdMesh"})})]})]})},Ke=({recommendations:t,trigger:r,theme:s,title:i="AI Recommendations",position:n="bottom-right",size:a="md",autoShow:d=!0,showDelay:c=1e3,onRecommendationClick:u,onDismiss:m,className:p})=>{const[x,o]=b.useState(!1),[y,v]=b.useState(!1);b.useEffect(()=>{if(d&&t.length>0){const T=setTimeout(()=>{o(!0),v(!0)},c);return()=>clearTimeout(T)}},[d,t.length,c]);const h=()=>{o(!1),m==null||m()},g=()=>{switch(a){case"sm":return"w-72 max-h-80";case"md":return"w-80 max-h-96";case"lg":return"w-96 max-h-[28rem]";default:return"w-80 max-h-96"}},w=()=>{switch(n){case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";default:return"bottom-4 right-4"}};if(!x||t.length===0)return null;const f=M("admesh-auto-recommendation-widget","fixed z-50 transition-all duration-500 ease-out",w(),g(),{"opacity-0 scale-95 translate-y-2":!y,"opacity-100 scale-100 translate-y-0":y},p),C=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return e.jsx("div",{className:f,style:C,"data-admesh-theme":s==null?void 0:s.mode,children:e.jsxs("div",{className:"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm",children:i}),r&&e.jsxs("p",{className:"text-xs text-blue-100 truncate max-w-48",children:['Based on: "',r,'"']})]})]}),e.jsx("button",{onClick:h,className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.length," intelligent match",t.length>1?"es":""," found"]})]}),e.jsx(xe,{recommendations:t,config:{displayMode:"inline",context:"assistant",maxRecommendations:3,showPoweredBy:!1,autoShow:!0,delayMs:200},theme:s,onRecommendationClick:u})]}),e.jsx("div",{className:"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Powered by AdMesh"}),e.jsx("button",{onClick:h,className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:"Dismiss"})]})})]})})},Y=(t={})=>{const r={mode:"light",primaryColor:"#3b82f6",secondaryColor:"#10b981",accentColor:"#3b82f6",backgroundColor:"#ffffff",surfaceColor:"#f9fafb",borderColor:"#e5e7eb",textColor:"#111827",textSecondaryColor:"#6b7280",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:{small:"12px",base:"14px",large:"16px",title:"18px"},borderRadius:"8px",spacing:{small:"4px",medium:"8px",large:"16px"},shadows:{small:"0 1px 3px rgba(0, 0, 0, 0.1)",medium:"0 4px 6px rgba(0, 0, 0, 0.1)",large:"0 10px 15px rgba(0, 0, 0, 0.1)"},icons:{expandIcon:"▼",collapseIcon:"▲",starIcon:"★",checkIcon:"✓",arrowIcon:"→"}};return{...r,...t,fontSize:{...r.fontSize,...t.fontSize},spacing:{...r.spacing,...t.spacing},shadows:{...r.shadows,...t.shadows},icons:{...r.icons,...t.icons},components:{...r.components,...t.components}}},Je=(t={})=>Y({...{mode:"dark",backgroundColor:"#1f2937",surfaceColor:"#374151",borderColor:"#4b5563",textColor:"#f9fafb",textSecondaryColor:"#9ca3af",shadows:{small:"0 1px 3px rgba(0, 0, 0, 0.3)",medium:"0 4px 6px rgba(0, 0, 0, 0.3)",large:"0 10px 15px rgba(0, 0, 0, 0.3)"}},...t}),Qe={minimal:Y({primaryColor:"#000000",secondaryColor:"#666666",borderRadius:"4px",shadows:{small:"none",medium:"0 1px 3px rgba(0, 0, 0, 0.1)",large:"0 2px 6px rgba(0, 0, 0, 0.1)"}}),vibrant:Y({primaryColor:"#8b5cf6",secondaryColor:"#06b6d4",accentColor:"#f59e0b",borderRadius:"12px",gradients:{primary:"linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)",secondary:"linear-gradient(135deg, #06b6d4 0%, #10b981 100%)",accent:"linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)"}}),corporate:Y({primaryColor:"#1e40af",secondaryColor:"#059669",backgroundColor:"#f8fafc",surfaceColor:"#ffffff",borderColor:"#cbd5e1",borderRadius:"6px",fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif'}),highContrast:Y({primaryColor:"#000000",secondaryColor:"#ffffff",backgroundColor:"#ffffff",surfaceColor:"#f5f5f5",borderColor:"#000000",textColor:"#000000",textSecondaryColor:"#333333",borderRadius:"0px",shadows:{small:"none",medium:"0 0 0 2px #000000",large:"0 0 0 3px #000000"}})},Xe=(...t)=>{const r=Y();return t.reduce((s,i)=>i?Y({...s,...i}):s,r)},Ze=t=>{var s,i,n,a,d,c;const r=getComputedStyle(t);return{primaryColor:((s=r.getPropertyValue("--admesh-primary-color"))==null?void 0:s.trim())||void 0,secondaryColor:((i=r.getPropertyValue("--admesh-secondary-color"))==null?void 0:i.trim())||void 0,backgroundColor:((n=r.getPropertyValue("--admesh-background-color"))==null?void 0:n.trim())||void 0,textColor:((a=r.getPropertyValue("--admesh-text-color"))==null?void 0:a.trim())||void 0,borderRadius:((d=r.getPropertyValue("--admesh-border-radius"))==null?void 0:d.trim())||void 0,fontFamily:((c=r.getPropertyValue("--admesh-font-family"))==null?void 0:c.trim())||void 0}},er="0.2.1",rr={trackingEnabled:!0,debug:!1,theme:{mode:"light",accentColor:"#2563eb"}};exports.AdMeshAutoRecommendationWidget=Ke;exports.AdMeshBadge=ke;exports.AdMeshChatInput=Ne;exports.AdMeshChatInterface=_e;exports.AdMeshChatMessage=je;exports.AdMeshCitationReference=me;exports.AdMeshCitationUnit=we;exports.AdMeshCompareTable=ye;exports.AdMeshConversationSummary=ve;exports.AdMeshConversationalUnit=xe;exports.AdMeshExpandableUnit=He;exports.AdMeshFloatingChat=Ye;exports.AdMeshInlineRecommendation=V;exports.AdMeshLinkTracker=O;exports.AdMeshProductCard=ae;exports.AdMeshSidebar=Ge;exports.AdMeshSidebarContent=Me;exports.AdMeshSidebarHeader=Ce;exports.DEFAULT_CONFIG=rr;exports.VERSION=er;exports.buildAdMeshLink=Be;exports.createAdMeshTheme=Y;exports.createDarkTheme=Je;exports.extractTrackingData=We;exports.getBadgeText=be;exports.getCtaText=$e;exports.getInlineDisclosure=te;exports.getInlineTooltip=se;exports.getLabelTooltip=Z;exports.getPoweredByText=Ve;exports.getRecommendationLabel=H;exports.getSectionDisclosure=Oe;exports.hasHighQualityMatches=De;exports.mergeThemes=Xe;exports.setAdMeshTrackerConfig=ze;exports.themeFromCSSProperties=Ze;exports.themePresets=Qe;exports.useAdMeshStyles=re;exports.useAdMeshTracker=fe;
//# sourceMappingURL=index.js.map
