export { AdMeshProductCard, AdMeshCompareTable, AdMeshBadge, AdMeshLinkTracker, AdMeshExpandableUnit, AdMeshConversationSummary, AdMeshCitationUnit, AdMeshInlineRecommendation, AdMeshConversationalUnit, AdMeshCitationReference, AdMeshFloatingChat, AdMeshChatInterface, AdMeshChatMessage, AdMeshChatInput, AdMeshSidebar, AdMeshSidebarHeader, AdMeshSidebarContent, AdMeshAutoRecommendationWidget } from './components';
export { useAdMeshTracker, setAdMeshTrackerConfig, buildAdMeshLink, extractTrackingData } from './hooks/useAdMeshTracker';
export { useAdMeshStyles } from './hooks/useAdMeshStyles';
export { createAdMeshTheme, createDarkTheme, themePresets, mergeThemes, themeFromCSSProperties } from './utils/themeUtils';
export { getRecommendationLabel, getLabelTooltip, getSectionDisclosure, getInlineDisclosure, getInlineTooltip, getBadgeText, getCtaText, hasHighQualityMatches, getPoweredByText } from './utils/disclosureUtils';
export type { DisclosureConfig } from './utils/disclosureUtils';
export type { AdMeshRecommendation, AdMeshTheme, IntentType, BadgeType, BadgeVariant, BadgeSize, TrackingData, AdMeshProductCardProps, AdMeshCompareTableProps, AdMeshBadgeProps, AdMeshLinkTrackerProps, UseAdMeshTrackerReturn, AgentRecommendationResponse, AdMeshConfig, ConversationalDisplayMode, ConversationContext, ConversationalAdConfig, AdMeshConversationSummaryProps, AdMeshCitationUnitProps, AdMeshInlineRecommendationProps, AdMeshChatInputProps, AdMeshChatMessageProps, AdMeshChatInterfaceProps, AdMeshFloatingChatProps, AdMeshCitationReferenceProps, AdMeshConversationalUnitProps, ChatMessage, SidebarPosition, SidebarSize, SidebarDisplayMode, AdMeshSidebarConfig, AdMeshSidebarProps, SidebarFilters, AdMeshSidebarHeaderProps, AdMeshSidebarContentProps, } from './types/index';
export declare const VERSION = "0.2.1";
export declare const DEFAULT_CONFIG: {
    trackingEnabled: boolean;
    debug: boolean;
    theme: {
        mode: "light";
        accentColor: string;
    };
};
//# sourceMappingURL=index.d.ts.map