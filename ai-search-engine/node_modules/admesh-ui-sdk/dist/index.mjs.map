{"version": 3, "file": "index.mjs", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.js", "../../node_modules/react/cjs/react-jsx-runtime.development.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/classnames/index.js", "../src/hooks/useAdMeshTracker.ts", "../src/components/AdMeshLinkTracker.tsx", "../src/hooks/useAdMeshStyles.ts", "../src/utils/disclosureUtils.ts", "../src/components/AdMeshProductCard.tsx", "../src/components/AdMeshCompareTable.tsx", "../src/components/AdMeshBadge.tsx", "../src/components/AdMeshExpandableUnit.tsx", "../src/components/AdMeshInlineRecommendation.tsx", "../src/components/AdMeshConversationSummary.tsx", "../src/components/AdMeshCitationReference.tsx", "../src/components/AdMeshCitationUnit.tsx", "../src/components/AdMeshConversationalUnit.tsx", "../src/components/AdMeshChatMessage.tsx", "../src/components/AdMeshChatInput.tsx", "../src/components/AdMeshChatInterface.tsx", "../src/components/AdMeshFloatingChat.tsx", "../src/components/AdMeshSidebarHeader.tsx", "../src/components/AdMeshSidebarContent.tsx", "../src/components/AdMeshSidebar.tsx", "../src/components/AdMeshAutoRecommendationWidget.tsx", "../src/utils/themeUtils.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import { useState, useCallback, useMemo } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = useMemo(() => ({ ...globalConfig, ...config }), [config]);\n\n  const log = useCallback((message: string, data?: unknown) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent, log]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (err) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink, err);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  trackingData,\n  className,\n  style\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{\n        cursor: 'pointer',\n        ...style\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import { useEffect } from 'react';\n\n// Complete CSS content as a string - this will be injected automatically\nconst ADMESH_STYLES = `\n/* AdMesh UI SDK - Complete Self-Contained Styles */\n\n/* CSS Reset for AdMesh components */\n.admesh-component, .admesh-component * {\n  box-sizing: border-box;\n}\n\n/* CSS Variables */\n.admesh-component {\n  --admesh-primary: #6366f1;\n  --admesh-primary-hover: #4f46e5;\n  --admesh-secondary: #8b5cf6;\n  --admesh-accent: #06b6d4;\n  --admesh-background: #ffffff;\n  --admesh-surface: #ffffff;\n  --admesh-border: #e2e8f0;\n  --admesh-text: #0f172a;\n  --admesh-text-muted: #64748b;\n  --admesh-text-light: #94a3b8;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --admesh-radius: 0.75rem;\n  --admesh-radius-sm: 0.375rem;\n  --admesh-radius-lg: 1rem;\n  --admesh-radius-xl: 1.5rem;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] {\n  --admesh-background: #111827;\n  --admesh-surface: #1f2937;\n  --admesh-border: #374151;\n  --admesh-text: #f9fafb;\n  --admesh-text-muted: #9ca3af;\n  --admesh-text-light: #6b7280;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);\n}\n\n/* Layout Styles */\n.admesh-layout {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  color: var(--admesh-text);\n  background-color: var(--admesh-background);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  box-shadow: var(--admesh-shadow);\n  border: 1px solid var(--admesh-border);\n}\n\n.admesh-layout__header {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.admesh-layout__title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__subtitle {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n.admesh-layout__cards-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.admesh-layout__more-indicator {\n  text-align: center;\n  padding: 1rem;\n  color: var(--admesh-text-muted);\n  font-size: 0.875rem;\n}\n\n.admesh-layout__empty {\n  text-align: center;\n  padding: 3rem 1rem;\n}\n\n.admesh-layout__empty-content h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__empty-content p {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n/* Product Card Styles */\n.admesh-product-card {\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  transition: all 0.2s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n\n.admesh-product-card:hover {\n  box-shadow: var(--admesh-shadow-lg);\n  transform: translateY(-2px);\n  border-color: var(--admesh-primary);\n}\n\n.admesh-product-card__header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.admesh-product-card__reason {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n  line-height: 1.5;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score {\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.75rem;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.25rem;\n}\n\n.admesh-product-card__match-score-bar {\n  width: 100%;\n  height: 0.375rem;\n  background-color: var(--admesh-border);\n  border-radius: var(--admesh-radius-sm);\n  overflow: hidden;\n}\n\n.admesh-product-card__match-score-fill {\n  height: 100%;\n  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);\n  border-radius: var(--admesh-radius-sm);\n  transition: width 0.3s ease-in-out;\n}\n\n.admesh-product-card__badges {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.5rem;\n  background-color: var(--admesh-primary);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: var(--admesh-radius-sm);\n}\n\n.admesh-product-card__badge--secondary {\n  background-color: var(--admesh-secondary);\n}\n\n.admesh-product-card__keywords {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__keyword {\n  padding: 0.125rem 0.375rem;\n  background-color: var(--admesh-border);\n  color: var(--admesh-text-muted);\n  font-size: 0.75rem;\n  border-radius: var(--admesh-radius-sm);\n}\n\n/* Dark mode specific enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__keyword {\n  background-color: #4b5563;\n  color: #d1d5db;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card:hover {\n  border-color: var(--admesh-primary);\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__button:hover {\n  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));\n}\n\n.admesh-product-card__footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 1.5rem;\n}\n\n/* Mobile-specific sidebar improvements */\n@media (max-width: 640px) {\n  .admesh-sidebar {\n    /* Ensure proper mobile viewport handling */\n    height: 100vh !important;\n    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */\n    max-height: 100vh !important;\n    max-height: 100dvh !important;\n    width: 100vw !important;\n    max-width: 90vw !important;\n    overflow: hidden !important;\n  }\n\n  .admesh-sidebar.relative {\n    height: 100% !important;\n    width: 100% !important;\n    max-width: 100% !important;\n  }\n\n  /* Improve touch scrolling */\n  .admesh-sidebar .overflow-y-auto {\n    -webkit-overflow-scrolling: touch !important;\n    overscroll-behavior: contain !important;\n    scroll-behavior: smooth !important;\n  }\n\n  /* Prevent body scroll when sidebar is open */\n  body:has(.admesh-sidebar[data-mobile-open=\"true\"]) {\n    overflow: hidden !important;\n    position: fixed !important;\n    width: 100% !important;\n  }\n}\n\n/* Tablet improvements */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .admesh-sidebar {\n    max-width: 400px !important;\n  }\n}\n\n/* Mobile responsiveness improvements for all components */\n@media (max-width: 640px) {\n  /* Product cards mobile optimization */\n  .admesh-card {\n    padding: 0.75rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  /* Inline recommendations mobile optimization */\n  .admesh-inline-recommendation {\n    padding: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  /* Conversation summary mobile optimization */\n  .admesh-conversation-summary {\n    padding: 1rem !important;\n  }\n\n  /* Percentage text mobile improvements */\n  .admesh-component .text-xs {\n    font-size: 0.75rem !important;\n    line-height: 1rem !important;\n  }\n\n  .admesh-component .text-sm {\n    font-size: 0.875rem !important;\n    line-height: 1.25rem !important;\n  }\n\n  /* Button mobile improvements */\n  .admesh-component button {\n    padding: 0.375rem 0.75rem !important;\n    font-size: 0.75rem !important;\n    min-height: 2rem !important;\n    touch-action: manipulation !important;\n  }\n\n  /* Badge mobile improvements */\n  .admesh-component .rounded-full {\n    padding: 0.25rem 0.5rem !important;\n    font-size: 0.625rem !important;\n    line-height: 1rem !important;\n  }\n\n  /* Progress bar mobile improvements */\n  .admesh-component .bg-gray-200,\n  .admesh-component .bg-slate-600 {\n    height: 0.25rem !important;\n  }\n\n  /* Flex layout mobile improvements */\n  .admesh-component .flex {\n    flex-wrap: wrap !important;\n  }\n\n  .admesh-component .gap-2 {\n    gap: 0.375rem !important;\n  }\n\n  .admesh-component .gap-3 {\n    gap: 0.5rem !important;\n  }\n}\n\n.admesh-product-card__button {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: none;\n  border-radius: var(--admesh-radius);\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  text-decoration: none;\n}\n\n.admesh-product-card__button:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--admesh-shadow-lg);\n}\n\n/* Utility Classes */\n.admesh-text-xs { font-size: 0.75rem; }\n.admesh-text-sm { font-size: 0.875rem; }\n.admesh-text-base { font-size: 1rem; }\n.admesh-text-lg { font-size: 1.125rem; }\n.admesh-text-xl { font-size: 1.25rem; }\n\n.admesh-font-medium { font-weight: 500; }\n.admesh-font-semibold { font-weight: 600; }\n.admesh-font-bold { font-weight: 700; }\n\n.admesh-text-muted { color: var(--admesh-text-muted); }\n\n/* Comparison Table Styles */\n.admesh-compare-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  overflow: hidden;\n}\n\n.admesh-compare-table th,\n.admesh-compare-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid var(--admesh-border);\n}\n\n.admesh-compare-table th {\n  background-color: var(--admesh-background);\n  font-weight: 600;\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table td {\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table tr:hover {\n  background-color: var(--admesh-border);\n}\n\n/* Dark mode table enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table th {\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table tr:hover {\n  background-color: #4b5563;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admesh-layout {\n    padding: 1rem;\n  }\n\n  .admesh-layout__cards-grid {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .admesh-product-card {\n    padding: 1rem;\n  }\n\n  .admesh-compare-table {\n    font-size: 0.75rem;\n  }\n\n  .admesh-compare-table th,\n  .admesh-compare-table td {\n    padding: 0.5rem;\n  }\n}\n\n/* Essential Utility Classes for Self-Contained SDK - High Specificity */\n.admesh-component .relative { position: relative !important; }\n.admesh-component .absolute { position: absolute !important; }\n.admesh-component .flex { display: flex !important; }\n.admesh-component .inline-flex { display: inline-flex !important; }\n.admesh-component .grid { display: grid !important; }\n.admesh-component .hidden { display: none !important; }\n.admesh-component .block { display: block !important; }\n.admesh-component .inline-block { display: inline-block !important; }\n\n/* Flexbox utilities */\n.admesh-component .flex-col { flex-direction: column !important; }\n.admesh-component .flex-row { flex-direction: row !important; }\n.admesh-component .flex-wrap { flex-wrap: wrap !important; }\n.admesh-component .items-center { align-items: center !important; }\n.admesh-component .items-start { align-items: flex-start !important; }\n.admesh-component .items-end { align-items: flex-end !important; }\n.admesh-component .justify-center { justify-content: center !important; }\n.admesh-component .justify-between { justify-content: space-between !important; }\n.admesh-component .justify-end { justify-content: flex-end !important; }\n.admesh-component .flex-1 { flex: 1 1 0% !important; }\n.admesh-component .flex-shrink-0 { flex-shrink: 0 !important; }\n\n/* Grid utilities */\n.admesh-component .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\n.admesh-component .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n.admesh-component .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n\n/* Spacing utilities */\n.admesh-component .gap-1 { gap: 0.25rem; }\n.admesh-component .gap-2 { gap: 0.5rem; }\n.admesh-component .gap-3 { gap: 0.75rem; }\n.admesh-component .gap-4 { gap: 1rem; }\n.admesh-component .gap-6 { gap: 1.5rem; }\n.admesh-component .gap-8 { gap: 2rem; }\n\n/* Padding utilities */\n.admesh-component .p-1 { padding: 0.25rem; }\n.admesh-component .p-2 { padding: 0.5rem; }\n.admesh-component .p-3 { padding: 0.75rem; }\n.admesh-component .p-4 { padding: 1rem; }\n.admesh-component .p-5 { padding: 1.25rem; }\n.admesh-component .p-6 { padding: 1.5rem; }\n.admesh-component .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }\n.admesh-component .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n.admesh-component .px-4 { padding-left: 1rem; padding-right: 1rem; }\n.admesh-component .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\n.admesh-component .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n.admesh-component .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n.admesh-component .pt-2 { padding-top: 0.5rem; }\n.admesh-component .pt-3 { padding-top: 0.75rem; }\n.admesh-component .pb-2 { padding-bottom: 0.5rem; }\n.admesh-component .pb-3 { padding-bottom: 0.75rem; }\n\n/* Margin utilities */\n.admesh-component .m-0 { margin: 0; }\n.admesh-component .mb-1 { margin-bottom: 0.25rem; }\n.admesh-component .mb-2 { margin-bottom: 0.5rem; }\n.admesh-component .mb-3 { margin-bottom: 0.75rem; }\n.admesh-component .mb-4 { margin-bottom: 1rem; }\n.admesh-component .mb-6 { margin-bottom: 1.5rem; }\n.admesh-component .mt-1 { margin-top: 0.25rem; }\n.admesh-component .mt-2 { margin-top: 0.5rem; }\n.admesh-component .mt-4 { margin-top: 1rem; }\n.admesh-component .mt-6 { margin-top: 1.5rem; }\n.admesh-component .mt-auto { margin-top: auto; }\n.admesh-component .ml-1 { margin-left: 0.25rem; }\n.admesh-component .mr-1 { margin-right: 0.25rem; }\n.admesh-component .mr-2 { margin-right: 0.5rem; }\n\n/* Width and height utilities */\n.admesh-component .w-2 { width: 0.5rem; }\n.admesh-component .w-3 { width: 0.75rem; }\n.admesh-component .w-4 { width: 1rem; }\n.admesh-component .w-5 { width: 1.25rem; }\n.admesh-component .w-6 { width: 1.5rem; }\n.admesh-component .w-full { width: 100%; }\n.admesh-component .w-fit { width: fit-content; }\n.admesh-component .h-2 { height: 0.5rem; }\n.admesh-component .h-3 { height: 0.75rem; }\n.admesh-component .h-4 { height: 1rem; }\n.admesh-component .h-5 { height: 1.25rem; }\n.admesh-component .h-6 { height: 1.5rem; }\n.admesh-component .h-full { height: 100%; }\n.admesh-component .min-w-0 { min-width: 0px; }\n\n/* Border utilities */\n.admesh-component .border { border-width: 1px; }\n.admesh-component .border-t { border-top-width: 1px; }\n.admesh-component .border-gray-100 { border-color: #f3f4f6; }\n.admesh-component .border-gray-200 { border-color: #e5e7eb; }\n.admesh-component .border-gray-300 { border-color: #d1d5db; }\n.admesh-component .border-blue-200 { border-color: #bfdbfe; }\n.admesh-component .border-green-200 { border-color: #bbf7d0; }\n\n/* Border radius utilities */\n.admesh-component .rounded { border-radius: 0.25rem !important; }\n.admesh-component .rounded-md { border-radius: 0.375rem !important; }\n.admesh-component .rounded-lg { border-radius: 0.5rem !important; }\n.admesh-component .rounded-xl { border-radius: 0.75rem !important; }\n.admesh-component .rounded-full { border-radius: 9999px !important; }\n\n/* Background utilities */\n.admesh-component .bg-white { background-color: #ffffff; }\n.admesh-component .bg-gray-50 { background-color: #f9fafb; }\n.admesh-component .bg-gray-100 { background-color: #f3f4f6; }\n.admesh-component .bg-blue-50 { background-color: #eff6ff; }\n.admesh-component .bg-blue-100 { background-color: #dbeafe; }\n.admesh-component .bg-green-100 { background-color: #dcfce7; }\n.admesh-component .bg-green-500 { background-color: #22c55e; }\n.admesh-component .bg-blue-500 { background-color: #3b82f6; }\n\n/* Gradients */\n.admesh-component .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\n.admesh-component .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\n.admesh-component .from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }\n.admesh-component .to-gray-50 { --tw-gradient-to: #f9fafb; }\n.admesh-component .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0)); }\n.admesh-component .to-pink-500 { --tw-gradient-to: #ec4899; }\n.admesh-component .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }\n.admesh-component .to-blue-500 { --tw-gradient-to: #3b82f6; }\n\n/* Text utilities */\n.admesh-component .text-xs { font-size: 0.75rem; line-height: 1rem; }\n.admesh-component .text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.admesh-component .text-base { font-size: 1rem; line-height: 1.5rem; }\n.admesh-component .text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.admesh-component .text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.admesh-component .font-medium { font-weight: 500; }\n.admesh-component .font-semibold { font-weight: 600; }\n.admesh-component .font-bold { font-weight: 700; }\n.admesh-component .leading-relaxed { line-height: 1.625; }\n\n/* Text colors */\n.admesh-component .text-white { color: #ffffff; }\n.admesh-component .text-gray-400 { color: #9ca3af; }\n.admesh-component .text-gray-500 { color: #6b7280; }\n.admesh-component .text-gray-600 { color: #4b5563; }\n.admesh-component .text-gray-700 { color: #374151; }\n.admesh-component .text-gray-800 { color: #1f2937; }\n.admesh-component .text-blue-600 { color: #2563eb; }\n.admesh-component .text-blue-700 { color: #1d4ed8; }\n.admesh-component .text-green-700 { color: #15803d; }\n\n/* Shadow utilities */\n.admesh-component .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }\n.admesh-component .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n\n/* Transition utilities */\n.admesh-component .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .duration-200 { transition-duration: 200ms; }\n.admesh-component .duration-300 { transition-duration: 300ms; }\n\n/* Transform utilities */\n.admesh-component .hover\\\\:-translate-y-1:hover { transform: translateY(-0.25rem); }\n.admesh-component .hover\\\\:scale-105:hover { transform: scale(1.05); }\n\n/* Hover utilities */\n.admesh-component .hover\\\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n.admesh-component .hover\\\\:bg-gray-100:hover { background-color: #f3f4f6; }\n.admesh-component .hover\\\\:text-blue-800:hover { color: #1e40af; }\n\n/* Cursor utilities */\n.admesh-component .cursor-pointer { cursor: pointer; }\n\n/* Overflow utilities */\n.admesh-component .overflow-hidden { overflow: hidden; }\n.admesh-component .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\n\n/* Text decoration */\n.admesh-component .underline { text-decoration-line: underline; }\n\n/* Whitespace */\n.admesh-component .whitespace-nowrap { white-space: nowrap; }\n\n/* Dark mode utilities */\n@media (prefers-color-scheme: dark) {\n  .admesh-component .dark\\\\:bg-slate-800 { background-color: #1e293b; }\n  .admesh-component .dark\\\\:bg-slate-900 { background-color: #0f172a; }\n  .admesh-component .dark\\\\:border-slate-700 { border-color: #334155; }\n  .admesh-component .dark\\\\:text-white { color: #ffffff; }\n  .admesh-component .dark\\\\:text-gray-200 { color: #e5e7eb; }\n  .admesh-component .dark\\\\:text-gray-300 { color: #d1d5db; }\n  .admesh-component .dark\\\\:text-gray-400 { color: #9ca3af; }\n  .admesh-component .dark\\\\:text-blue-400 { color: #60a5fa; }\n}\n\n/* Responsive utilities */\n@media (min-width: 640px) {\n  .admesh-component .sm\\\\:p-5 { padding: 1.25rem; }\n  .admesh-component .sm\\\\:text-base { font-size: 1rem; line-height: 1.5rem; }\n  .admesh-component .sm\\\\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n  .admesh-component .sm\\\\:flex-row { flex-direction: row; }\n  .admesh-component .sm\\\\:items-center { align-items: center; }\n  .admesh-component .sm\\\\:justify-between { justify-content: space-between; }\n}\n\n@media (min-width: 768px) {\n  .admesh-component .md\\\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n}\n\n@media (min-width: 1024px) {\n  .admesh-component .lg\\\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n  .admesh-component .lg\\\\:col-span-1 { grid-column: span 1 / span 1; }\n}\n`;\n\nlet stylesInjected = false;\n\nexport const useAdMeshStyles = () => {\n  useEffect(() => {\n    if (stylesInjected) return;\n\n    // Create and inject styles\n    const styleElement = document.createElement('style');\n    styleElement.id = 'admesh-ui-sdk-styles';\n    styleElement.textContent = ADMESH_STYLES;\n    \n    // Check if styles are already injected\n    if (!document.getElementById('admesh-ui-sdk-styles')) {\n      document.head.appendChild(styleElement);\n      stylesInjected = true;\n    }\n\n    // Cleanup function\n    return () => {\n      const existingStyle = document.getElementById('admesh-ui-sdk-styles');\n      if (existingStyle && document.head.contains(existingStyle)) {\n        document.head.removeChild(existingStyle);\n        stylesInjected = false;\n      }\n    };\n  }, []);\n};\n", "import type { AdMeshRecommendation } from '../types/index';\n\n/**\n * Utility functions for generating compliant disclosure labels and tooltips\n */\n\nexport interface DisclosureConfig {\n  showTooltips?: boolean;\n  compactMode?: boolean;\n  customLabels?: {\n    smartPick?: string;\n    partnerMatch?: string;\n    promotedOption?: string;\n    relatedOption?: string;\n  };\n}\n\n/**\n * Generate appropriate label based on match score and recommendation quality\n */\nexport const getRecommendationLabel = (\n  recommendation: AdMeshRecommendation,\n  config: DisclosureConfig = {}\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n  const customLabels = config.customLabels || {};\n\n  // High match score (>0.8)\n  if (matchScore >= 0.8) {\n    return customLabels.smartPick || 'Smart Pick';\n  }\n  \n  // Medium match score (0.6-0.8)\n  if (matchScore >= 0.6) {\n    return customLabels.partnerMatch || 'Partner Match';\n  }\n  \n  // Lower match score (<0.6)\n  if (matchScore >= 0.3) {\n    return customLabels.promotedOption || 'Promoted Option';\n  }\n  \n  // Very low match - related option\n  return customLabels.relatedOption || 'Related Option';\n};\n\n/**\n * Generate tooltip text for recommendation labels\n */\nexport const getLabelTooltip = (\n  recommendation: AdMeshRecommendation,\n  _label: string\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (matchScore >= 0.8) {\n    return \"This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query.\";\n  }\n  \n  if (matchScore >= 0.6) {\n    return \"Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals.\";\n  }\n  \n  if (matchScore >= 0.3) {\n    return \"This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions.\";\n  }\n  \n  return \"This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals.\";\n};\n\n/**\n * Generate section-level disclosure text\n */\nexport const getSectionDisclosure = (\n  hasHighMatches: boolean = true,\n  isExpanded: boolean = false\n): string => {\n  if (!hasHighMatches) {\n    return \"Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.\";\n  }\n  \n  if (isExpanded) {\n    return \"These curated recommendations are from partners who compensate us for referrals.\";\n  }\n  \n  return \"Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs.\";\n};\n\n/**\n * Generate inline disclosure text for product cards\n */\nexport const getInlineDisclosure = (\n  recommendation: AdMeshRecommendation,\n  compact: boolean = false\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (compact) {\n    return \"Promoted\";\n  }\n\n  if (matchScore >= 0.8) {\n    return \"Smart Recommendation\";\n  }\n\n  if (matchScore >= 0.6) {\n    return \"Partner Match\";\n  }\n\n  return \"Promoted\";\n};\n\n/**\n * Generate detailed tooltip for inline disclosures\n */\nexport const getInlineTooltip = (): string => {\n  return \"We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.\";\n};\n\n/**\n * Generate badge text without emojis\n */\nexport const getBadgeText = (badgeType: string): string => {\n  const badgeMap: Record<string, string> = {\n    'Top Match': 'Top Match',\n    'Smart Pick': 'Smart Pick',\n    'Perfect Fit': 'Perfect Fit',\n    'Great Match': 'Great Match',\n    'Recommended': 'Recommended',\n    'Good Fit': 'Good Fit',\n    'Featured': 'Featured',\n    'Popular Choice': 'Popular Choice',\n    'Premium Pick': 'Premium Pick',\n    'Free Tier': 'Free Tier',\n    'AI Powered': 'AI Powered',\n    'Popular': 'Popular',\n    'New': 'New',\n    'Trial Available': 'Trial Available',\n    'Related Option': 'Related Option',\n    'Alternative Solution': 'Alternative Solution',\n    'Expanded Match': 'Expanded Match'\n  };\n  \n  return badgeMap[badgeType] || badgeType;\n};\n\n/**\n * Generate appropriate CTA text\n */\nexport const getCtaText = (\n  recommendation: AdMeshRecommendation,\n  context: 'button' | 'link' = 'button'\n): string => {\n  const productName = recommendation.recommendation_title || recommendation.title;\n  \n  if (context === 'link') {\n    return productName;\n  }\n  \n  // For buttons, use action-oriented text\n  if (recommendation.trial_days && recommendation.trial_days > 0) {\n    return `Try ${productName}`;\n  }\n\n  return `Learn More`;\n};\n\n/**\n * Check if recommendations have high match scores\n */\nexport const hasHighQualityMatches = (recommendations: AdMeshRecommendation[]): boolean => {\n  return recommendations.some(rec => (rec.intent_match_score || 0) >= 0.8);\n};\n\n/**\n * Generate compliant powered-by text\n */\nexport const getPoweredByText = (compact: boolean = false): string => {\n  if (compact) {\n    return \"Powered by AdMesh\";\n  }\n  \n  return \"Recommendations powered by AdMesh\";\n};\n", "import React, { useMemo, useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText\n} from '../utils/disclosureUtils';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = false,\n  showBadges = true,\n  variation = 'default',\n  expandable = false,\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // State for expandable variations\n  const [isExpanded, setIsExpanded] = useState(false);\n  // Generate badges based on recommendation data using compliant labels\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n\n    // Add primary recommendation label based on match score\n    const primaryLabel = getRecommendationLabel(recommendation);\n    if (primaryLabel === 'Smart Pick') {\n      generatedBadges.push('Top Match'); // Map to existing badge type\n    }\n\n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n\n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword =>\n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n\n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n\n    // Add badges from the response if available\n    if (recommendation.badges && recommendation.badges.length > 0) {\n      recommendation.badges.forEach(badge => {\n        // Only add if it's a valid BadgeType and not already included\n        if (['Top Match', 'Free Tier', 'AI Powered', 'Popular', 'New', 'Trial Available'].includes(badge) &&\n            !generatedBadges.includes(badge as BadgeType)) {\n          generatedBadges.push(badge as BadgeType);\n        }\n      });\n    }\n\n    // Note: is_open_source field has been removed\n\n    return generatedBadges;\n  }, [recommendation]);\n\n  // Get compliant disclosure text\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get content based on variation\n  const getVariationContent = () => {\n    const variations = recommendation.content_variations;\n\n    if (variation === 'simple') {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title,\n        isSimple: true\n      };\n    } else if (variation === 'question' && variations?.question) {\n      return {\n        title: variations.question.cta || recommendation.recommendation_title || recommendation.title,\n        description: variations.question.text,\n        ctaText: variations.question.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else if (variation === 'statement' && variations?.statement) {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: variations.statement.text,\n        ctaText: variations.statement.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else {\n      // Default variation\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title\n      };\n    }\n  };\n\n  const content = getVariationContent();\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1',\n    className\n  );\n\n  const cardStyle = theme ? {\n    '--admesh-primary': theme.primaryColor || theme.accentColor || '#3b82f6',\n    '--admesh-secondary': theme.secondaryColor || '#10b981',\n    '--admesh-accent': theme.accentColor || '#3b82f6',\n    '--admesh-background': theme.backgroundColor,\n    '--admesh-surface': theme.surfaceColor,\n    '--admesh-border': theme.borderColor,\n    '--admesh-text': theme.textColor,\n    '--admesh-text-secondary': theme.textSecondaryColor,\n    '--admesh-radius': theme.borderRadius || '12px',\n    '--admesh-shadow-sm': theme.shadows?.small,\n    '--admesh-shadow-md': theme.shadows?.medium,\n    '--admesh-shadow-lg': theme.shadows?.large,\n    '--admesh-spacing-sm': theme.spacing?.small,\n    '--admesh-spacing-md': theme.spacing?.medium,\n    '--admesh-spacing-lg': theme.spacing?.large,\n    '--admesh-font-size-sm': theme.fontSize?.small,\n    '--admesh-font-size-base': theme.fontSize?.base,\n    '--admesh-font-size-lg': theme.fontSize?.large,\n    '--admesh-font-size-title': theme.fontSize?.title,\n    fontFamily: theme.fontFamily\n  } as React.CSSProperties : undefined;\n\n  // Render different layouts based on variation\n  if (variation === 'simple') {\n    // Simple inline ad format (replaces AdMeshSimpleAd)\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-simple-ad\",\n          \"inline-block text-sm leading-relaxed\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Recommendation label */}\n        <span\n          style={{\n            fontSize: '11px',\n            fontWeight: '600',\n            color: theme?.accentColor || '#2563eb',\n            backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            marginRight: '8px'\n          }}\n          title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n        >\n          {getRecommendationLabel(recommendation)}\n        </span>\n\n        {/* Main content */}\n        <span\n          style={{\n            color: theme?.mode === 'dark' ? '#f3f4f6' : '#374151',\n            marginRight: '4px'\n          }}\n        >\n          {content.description}{' '}\n        </span>\n\n        {/* CTA Link */}\n        <AdMeshLinkTracker\n          adId={recommendation.ad_id}\n          admeshLink={recommendation.admesh_link}\n          productId={recommendation.product_id}\n          trackingData={{\n            title: recommendation.title,\n            matchScore: recommendation.intent_match_score,\n            component: 'simple_ad_cta'\n          }}\n        >\n          <span\n            style={{\n              color: theme?.accentColor || '#2563eb',\n              textDecoration: 'underline',\n              cursor: 'pointer',\n              fontSize: 'inherit',\n              fontFamily: 'inherit'\n            }}\n          >\n            {content.ctaText}\n          </span>\n        </AdMeshLinkTracker>\n\n        {/* Disclosure */}\n        <span\n          style={{\n            fontSize: '10px',\n            color: theme?.mode === 'dark' ? '#9ca3af' : '#6b7280',\n            marginLeft: '8px'\n          }}\n          title={inlineTooltip}\n        >\n          ({inlineDisclosure})\n        </span>\n      </div>\n    );\n  }\n\n  if (variation === 'question' || variation === 'statement') {\n    // Expandable layout - starts simple, can expand to full card (only if expandable=true)\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-expandable-variation transition-all duration-300\",\n          expandable && isExpanded\n            ? \"p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg\"\n            : \"p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {!expandable || !isExpanded ? (\n          // Simple inline layout with top label (or non-expandable layout)\n          <>\n            {/* Recommendation label at top */}\n            <div className=\"mb-2\">\n              <span\n                style={{\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: theme?.accentColor || '#2563eb',\n                  backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  padding: '2px 6px',\n                  borderRadius: '4px'\n                }}\n                title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n              >\n                {getRecommendationLabel(recommendation)}\n              </span>\n            </div>\n\n            <div className=\"flex items-center justify-between gap-3\">\n              {/* Content */}\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">\n                  {content.description}{' '}\n                  <AdMeshLinkTracker\n                    adId={recommendation.ad_id}\n                    admeshLink={recommendation.admesh_link}\n                    productId={recommendation.product_id}\n                    trackingData={{\n                      title: recommendation.title,\n                      matchScore: recommendation.intent_match_score,\n                      component: 'simple_variation_cta'\n                    }}\n                  >\n                    <span\n                      className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors\"\n                    >\n                      {content.ctaText}\n                    </span>\n                  </AdMeshLinkTracker>\n                </p>\n              </div>\n\n              {/* Expand Button - only show if expandable is true */}\n              {expandable && (\n                <div className=\"flex items-center gap-3 flex-shrink-0\">\n                  <button\n                    onClick={() => setIsExpanded(true)}\n                    className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600\"\n                    title=\"View more details\"\n                  >\n                    <span>More Details</span>\n                    <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </button>\n              </div>\n              )}\n            </div>\n\n\n          </>\n        ) : (\n          // Expanded full card layout (same as default variation)\n          <div\n            className=\"h-full flex flex-col\"\n            style={cardStyle}\n            data-admesh-theme={theme?.mode}\n          >\n            {/* Header with badges, title, and collapse button */}\n            <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0\">\n                {showBadges && badges.includes('Top Match') && (\n                  <span\n                    className=\"text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md\"\n                    style={{\n                      backgroundColor: theme?.primaryColor || theme?.accentColor || '#f59e0b',\n                      borderRadius: theme?.borderRadius || '9999px'\n                    }}\n                    title={getLabelTooltip(recommendation, 'Smart Pick')}\n                  >\n                    {getBadgeText('Top Match')}\n                  </span>\n                )}\n                <div className=\"flex items-center gap-2 min-w-0\">\n                  {recommendation.product_logo && (\n                    <img\n                      src={recommendation.product_logo.url}\n                      alt={`${recommendation.title} logo`}\n                      className=\"w-6 h-6 rounded flex-shrink-0\"\n                      onError={(e) => {\n                        // Hide image if it fails to load\n                        (e.target as HTMLImageElement).style.display = 'none';\n                      }}\n                    />\n                  )}\n                  <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n                    {content.title}\n                  </h4>\n                </div>\n              </div>\n\n              <div className=\"flex gap-3 flex-shrink-0\">\n                <button\n                  onClick={() => setIsExpanded(false)}\n                  className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\"\n                  title=\"Show less details\"\n                >\n                  <span>Less Details</span>\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                  </svg>\n                </button>\n                <AdMeshLinkTracker\n                  adId={recommendation.ad_id}\n                  admeshLink={recommendation.admesh_link}\n                  productId={recommendation.product_id}\n                  trackingData={{\n                    title: recommendation.title,\n                    matchScore: recommendation.intent_match_score,\n                    component: 'product_card_cta'\n                  }}\n                >\n                  <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                    {variation === 'question' ? 'Try' : 'Visit'} {content.ctaText}\n                    <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                    </svg>\n                  </button>\n                </AdMeshLinkTracker>\n              </div>\n            </div>\n\n            {/* Product Description/Reason */}\n            <div className=\"mb-6\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n                {content.description}\n              </p>\n            </div>\n\n            {/* Disclosure */}\n            <div className=\"mb-6\">\n              <p\n                className=\"text-xs text-gray-500 dark:text-gray-400 leading-relaxed\"\n                title={inlineTooltip}\n              >\n                {inlineDisclosure}\n              </p>\n            </div>\n\n            {/* Match Score */}\n            {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                  <span className=\"font-medium\">Match Score</span>\n                  <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n                  <div\n                    className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                    style={{ width: `${matchScorePercentage}%` }}\n                  />\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n              {recommendation.pricing && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                  {recommendation.pricing}\n                </span>\n              )}\n\n              {recommendation.trial_days && recommendation.trial_days > 0 && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                  </svg>\n                  {recommendation.trial_days}-day trial\n                </span>\n              )}\n            </div>\n\n            {/* Features */}\n            {recommendation.features && recommendation.features.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  ✨ Key Features\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.features.slice(0, 4).map((feature, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      {feature}\n                    </span>\n                  ))}\n                  {recommendation.features.length > 4 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.features.length - 4} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Integrations */}\n            {recommendation.integrations && recommendation.integrations.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  🔗 Integrations\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                      </svg>\n                      {integration}\n                    </span>\n                  ))}\n                  {recommendation.integrations.length > 3 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.integrations.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Powered by AdMesh branding */}\n            <div className=\"flex justify-end mt-auto pt-2\">\n              <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                Powered by AdMesh\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  // Default full product card layout\n  return (\n    <div\n      className={cardClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.productCard,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div\n        className=\"h-full flex flex-col\"\n        style={cardStyle}\n      >\n        {/* Recommendation label at top */}\n        <div className=\"mb-3\">\n          <span\n            style={{\n              fontSize: '11px',\n              fontWeight: '600',\n              color: theme?.accentColor || '#2563eb',\n              backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n              padding: '2px 6px',\n              borderRadius: '4px'\n            }}\n            title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n          >\n            {getRecommendationLabel(recommendation)}\n          </span>\n        </div>\n\n        {/* Header with title */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n          <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n            {recommendation.product_logo && (\n              <img\n                src={recommendation.product_logo.url}\n                alt={`${recommendation.title} logo`}\n                className=\"w-6 h-6 rounded flex-shrink-0\"\n                onError={(e) => {\n                  // Hide image if it fails to load\n                  (e.target as HTMLImageElement).style.display = 'none';\n                }}\n              />\n            )}\n            <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n              {content.title}\n            </h4>\n          </div>\n\n          <div className=\"flex gap-2 flex-shrink-0\">\n            <AdMeshLinkTracker\n              adId={recommendation.ad_id}\n              admeshLink={recommendation.admesh_link}\n              productId={recommendation.product_id}\n              trackingData={{\n                title: recommendation.title,\n                matchScore: recommendation.intent_match_score,\n                component: 'product_card_cta'\n              }}\n            >\n              <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                Visit {content.ctaText}\n                <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                </svg>\n              </button>\n            </AdMeshLinkTracker>\n          </div>\n        </div>\n\n        {/* Product Description/Reason */}\n        <div className=\"mb-6\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n            {content.description}\n          </p>\n        </div>\n\n        {/* Match Score */}\n        {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n              <span className=\"font-medium\">Match Score</span>\n              <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n              <div\n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                style={{ width: `${matchScorePercentage}%` }}\n              />\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n          {recommendation.pricing && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n              {recommendation.pricing}\n            </span>\n          )}\n\n          {recommendation.trial_days && recommendation.trial_days > 0 && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n              </svg>\n              {recommendation.trial_days}-day trial\n            </span>\n          )}\n        </div>\n\n        {/* Features */}\n        {recommendation.features && recommendation.features.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              ✨ Key Features\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.features.slice(0, 4).map((feature, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {feature}\n                </span>\n              ))}\n              {recommendation.features.length > 4 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.features.length - 4} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Integrations */}\n        {recommendation.integrations && recommendation.integrations.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              🔗 Integrations\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  {integration}\n                </span>\n              ))}\n              {recommendation.integrations.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.integrations.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n\n\n\n\n\n\n\n\n        {/* Footer section */}\n        <div className=\"mt-auto pt-3 border-t border-gray-100 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n            <span title={inlineTooltip}>\n              {inlineDisclosure}\n            </span>\n            <span className=\"text-gray-400 dark:text-gray-500\">\n              Powered by AdMesh\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-compare-layout',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div\n        className={containerClasses}\n        style={{\n          ...containerStyle,\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.compareTable,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        <div className=\"p-8 text-center text-gray-500 dark:text-gray-400\">\n          <p>No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.compareTable,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center gap-2 mb-2\">\n            <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200\">\n              Smart Comparison\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {productsToCompare.length} intelligent matches found\n          </p>\n        </div>\n\n        {/* Product Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {productsToCompare.map((product, index) => (\n            <div\n              key={product.product_id || index}\n              className=\"relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow\"\n            >\n              {/* Product Header */}\n              <div className=\"flex justify-between items-start mb-3\">\n                <div className=\"flex items-center gap-2\">\n                  {index === 0 && (\n                    <span className=\"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full\">\n                      Top Match\n                    </span>\n                  )}\n                  <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                    #{index + 1}\n                  </span>\n                </div>\n                {showMatchScores && (\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap\">\n                    {Math.round(product.intent_match_score * 100)}% match\n                  </div>\n                )}\n              </div>\n\n              {/* Product Title */}\n              <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 mb-2\">\n                {product.title}\n              </h4>\n\n              {/* Match Score */}\n              {showMatchScores && (\n                <div className=\"mb-3\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Match Score</span>\n                    <span className=\"whitespace-nowrap\">{Math.round(product.intent_match_score * 100)}% match</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden\">\n                    <div\n                      className=\"bg-black h-1.5\"\n                      style={{ width: `${Math.round(product.intent_match_score * 100)}%` }}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Pricing and Trial Info */}\n              <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n                {product.pricing && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                    {product.pricing}\n                  </span>\n                )}\n\n\n\n                {product.trial_days && product.trial_days > 0 && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                    </svg>\n                    {product.trial_days}-day trial\n                  </span>\n                )}\n              </div>\n\n              {/* Features */}\n              {showFeatures && product.features && product.features.length > 0 && (\n                <div className=\"mb-3\">\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    Key Features:\n                  </div>\n                  <div className=\"flex flex-wrap gap-1.5\">\n                    {product.features.slice(0, 4).map((feature, j) => (\n                      <span\n                        key={j}\n                        className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300\"\n                      >\n                        <svg className=\"h-3 w-3 mr-0.5 inline text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        {feature}\n                      </span>\n                    ))}\n                    {(product.features.length || 0) > 4 && (\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 italic\">\n                        +{product.features.length - 4} more\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Visit Button */}\n              <AdMeshLinkTracker\n                adId={product.ad_id}\n                admeshLink={product.admesh_link}\n                productId={product.product_id}\n                trackingData={{\n                  title: product.title,\n                  matchScore: product.intent_match_score,\n                  component: 'compare_table_cta'\n                }}\n              >\n                <button className=\"w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors\">\n                  Visit Offer\n                  <svg className=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                  </svg>\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          ))}\n        </div>\n\n        {/* Powered by AdMesh branding */}\n        <div className=\"flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50\">\n          <span className=\"flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500\">\n            <svg className=\"w-3 h-3 text-indigo-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"font-medium\">Powered by</span>\n            <span className=\"font-semibold text-indigo-600 dark:text-indigo-400\">AdMesh</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using clean Unicode symbols)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '★',\n  'Free Tier': '◆',\n  'AI Powered': '◉',\n  'Popular': '▲',\n  'New': '●',\n  'Trial Available': '◈'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className,\n  style\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span\n      className={badgeClasses}\n      style={style}\n    >\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import React, { useState } from 'react';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport interface AdMeshExpandableUnitProps {\n  /** Product recommendation data */\n  recommendation: AdMeshRecommendation;\n  /** Theme configuration */\n  theme?: AdMeshTheme;\n  /** Custom CSS class name */\n  className?: string;\n  /** Custom inline styles */\n  style?: React.CSSProperties;\n  /** Show \"powered by AdMesh\" branding */\n  showPoweredBy?: boolean;\n  /** Initial expanded state */\n  initialExpanded?: boolean;\n  /** Custom sections to display */\n  sections?: {\n    title: string;\n    description: string;\n    icon?: string;\n  }[];\n  /** Custom call-to-action text */\n  ctaText?: string;\n  /** Show collapse/expand functionality */\n  collapsible?: boolean;\n}\n\n/**\n * AdMeshExpandableUnit - A comprehensive ad unit with expandable sections\n * \n * Similar to the Temporal ad format, this component displays:\n * - Header with product name and sponsor info\n * - Multiple expandable sections with descriptions\n * - Primary call-to-action button\n * - Optional powered by branding\n */\nexport const AdMeshExpandableUnit: React.FC<AdMeshExpandableUnitProps> = ({\n  recommendation,\n  theme = { mode: 'light' },\n  className = '',\n  style,\n  showPoweredBy = true,\n  initialExpanded = false,\n  sections,\n  ctaText,\n  collapsible = true\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  const [isExpanded, setIsExpanded] = useState(initialExpanded);\n\n\n\n  const handleToggleExpand = () => {\n    if (collapsible) {\n      setIsExpanded(!isExpanded);\n    }\n  };\n\n  // Use feature sections from API if available, otherwise use default sections\n  const apiFeatureSections = recommendation.feature_sections || [];\n\n  const defaultSections = [\n    {\n      title: 'Documentation',\n      description: `Learn more about ${recommendation.recommendation_title || recommendation.title}. Start exploring the features and capabilities.`,\n      icon: '◆'\n    },\n    {\n      title: 'Talk To An Expert',\n      description: `Ready to learn more about ${recommendation.recommendation_title || recommendation.title}? Reach out to a platform specialist for personalized guidance.`,\n      icon: '◉'\n    },\n    {\n      title: `${recommendation.recommendation_title || recommendation.title} Features`,\n      description: recommendation.recommendation_description || recommendation.description || `${recommendation.recommendation_title || recommendation.title} offers comprehensive solutions for your needs. Discover the full potential.`,\n      icon: '▲'\n    },\n    {\n      title: 'How it Works',\n      description: `Learn how to get started with ${recommendation.recommendation_title || recommendation.title}. Begin your journey today.`,\n      icon: '●'\n    }\n  ];\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Prioritize: custom sections > API feature sections > default sections\n  const displaySections = sections || (apiFeatureSections.length > 0 ? apiFeatureSections : defaultSections);\n  const displayCtaText = ctaText || `Try ${recommendation.recommendation_title || recommendation.title}`;\n\n  // Clean, professional color scheme with customization support\n  const colors = {\n    background: theme.backgroundColor || (theme.mode === 'dark' ? '#1f2937' : '#ffffff'),\n    surface: theme.surfaceColor || (theme.mode === 'dark' ? '#374151' : '#f9fafb'),\n    border: theme.borderColor || (theme.mode === 'dark' ? '#4b5563' : '#e5e7eb'),\n    text: theme.textColor || (theme.mode === 'dark' ? '#f9fafb' : '#111827'),\n    textSecondary: theme.textSecondaryColor || (theme.mode === 'dark' ? '#9ca3af' : '#6b7280'),\n    accent: theme.accentColor || theme.primaryColor || '#3b82f6',\n    secondary: theme.secondaryColor || '#10b981',\n    // Remove excessive gradients, use clean solid colors or subtle gradients\n    headerBg: theme.gradients?.primary || (theme.mode === 'dark' ? '#374151' : '#f8fafc'),\n    sectionBg: theme.gradients?.secondary || (theme.mode === 'dark' ? '#4b5563' : '#ffffff')\n  };\n\n  // Get custom styles if provided\n  const customStyles = theme.disableDefaultStyles ? {} : {\n    fontFamily: theme.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    borderRadius: theme.borderRadius || '12px',\n    border: `1px solid ${colors.border}`,\n    background: colors.background,\n    overflow: 'hidden',\n    maxWidth: '420px',\n    boxShadow: theme.shadows?.medium || (theme.mode === 'dark'\n      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'),\n    position: 'relative' as const,\n    transition: 'all 0.2s ease'\n  };\n\n  return (\n    <div\n      className={`admesh-component admesh-expandable-unit ${className}`}\n      style={{\n        ...customStyles,\n        ...theme.components?.expandableUnit,\n        ...style\n      }}\n      data-admesh-theme={theme.mode}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: colors.headerBg,\n          padding: '20px',\n          borderBottom: isExpanded || !collapsible ? `1px solid ${colors.border}` : 'none',\n          position: 'relative',\n          transition: 'all 0.2s ease'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '16px' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flex: 1, minWidth: 0 }}>\n            <div\n              style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: theme.borderRadius || '8px',\n                background: colors.accent,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: theme.fontSize?.base || '16px',\n                fontWeight: '600',\n                boxShadow: theme.shadows?.small || '0 2px 4px rgba(0, 0, 0, 0.1)',\n                border: `1px solid ${colors.border}`\n              }}\n            >\n              {(recommendation.recommendation_title || recommendation.title).charAt(0).toUpperCase()}\n            </div>\n            <div style={{ flex: 1, minWidth: 0 }}>\n              <h3\n                style={{\n                  margin: 0,\n                  fontSize: '18px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  lineHeight: '1.4',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n              >\n                {recommendation.recommendation_title || recommendation.title}\n              </h3>\n              <p\n                style={{\n                  margin: '8px 0 0 0',\n                  fontSize: '13px',\n                  color: colors.textSecondary,\n                  fontWeight: '400',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n                title={inlineTooltip}\n              >\n                {inlineDisclosure} • {new URL(recommendation.url || recommendation.admesh_link).hostname}\n              </p>\n            </div>\n          </div>\n\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n            {/* CTA Button when collapsed */}\n            {!isExpanded && collapsible && (\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.recommendation_title || recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: false,\n                  location: 'header'\n                }}\n              >\n                <button\n                  style={{\n                    padding: theme.spacing?.small ? `${theme.spacing.small} ${theme.spacing.medium || '12px'}` : '6px 12px',\n                    backgroundColor: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: theme.borderRadius || '6px',\n                    fontSize: theme.fontSize?.small || '12px',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    whiteSpace: 'nowrap',\n                    ...theme.components?.button\n                  }}\n                  onMouseOver={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.medium || '0 2px 6px rgba(0, 0, 0, 0.15)';\n                    }\n                  }}\n                  onMouseOut={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            )}\n\n            {/* Modern Expand/Collapse button */}\n            {collapsible && (\n              <button\n                onClick={handleToggleExpand}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: theme.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  border: `1px solid ${theme.mode === 'dark' ? '#4b5563' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  color: theme.accentColor || '#2563eb',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#4b5563' : '#e5e7eb';\n                  e.currentTarget.style.borderColor = theme.accentColor || '#2563eb';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#374151' : '#f3f4f6';\n                  e.currentTarget.style.borderColor = theme.mode === 'dark' ? '#4b5563' : '#d1d5db';\n                }}\n                aria-label={isExpanded ? 'Show less details' : 'Show more details'}\n              >\n                <span>{isExpanded ? 'Less Details' : 'More Details'}</span>\n                <svg\n                  width=\"16\"\n                  height=\"16\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                >\n                  {isExpanded ? (\n                    // Minus icon for collapse\n                    <path d=\"M5 12h14\" />\n                  ) : (\n                    // Info icon for expand\n                    <>\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n                      <path d=\"M12 16v-4\" />\n                      <path d=\"M12 8h.01\" />\n                    </>\n                  )}\n                </svg>\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Expandable Content */}\n      {(isExpanded || !collapsible) && (\n        <div style={{ padding: '0' }}>\n          {/* Sections */}\n          {displaySections.map((section, index) => (\n            <div\n              key={index}\n              style={{\n                padding: '24px',\n                backgroundColor: index % 2 === 0 ? colors.background : colors.sectionBg,\n                borderBottom: index < displaySections.length - 1 ? `1px solid ${colors.border}` : 'none'\n              }}\n            >\n              <h4\n                style={{\n                  margin: '0 0 12px 0',\n                  fontSize: '15px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px'\n                }}\n              >\n                {section.icon && <span>{section.icon}</span>}\n                {section.title}\n              </h4>\n              <p\n                style={{\n                  margin: 0,\n                  fontSize: '14px',\n                  color: colors.textSecondary,\n                  lineHeight: '1.6'\n                }}\n              >\n                {section.description}\n              </p>\n            </div>\n          ))}\n\n          {/* CTA Button - only show when expanded or when not collapsible */}\n          {(isExpanded || !collapsible) && (\n            <div style={{ padding: '24px', borderTop: `1px solid ${colors.border}`, backgroundColor: colors.background }}>\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: isExpanded,\n                  location: 'footer'\n                }}\n              >\n                <button\n                  style={{\n                    width: '100%',\n                    padding: '14px 28px',\n                    background: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    fontSize: '15px',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',\n                    position: 'relative',\n                    overflow: 'hidden'\n                  }}\n                  onMouseOver={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(99, 102, 241, 0.4)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.3)';\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          )}\n\n          {/* Powered by AdMesh */}\n          {showPoweredBy && (\n            <div\n              style={{\n                padding: '8px 16px',\n                borderTop: `1px solid ${colors.border}`,\n                backgroundColor: colors.headerBg\n              }}\n            >\n              <div\n                style={{\n                  fontSize: '11px',\n                  color: colors.textSecondary,\n                  textAlign: 'center' as const\n                }}\n              >\n                powered by <strong style={{ color: colors.text }}>AdMesh</strong>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdMeshExpandableUnit;\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshInlineRecommendationProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport const AdMeshInlineRecommendation: React.FC<AdMeshInlineRecommendationProps> = ({\n  recommendation,\n  theme,\n  compact = false,\n  showReason = true,\n  className,\n  style\n}) => {\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, compact);\n  const inlineTooltip = getInlineTooltip();\n\n  const containerClasses = classNames(\n    'admesh-inline-recommendation',\n    'group cursor-pointer transition-all duration-200',\n    {\n      'p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700': !compact,\n      'p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30': compact,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      trackingData={{\n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score\n      }}\n      className={containerClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.inlineRecommendation,\n        ...style\n      }}\n    >\n      <div\n        className=\"flex items-start gap-3\"\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Icon/Badge */}\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {recommendation.offer_images && recommendation.offer_images.length > 0 ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.offer_images[0].url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.product_logo ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.product_logo.url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.intent_match_score >= 0.8 ? (\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n          ) : (\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row\">\n            <h4 className={classNames(\n              'font-medium transition-colors duration-200 flex-shrink-0',\n              'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n              'cursor-pointer hover:underline',\n              compact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'\n            )}>\n              {recommendation.recommendation_title || recommendation.title}\n            </h4>\n            \n            {/* Match score badge */}\n            {recommendation.intent_match_score >= 0.7 && (\n              <span className={classNames(\n                'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap',\n                recommendation.intent_match_score >= 0.8\n                  ? 'bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100'\n                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n              )}>\n                {matchScorePercentage}% match\n              </span>\n            )}\n          </div>\n\n          {/* Reason/Description */}\n          {showReason && (recommendation.recommendation_description || recommendation.reason) && (\n            <p className={classNames(\n              'text-gray-600 dark:text-gray-400 line-clamp-2',\n              compact ? 'text-xs' : 'text-sm'\n            )}>\n              {recommendation.recommendation_description || recommendation.reason}\n            </p>\n          )}\n\n          {/* Disclosure */}\n          <p\n            className={classNames(\n              'text-gray-500 dark:text-gray-400 mt-1',\n              compact ? 'text-xs' : 'text-xs'\n            )}\n            title={inlineTooltip}\n          >\n            {inlineDisclosure}\n          </p>\n\n          {/* Features/Keywords */}\n          {!compact && recommendation.keywords && recommendation.keywords.length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mt-2\">\n              {recommendation.keywords.slice(0, 3).map((keyword, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {recommendation.keywords.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  +{recommendation.keywords.length - 3} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Pricing/Trial info */}\n          {!compact && recommendation.trial_days && recommendation.trial_days > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400\">\n                {recommendation.trial_days}-day trial\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Arrow indicator */}\n        <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <svg \n            className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M9 5l7 7-7 7\" \n            />\n          </svg>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationSummaryProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshConversationSummary: React.FC<AdMeshConversationSummaryProps> = ({\n  recommendations,\n  conversationSummary,\n  theme,\n  showTopRecommendations = 3,\n  onRecommendationClick,\n  onStartNewConversation,\n  className\n}) => {\n  const topRecommendations = recommendations\n    .sort((a, b) => b.intent_match_score - a.intent_match_score)\n    .slice(0, showTopRecommendations);\n\n  const containerClasses = classNames(\n    'admesh-conversation-summary',\n    'bg-white dark:bg-black',\n    'rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6',\n    'font-sans', // Standardize font family\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Header */}\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        </div>\n        <div className=\"min-w-0 flex-1\">\n          <h3 className=\"text-base sm:text-lg font-semibold text-black dark:text-white\">\n            Conversation Summary\n          </h3>\n          <p className=\"text-xs sm:text-sm text-gray-600 dark:text-gray-300\">\n            Here's what we discussed and found for you\n          </p>\n        </div>\n      </div>\n\n      {/* Summary Text */}\n      <div className=\"mb-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n          <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed\">\n            {conversationSummary}\n          </p>\n        </div>\n      </div>\n\n      {/* Top Recommendations */}\n      {topRecommendations.length > 0 && (\n        <div className=\"mb-6\">\n          <div className=\"flex items-center gap-2 mb-3\">\n            <svg className=\"w-5 h-5 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <h4 className=\"font-medium text-black dark:text-white\">\n              Top Recommendations\n            </h4>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {topRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                {/* Ranking badge */}\n                <div className=\"absolute -left-2 top-2 z-10\">\n                  <div className={classNames(\n                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',\n                    index === 0 ? 'bg-black dark:bg-white text-white dark:text-black' :\n                    index === 1 ? 'bg-gray-600 dark:bg-gray-400 text-white dark:text-black' :\n                    'bg-gray-800 dark:bg-gray-200 text-white dark:text-black'\n                  )}>\n                    {index + 1}\n                  </div>\n                </div>\n                \n                <div className=\"ml-4\">\n                  <AdMeshInlineRecommendation\n                    recommendation={recommendation}\n                    theme={theme}\n                    compact={true}\n                    showReason={true}\n                    onClick={onRecommendationClick}\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Additional Insights */}\n      {recommendations.length > showTopRecommendations && (\n        <div className=\"mb-6\">\n          <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center gap-2\">\n              <svg className=\"w-4 h-4 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">\n                {recommendations.length - showTopRecommendations} additional recommendation{recommendations.length - showTopRecommendations > 1 ? 's' : ''} available\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        {onStartNewConversation && (\n          <button\n            onClick={onStartNewConversation}\n            className=\"flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n            Start New Conversation\n          </button>\n        )}\n        \n        <button\n          onClick={() => {\n            if (topRecommendations.length > 0) {\n              onRecommendationClick?.(topRecommendations[0].ad_id, topRecommendations[0].admesh_link);\n            }\n          }}\n          className=\"flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n          </svg>\n          View Top Pick\n        </button>\n      </div>\n\n      {/* Powered by AdMesh */}\n      <div className=\"flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          Powered by AdMesh\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationReferenceProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCitationReference: React.FC<AdMeshCitationReferenceProps> = ({\n  recommendation,\n  citationNumber,\n  citationStyle = 'numbered',\n  theme,\n  showTooltip = true,\n  onHover,\n  className,\n  style\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    onHover?.(recommendation);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n  };\n\n\n\n  // Generate citation display based on style\n  const getCitationDisplay = () => {\n    switch (citationStyle) {\n      case 'bracketed':\n        return `[${citationNumber}]`;\n      case 'superscript':\n        return citationNumber.toString();\n      case 'numbered':\n      default:\n        return citationNumber.toString();\n    }\n  };\n\n  const citationClasses = classNames(\n    'admesh-citation-reference',\n    'inline-flex items-center justify-center',\n    'cursor-pointer transition-all duration-200',\n    'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n    'font-medium',\n    {\n      // Numbered style (default)\n      'w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50': citationStyle === 'numbered',\n      \n      // Bracketed style\n      'px-1 text-sm hover:underline': citationStyle === 'bracketed',\n      \n      // Superscript style\n      'text-xs align-super hover:underline': citationStyle === 'superscript',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <span className=\"relative inline-block\">\n      <AdMeshLinkTracker\n        adId={recommendation.ad_id}\n        admeshLink={recommendation.admesh_link}\n        productId={recommendation.product_id}\n        trackingData={{\n          title: recommendation.title,\n          matchScore: recommendation.intent_match_score,\n          citationNumber,\n          citationStyle\n        }}\n        className={citationClasses}\n        style={style}\n      >\n        <span\n          style={containerStyle}\n          data-admesh-theme={theme?.mode}\n          onMouseEnter={handleMouseEnter}\n          onMouseLeave={handleMouseLeave}\n        >\n          {getCitationDisplay()}\n        </span>\n      </AdMeshLinkTracker>\n\n      {/* Tooltip */}\n      {showTooltip && isHovered && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\">\n          <div className=\"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs\">\n            <div className=\"font-semibold mb-1\">{recommendation.title}</div>\n            {recommendation.reason && (\n              <div className=\"text-gray-300 dark:text-gray-600 text-xs\">\n                {recommendation.reason.length > 100 \n                  ? `${recommendation.reason.substring(0, 100)}...` \n                  : recommendation.reason\n                }\n              </div>\n            )}\n            {recommendation.intent_match_score >= 0.7 && (\n              <div className=\"text-green-400 dark:text-green-600 text-xs mt-1\">\n                {Math.round(recommendation.intent_match_score * 100)}% match\n              </div>\n            )}\n            <div className=\"text-gray-400 dark:text-gray-500 text-xs mt-1 italic\">\n              Click to visit product page\n            </div>\n            {/* Tooltip arrow */}\n            <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100\"></div>\n          </div>\n        </div>\n      )}\n    </span>\n  );\n};\n", "import React, { useState, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationUnitProps, AdMeshRecommendation } from '../types/index';\nimport { AdMeshCitationReference } from './AdMeshCitationReference';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshCitationUnit: React.FC<AdMeshCitationUnitProps> = ({\n  recommendations,\n  conversationText,\n  theme,\n  showCitationList = true,\n  citationStyle = 'numbered',\n  onCitationHover,\n  className,\n  style\n}) => {\n  const [hoveredRecommendation, setHoveredRecommendation] = useState<AdMeshRecommendation | null>(null);\n\n  // Process conversation text to insert citations\n  const processedContent = useMemo(() => {\n    if (!conversationText || recommendations.length === 0) {\n      return { text: conversationText, citationMap: new Map() };\n    }\n\n    let processedText = conversationText;\n    const citationMap = new Map();\n    \n    // Sort recommendations by intent match score (highest first)\n    const sortedRecommendations = [...recommendations]\n      .sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Find mentions of product titles in the text and replace with citations\n    sortedRecommendations.forEach((recommendation, index) => {\n      const citationNumber = index + 1;\n      const title = recommendation.title;\n      \n      // Create citation reference\n      citationMap.set(citationNumber, recommendation);\n      \n      // Look for exact title matches (case insensitive)\n      const titleRegex = new RegExp(`\\\\b${title.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n      \n      // Replace first occurrence with citation\n      if (titleRegex.test(processedText)) {\n        processedText = processedText.replace(titleRegex, (match) => {\n          return `${match}{{CITATION_${citationNumber}}}`;\n        });\n      } else {\n        // If no exact match, try to find a good insertion point\n        // Look for related keywords or add at the end of relevant sentences\n        const keywords = recommendation.keywords || [];\n        let inserted = false;\n        \n        for (const keyword of keywords) {\n          const keywordRegex = new RegExp(`\\\\b${keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n          if (keywordRegex.test(processedText) && !inserted) {\n            processedText = processedText.replace(keywordRegex, (match) => {\n              inserted = true;\n              return `${match}{{CITATION_${citationNumber}}}`;\n            });\n            break;\n          }\n        }\n        \n        // If still no insertion point found, add citation at the end\n        if (!inserted) {\n          processedText += `{{CITATION_${citationNumber}}}`;\n        }\n      }\n    });\n\n    return { text: processedText, citationMap };\n  }, [conversationText, recommendations]);\n\n  // Render text with embedded citations\n  const renderTextWithCitations = () => {\n    const { text, citationMap } = processedContent;\n    const parts = text.split(/(\\{\\{CITATION_\\d+\\}\\})/);\n\n    return parts.map((part, index) => {\n      const citationMatch = part.match(/\\{\\{CITATION_(\\d+)\\}\\}/);\n\n      if (citationMatch) {\n        const citationNumber = parseInt(citationMatch[1]);\n        const recommendation = citationMap.get(citationNumber);\n\n        if (recommendation) {\n          return (\n            <AdMeshCitationReference\n              key={`citation-${citationNumber}-${index}`}\n              recommendation={recommendation}\n              citationNumber={citationNumber}\n              citationStyle={citationStyle}\n              theme={theme}\n              showTooltip={true}\n              onHover={(rec) => {\n                setHoveredRecommendation(rec);\n                onCitationHover?.(rec);\n              }}\n            />\n          );\n        }\n      }\n\n      return <span key={index}>{part}</span>;\n    });\n  };\n\n  const containerClasses = classNames(\n    'admesh-citation-unit',\n    'space-y-4',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.citationUnit,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Main conversation text with embedded citations */}\n      <div className=\"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed\">\n        {renderTextWithCitations()}\n      </div>\n\n      {/* Citation list/references */}\n      {showCitationList && recommendations.length > 0 && (\n        <div className=\"admesh-citation-list\">\n          <div className=\"border-t border-gray-200 dark:border-slate-700 pt-4\">\n            <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n              </svg>\n              References\n            </h4>\n            \n            <div className=\"space-y-2\">\n              {recommendations\n                .sort((a, b) => b.intent_match_score - a.intent_match_score)\n                .map((recommendation, index) => (\n                  <div \n                    key={recommendation.ad_id || index}\n                    className={classNames(\n                      'flex items-start gap-3 p-2 rounded-lg transition-colors duration-200',\n                      {\n                        'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800': \n                          hoveredRecommendation?.ad_id === recommendation.ad_id,\n                        'hover:bg-gray-50 dark:hover:bg-slate-800/50': \n                          hoveredRecommendation?.ad_id !== recommendation.ad_id\n                      }\n                    )}\n                  >\n                    {/* Citation number */}\n                    <div className=\"flex-shrink-0 mt-1\">\n                      <AdMeshCitationReference\n                        recommendation={recommendation}\n                        citationNumber={index + 1}\n                        citationStyle={citationStyle}\n                        theme={theme}\n                        showTooltip={false}\n                      />\n                    </div>\n                    \n                    {/* Recommendation details */}\n                    <div className=\"flex-1 min-w-0\">\n                      <AdMeshInlineRecommendation\n                        recommendation={recommendation}\n                        theme={theme}\n                        compact={true}\n                        showReason={false}\n                      />\n                    </div>\n                  </div>\n                ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationalUnitProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshConversationSummary } from './AdMeshConversationSummary';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCitationUnit } from './AdMeshCitationUnit';\n\nexport const AdMeshConversationalUnit: React.FC<AdMeshConversationalUnitProps> = ({\n  recommendations,\n  config,\n  theme,\n  conversationSummary,\n  sessionId,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(config.autoShow !== false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  useEffect(() => {\n    if (config.delayMs && config.delayMs > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, config.delayMs);\n      return () => clearTimeout(timer);\n    } else {\n      setHasAnimated(true);\n    }\n  }, [config.delayMs]);\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const maxRecommendations = config.maxRecommendations || 3;\n  const displayRecommendations = recommendations.slice(0, maxRecommendations);\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    onRecommendationClick?.(adId, admeshLink);\n  };\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Render based on display mode\n  const renderContent = () => {\n    switch (config.displayMode) {\n      case 'summary':\n        return conversationSummary ? (\n          <AdMeshConversationSummary\n            recommendations={displayRecommendations}\n            conversationSummary={conversationSummary}\n            theme={theme}\n            showTopRecommendations={maxRecommendations}\n            onRecommendationClick={handleRecommendationClick}\n            onStartNewConversation={onDismiss}\n          />\n        ) : null;\n\n      case 'inline':\n        return (\n          <div className=\"space-y-2\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'minimal':\n        return displayRecommendations.length > 0 ? (\n          <div className=\"admesh-minimal-unit\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {displayRecommendations.length} intelligent match{displayRecommendations.length > 1 ? 'es' : ''} found\n              </span>\n            </div>\n            <AdMeshInlineRecommendation\n              recommendation={displayRecommendations[0]}\n              theme={theme}\n              compact={true}\n              showReason={false}\n              onClick={handleRecommendationClick}\n            />\n            {displayRecommendations.length > 1 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                +{displayRecommendations.length - 1} more recommendation{displayRecommendations.length > 2 ? 's' : ''}\n              </div>\n            )}\n          </div>\n        ) : null;\n\n      case 'citation':\n        return conversationSummary ? (\n          <AdMeshCitationUnit\n            recommendations={displayRecommendations}\n            conversationText={conversationSummary}\n            theme={theme}\n            showCitationList={true}\n            citationStyle=\"numbered\"\n            onRecommendationClick={handleRecommendationClick}\n          />\n        ) : null;\n\n      case 'floating':\n        return (\n          <div className=\"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <span className=\"text-sm font-semibold text-gray-800 dark:text-gray-200\">\n                  Recommended for you\n                </span>\n              </div>\n              {onDismiss && (\n                <button\n                  onClick={handleDismiss}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                  aria-label=\"Dismiss recommendations\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              )}\n            </div>\n            <div className=\"space-y-2\">\n              {displayRecommendations.map((recommendation, index) => (\n                <AdMeshInlineRecommendation\n                  key={recommendation.ad_id || index}\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={handleRecommendationClick}\n                />\n              ))}\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={false}\n                showBadges={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-conversational-unit',\n    'transition-all duration-300 ease-in-out',\n    {\n      'opacity-0 translate-y-2': !hasAnimated,\n      'opacity-100 translate-y-0': hasAnimated,\n      'fixed bottom-4 right-4 max-w-sm z-50': config.displayMode === 'floating',\n      'my-3': config.displayMode === 'inline',\n      'mt-4 pt-4 border-t border-gray-200 dark:border-slate-700': config.displayMode === 'summary',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n      data-admesh-context={config.context}\n      data-session-id={sessionId}\n    >\n      {renderContent()}\n      \n      {/* Powered by AdMesh branding */}\n      {config.showPoweredBy !== false && (\n        <div className=\"flex justify-end mt-2\">\n          <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n            Powered by AdMesh\n          </span>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatMessageProps } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport const AdMeshChatMessage: React.FC<AdMeshChatMessageProps> = ({\n  message,\n  theme,\n  onRecommendationClick,\n  className\n}) => {\n  const isUser = message.role === 'user';\n  const isAssistant = message.role === 'assistant';\n\n  const messageClasses = classNames(\n    'admesh-chat-message',\n    'flex items-start gap-3',\n    {\n      'flex-row-reverse': isUser,\n    },\n    className\n  );\n\n  const bubbleClasses = classNames(\n    'max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm',\n    {\n      'bg-gradient-to-r from-blue-600 to-indigo-600 text-white': isUser,\n      'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100': isAssistant,\n      'bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100': message.role === 'system',\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div\n      className={messageClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Avatar */}\n      {!isUser && (\n        <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {isUser && (\n        <div className=\"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {/* Message Content */}\n      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} flex-1`}>\n        {/* Message Bubble */}\n        <div className={bubbleClasses}>\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n        </div>\n\n        {/* Timestamp */}\n        <div className={classNames(\n          'text-xs text-gray-500 dark:text-gray-400 mt-1',\n          { 'text-right': isUser }\n        )}>\n          {formatTime(message.timestamp)}\n        </div>\n\n        {/* Recommendations */}\n        {message.recommendations && message.recommendations.length > 0 && (\n          <div className=\"mt-3 w-full max-w-lg\">\n            {/* Recommendations Header */}\n            <div className=\"flex items-center gap-2 mb-3\">\n              <svg className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {message.recommendations.length} recommendation{message.recommendations.length > 1 ? 's' : ''} found\n              </span>\n            </div>\n\n            {/* Recommendations Display */}\n            <AdMeshConversationalUnit\n              recommendations={message.recommendations}\n              config={{\n                displayMode: 'inline',\n                context: 'chat',\n                maxRecommendations: 3,\n                showPoweredBy: false,\n                autoShow: true,\n                delayMs: 300\n              }}\n              theme={theme}\n              onRecommendationClick={onRecommendationClick}\n              className=\"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useRef, KeyboardEvent } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInputProps } from '../types/index';\n\nexport const AdMeshChatInput: React.FC<AdMeshChatInputProps> = ({\n  placeholder = \"Type your message...\",\n  disabled = false,\n  suggestions = [],\n  theme,\n  onSendMessage,\n  className\n}) => {\n  const [message, setMessage] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);\n  const inputRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Filter suggestions based on input\n    if (value.trim() && suggestions.length > 0) {\n      const filtered = suggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(value.toLowerCase())\n      );\n      setFilteredSuggestions(filtered);\n      setShowSuggestions(filtered.length > 0);\n    } else {\n      setShowSuggestions(false);\n    }\n\n    // Auto-resize textarea\n    if (inputRef.current) {\n      inputRef.current.style.height = 'auto';\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;\n    }\n  };\n\n  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  const handleSend = () => {\n    const trimmedMessage = message.trim();\n    if (trimmedMessage && !disabled && onSendMessage) {\n      onSendMessage(trimmedMessage);\n      setMessage('');\n      setShowSuggestions(false);\n      \n      // Reset textarea height\n      if (inputRef.current) {\n        inputRef.current.style.height = 'auto';\n      }\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setMessage(suggestion);\n    setShowSuggestions(false);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-chat-input',\n    'relative',\n    className\n  );\n\n  const inputClasses = classNames(\n    'w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600',\n    'bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100',\n    'placeholder-gray-500 dark:placeholder-gray-400',\n    'focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n    'transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600',\n    'pr-12 pl-4 py-3 text-sm leading-5',\n    {\n      'opacity-50 cursor-not-allowed': disabled,\n    }\n  );\n\n  const sendButtonClasses = classNames(\n    'absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200',\n    'flex items-center justify-center',\n    {\n      'bg-blue-600 hover:bg-blue-700 text-white': message.trim() && !disabled,\n      'bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed': !message.trim() || disabled,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Suggestions Dropdown */}\n      {showSuggestions && filteredSuggestions.length > 0 && (\n        <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10\">\n          {filteredSuggestions.slice(0, 5).map((suggestion, index) => (\n            <button\n              key={index}\n              onClick={() => handleSuggestionClick(suggestion)}\n              className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Input Container */}\n      <div className=\"relative\">\n        <textarea\n          ref={inputRef}\n          value={message}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          disabled={disabled}\n          rows={1}\n          className={inputClasses}\n          style={{ minHeight: '44px', maxHeight: '120px' }}\n        />\n\n        {/* Send Button */}\n        <button\n          onClick={handleSend}\n          disabled={!message.trim() || disabled}\n          className={sendButtonClasses}\n          aria-label=\"Send message\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Helper Text */}\n      <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400\">\n        <span>Press Enter to send, Shift+Enter for new line</span>\n        <span className={classNames(\n          'transition-opacity duration-200',\n          { 'opacity-0': message.length < 100 }\n        )}>\n          {message.length}/500\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInterfaceProps } from '../types/index';\nimport { AdMeshChatMessage } from './AdMeshChatMessage';\nimport { AdMeshChatInput } from './AdMeshChatInput';\n\nexport const AdMeshChatInterface: React.FC<AdMeshChatInterfaceProps> = ({\n  messages,\n  config,\n  theme,\n  isLoading = false,\n  onSendMessage,\n  onRecommendationClick,\n  className\n}) => {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const messagesContainerRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const containerClasses = classNames(\n    'admesh-chat-interface',\n    'flex flex-col h-full bg-white dark:bg-slate-900',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  // Limit messages if maxMessages is set\n  const displayMessages = config.maxMessages \n    ? messages.slice(-config.maxMessages)\n    : messages;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Messages Area */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\"\n      >\n        {displayMessages.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4\">\n              <svg className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n              Welcome to AdMesh AI\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 max-w-xs\">\n              Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!\n            </p>\n          </div>\n        ) : (\n          <>\n            {displayMessages.map((message) => (\n              <AdMeshChatMessage\n                key={message.id}\n                message={message}\n                theme={theme}\n                onRecommendationClick={onRecommendationClick}\n              />\n            ))}\n\n            {/* Typing Indicator */}\n            {isLoading && config.enableTypingIndicator !== false && (\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className=\"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </>\n        )}\n      </div>\n\n      {/* Quick Suggestions */}\n      {config.enableSuggestions && config.suggestions && config.suggestions.length > 0 && messages.length === 0 && (\n        <div className=\"px-4 pb-2\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">Quick suggestions:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {config.suggestions.slice(0, 3).map((suggestion, index) => (\n              <button\n                key={index}\n                onClick={() => onSendMessage?.(suggestion)}\n                className=\"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors\"\n              >\n                {suggestion}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Input Area */}\n      {config.showInputField !== false && onSendMessage && (\n        <div className=\"border-t border-gray-200 dark:border-slate-700 p-4\">\n          <AdMeshChatInput\n            placeholder={config.placeholder || \"Ask me about products, tools, or services...\"}\n            disabled={isLoading}\n            suggestions={config.suggestions}\n            theme={theme}\n            onSendMessage={onSendMessage}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshFloatingChatProps, ChatMessage } from '../types/index';\nimport { AdMeshChatInterface } from './AdMeshChatInterface';\n\nexport const AdMeshFloatingChat: React.FC<AdMeshFloatingChatProps> = ({\n  config,\n  theme,\n  title = 'AI Assistant',\n  subtitle = 'Get personalized recommendations',\n  isOpen: controlledIsOpen,\n  onToggle,\n  onSendMessage,\n  onRecommendationClick,\n  autoRecommendations,\n  autoRecommendationTrigger,\n  showInputField = true,\n  autoShowRecommendations = false,\n  onAutoRecommendationDismiss,\n  className\n}) => {\n  const [internalIsOpen, setInternalIsOpen] = useState(config.autoOpen || false);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasInteracted, setHasInteracted] = useState(false);\n\n  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (config.showWelcomeMessage && config.welcomeMessage && messages.length === 0) {\n      const welcomeMessage: ChatMessage = {\n        id: 'welcome',\n        role: 'assistant',\n        content: config.welcomeMessage,\n        timestamp: new Date(),\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [config.showWelcomeMessage, config.welcomeMessage, messages.length]);\n\n  // Handle auto-recommendations\n  useEffect(() => {\n    if (autoRecommendations && autoRecommendations.length > 0 && autoShowRecommendations) {\n      const autoMessage: ChatMessage = {\n        id: `auto-${Date.now()}`,\n        role: 'assistant',\n        content: autoRecommendationTrigger\n          ? `Based on \"${autoRecommendationTrigger}\", here are some relevant recommendations:`\n          : 'I found some relevant recommendations for you:',\n        timestamp: new Date(),\n        recommendations: autoRecommendations,\n      };\n\n      // Auto-open the chat and show recommendations\n      if (controlledIsOpen === undefined) {\n        setInternalIsOpen(true);\n      }\n\n      // Add the auto-recommendation message\n      setMessages(prev => {\n        // Avoid duplicating auto-recommendations\n        const hasAutoMessage = prev.some(msg => msg.id.startsWith('auto-'));\n        if (hasAutoMessage) {\n          return prev.map(msg =>\n            msg.id.startsWith('auto-') ? autoMessage : msg\n          );\n        }\n        return [...prev, autoMessage];\n      });\n    }\n  }, [autoRecommendations, autoShowRecommendations, autoRecommendationTrigger, controlledIsOpen]);\n\n  const handleToggle = () => {\n    if (onToggle) {\n      onToggle();\n    } else {\n      setInternalIsOpen(!internalIsOpen);\n    }\n    setHasInteracted(true);\n  };\n\n  const handleSendMessage = async (messageContent: string) => {\n    if (!onSendMessage) return;\n\n    // Add user message\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setIsLoading(true);\n\n    try {\n      // Send message\n      await onSendMessage(messageContent);\n      // Note: The response should be handled by the parent component\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage: ChatMessage = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Get chat dimensions based on size\n  const getChatDimensions = () => {\n    switch (config.size) {\n      case 'sm': return 'w-80 h-96';\n      case 'md': return 'w-96 h-[32rem]';\n      case 'lg': return 'w-[28rem] h-[36rem]';\n      case 'xl': return 'w-[32rem] h-[40rem]';\n      default: return 'w-96 h-[32rem]';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (config.position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-floating-chat',\n    'fixed z-50 transition-all duration-300 ease-in-out',\n    getPositionClasses(),\n    className\n  );\n\n  const chatClasses = classNames(\n    'bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden',\n    getChatDimensions(),\n    {\n      'opacity-0 scale-95 pointer-events-none': !isOpen,\n      'opacity-100 scale-100': isOpen,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Chat Interface */}\n      <div className={chatClasses}>\n        {isOpen && (\n          <>\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-sm\">{title}</h3>\n                  <p className=\"text-xs text-blue-100\">{subtitle}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {/* Dismiss auto-recommendations button */}\n                {autoRecommendations && autoRecommendations.length > 0 && onAutoRecommendationDismiss && (\n                  <button\n                    onClick={() => {\n                      onAutoRecommendationDismiss();\n                      setMessages(prev => prev.filter(msg => !msg.id.startsWith('auto-')));\n                    }}\n                    className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                    aria-label=\"Dismiss recommendations\"\n                    title=\"Dismiss recommendations\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                    </svg>\n                  </button>\n                )}\n\n                <button\n                  onClick={handleToggle}\n                  className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                  aria-label=\"Close chat\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Interface */}\n            <AdMeshChatInterface\n              messages={messages}\n              config={{\n                ...config,\n                showInputField: showInputField\n              }}\n              theme={theme}\n              isLoading={isLoading}\n              onSendMessage={showInputField ? handleSendMessage : () => {}}\n              onRecommendationClick={onRecommendationClick}\n              className=\"h-full\"\n            />\n          </>\n        )}\n      </div>\n\n      {/* Chat Toggle Button */}\n      {!isOpen && (\n        <button\n          onClick={handleToggle}\n          className={classNames(\n            'w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',\n            'text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200',\n            'flex items-center justify-center relative'\n          )}\n          aria-label=\"Open chat\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n          \n          {/* Notification dot for new users */}\n          {!hasInteracted && (\n            <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n          )}\n        </button>\n      )}\n\n      {/* Powered by AdMesh */}\n      {isOpen && (\n        <div className=\"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm\">\n          Powered by AdMesh\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarHeaderProps } from '../types/index';\n\nexport const AdMeshSidebarHeader: React.FC<AdMeshSidebarHeaderProps> = ({\n  title,\n  theme,\n  collapsible = false,\n  isCollapsed = false,\n  onToggle,\n  onSearch,\n  showSearch = false,\n  className\n}) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearchFocused, setIsSearchFocused] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    onSearch?.(value);\n  };\n\n  const handleSearchClear = () => {\n    setSearchQuery('');\n    onSearch?.('');\n  };\n\n  const headerClasses = classNames(\n    'admesh-sidebar-header',\n    'flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={headerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Title and Toggle */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">\n          {title}\n        </h3>\n\n        <div className=\"flex items-center gap-2\">\n          {/* Mobile close button */}\n          {isMobile && onToggle && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden\"\n              title=\"Close sidebar\"\n            >\n              <svg\n                className=\"w-4 h-4 text-gray-600 dark:text-gray-400\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n\n          {/* Desktop collapse button */}\n          {collapsible && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block\"\n              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n            >\n              <svg\n                className={classNames(\n                  'w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200',\n                  { 'rotate-180': isCollapsed }\n                )}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search Bar */}\n      {showSearch && !isCollapsed && (\n        <div className=\"relative\">\n          <div className={classNames(\n            'relative flex items-center transition-all duration-200',\n            {\n              'ring-2 ring-blue-500 dark:ring-blue-400': isSearchFocused,\n            }\n          )}>\n            {/* Search Icon */}\n            <div className=\"absolute left-3 pointer-events-none\">\n              <svg className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n\n            {/* Search Input */}\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              onFocus={() => setIsSearchFocused(true)}\n              onBlur={() => setIsSearchFocused(false)}\n              placeholder=\"Search recommendations...\"\n              className={classNames(\n                'w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg',\n                'placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100',\n                'focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n                'transition-all duration-200'\n              )}\n            />\n\n            {/* Clear Button */}\n            {searchQuery && (\n              <button\n                onClick={handleSearchClear}\n                className=\"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors\"\n                title=\"Clear search\"\n              >\n                <svg className=\"w-3 h-3 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n          </div>\n\n          {/* Search Results Count */}\n          {searchQuery && (\n            <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\n              Search results will be filtered in real-time\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Quick Stats */}\n      {!isCollapsed && (\n        <div className=\"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n          <div className=\"flex items-center gap-1\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            <span>Live recommendations</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span>AI-powered</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarContentProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshProductCard } from './AdMeshProductCard';\n\nexport const AdMeshSidebarContent: React.FC<AdMeshSidebarContentProps> = ({\n  recommendations,\n  displayMode,\n  theme,\n  maxRecommendations,\n  onRecommendationClick,\n  className\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n  const [activeTab, setActiveTab] = useState<'all' | 'top' | 'recent'>('all');\n\n  const displayRecommendations = maxRecommendations \n    ? recommendations.slice(0, maxRecommendations)\n    : recommendations;\n\n  const getTabRecommendations = () => {\n    switch (activeTab) {\n      case 'top':\n        return displayRecommendations\n          .filter(rec => rec.intent_match_score >= 0.8)\n          .slice(0, 5);\n      case 'recent':\n        return displayRecommendations.slice(0, 3);\n      default:\n        return displayRecommendations;\n    }\n  };\n\n  const tabRecommendations = getTabRecommendations();\n\n  const contentClasses = classNames(\n    'admesh-sidebar-content',\n    'flex flex-col h-full',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const renderRecommendations = () => {\n    if (tabRecommendations.length === 0) {\n      return (\n        <div className=\"flex-1 flex flex-col items-center justify-center p-6 text-center\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n            No recommendations found\n          </h4>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            Try adjusting your search or filters\n          </p>\n        </div>\n      );\n    }\n\n    switch (displayMode) {\n      case 'recommendations':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'history':\n        return (\n          <div className=\"space-y-2\">\n            {tabRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0\"></div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\n                    {recommendation.title}\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {Math.round(recommendation.intent_match_score * 100)}% match\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'favorites':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.slice(0, 3).map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                <AdMeshInlineRecommendation\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={onRecommendationClick}\n                />\n                <button className=\"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\">\n                  <svg className=\"w-3 h-3 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n                  </svg>\n                </button>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'mixed':\n        return (\n          <div className=\"space-y-4\">\n            {/* Top recommendation as card */}\n            {tabRecommendations[0] && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  Top Pick\n                </h4>\n                <AdMeshProductCard\n                  recommendation={tabRecommendations[0]}\n                  theme={theme}\n                  showMatchScore={true}\n                  showBadges={true}\n                  onClick={onRecommendationClick}\n                  className=\"text-xs\"\n                />\n              </div>\n            )}\n\n            {/* Other recommendations as inline */}\n            {tabRecommendations.slice(1).length > 0 && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  More Options\n                </h4>\n                <div className=\"space-y-2\">\n                  {tabRecommendations.slice(1, 4).map((recommendation, index) => (\n                    <AdMeshInlineRecommendation\n                      key={recommendation.ad_id || index}\n                      recommendation={recommendation}\n                      theme={theme}\n                      compact={true}\n                      showReason={false}\n                      onClick={onRecommendationClick}\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div\n      className={contentClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Tabs */}\n      <div className=\"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900\">\n        <button\n          onClick={() => setActiveTab('all')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'all'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          All ({recommendations.length})\n        </button>\n        <button\n          onClick={() => setActiveTab('top')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'top'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Top\n        </button>\n        <button\n          onClick={() => setActiveTab('recent')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'recent'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Recent\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 min-h-0\" style={{\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\n        overscrollBehavior: 'contain' // Prevent scroll chaining on mobile\n      }}>\n        {renderRecommendations()}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800\">\n        <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            {tabRecommendations.length} recommendation{tabRecommendations.length !== 1 ? 's' : ''}\n          </span>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors\"\n          >\n            Filters\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarProps, SidebarFilters } from '../types/index';\nimport { AdMeshSidebarHeader } from './AdMeshSidebarHeader';\nimport { AdMeshSidebarContent } from './AdMeshSidebarContent';\n\nexport const AdMeshSidebar: React.FC<AdMeshSidebarProps> = ({\n  recommendations,\n  config,\n  theme,\n  title = 'Recommendations',\n  isOpen = true,\n  onToggle,\n  onRecommendationClick,\n  onSearch,\n  // onFilter,\n  className,\n  containerMode = false // New prop for demo/container integration\n}) => {\n  const [isCollapsed, setIsCollapsed] = useState(config.defaultCollapsed || false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filters] = useState<SidebarFilters>({});\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Prevent body scroll on mobile when sidebar is open\n  useEffect(() => {\n    if (isMobile && isOpen && !isCollapsed && !containerMode) {\n      const originalStyle = window.getComputedStyle(document.body).overflow;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n\n      return () => {\n        document.body.style.overflow = originalStyle;\n        document.body.style.position = '';\n        document.body.style.width = '';\n      };\n    }\n  }, [isMobile, isOpen, isCollapsed, containerMode]);\n\n  // Handle auto-refresh if enabled\n  useEffect(() => {\n    if (config.autoRefresh && config.refreshInterval) {\n      const interval = setInterval(() => {\n        // Trigger a refresh - in a real app this would refetch recommendations\n        console.log('Auto-refreshing recommendations...');\n      }, config.refreshInterval);\n\n      return () => clearInterval(interval);\n    }\n  }, [config.autoRefresh, config.refreshInterval]);\n\n  // Filter recommendations based on search and filters\n  const filteredRecommendations = useMemo(() => {\n    let filtered = [...recommendations];\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(rec => \n        rec.title.toLowerCase().includes(query) ||\n        rec.reason.toLowerCase().includes(query) ||\n        rec.keywords?.some(keyword => keyword.toLowerCase().includes(query))\n      );\n    }\n\n    // Apply category filter\n    if (filters.categories && filters.categories.length > 0) {\n      filtered = filtered.filter(rec => \n        rec.categories?.some(cat => filters.categories?.includes(cat))\n      );\n    }\n\n    // Apply free tier filter\n    if (filters.hasFreeTier) {\n      // Note: has_free_tier property not available in current type definition\n      // This filter is disabled until the property is added to AdMeshRecommendation\n      // filtered = filtered.filter(rec => rec.has_free_tier);\n    }\n\n    // Apply trial filter\n    if (filters.hasTrial) {\n      filtered = filtered.filter(rec => rec.trial_days && rec.trial_days > 0);\n    }\n\n    // Apply minimum match score filter\n    if (filters.minMatchScore !== undefined) {\n      filtered = filtered.filter(rec => rec.intent_match_score >= filters.minMatchScore!);\n    }\n\n    // Sort by match score (highest first)\n    filtered.sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Limit results\n    if (config.maxRecommendations) {\n      filtered = filtered.slice(0, config.maxRecommendations);\n    }\n\n    return filtered;\n  }, [recommendations, searchQuery, filters, config.maxRecommendations]);\n\n  const handleToggle = () => {\n    if (config.collapsible) {\n      setIsCollapsed(!isCollapsed);\n      onToggle?.();\n    }\n  };\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  // const handleFilter = (newFilters: SidebarFilters) => {\n  //   setFilters(newFilters);\n  //   onFilter?.(newFilters);\n  // };\n\n  // Get sidebar width based on size with mobile responsiveness\n  const getSidebarWidth = () => {\n    if (isCollapsed) return 'w-12';\n\n    // On mobile, always use full width with proper constraints\n    switch (config.size) {\n      case 'sm': return 'w-full sm:w-64 max-w-[90vw] sm:max-w-sm';\n      case 'md': return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n      case 'lg': return 'w-full sm:w-96 max-w-[90vw] sm:max-w-lg';\n      case 'xl': return 'w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl';\n      default: return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n    }\n  };\n\n  const sidebarClasses = classNames(\n    'admesh-sidebar',\n    'flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out',\n    getSidebarWidth(),\n    {\n      'border-r': config.position === 'left',\n      'border-l': config.position === 'right',\n      // Use fixed positioning for full-screen mode, relative for container mode\n      // Improved mobile positioning with proper viewport handling\n      'fixed top-0 bottom-0 z-[9999]': !containerMode,\n      'relative h-full': containerMode,\n      'left-0': config.position === 'left' && !containerMode,\n      'right-0': config.position === 'right' && !containerMode,\n      // Better mobile transform handling\n      'transform -translate-x-full': config.position === 'left' && !isOpen && !containerMode,\n      'transform translate-x-full': config.position === 'right' && !isOpen && !containerMode,\n      // Mobile-specific improvements\n      'min-h-0': true, // Prevent height issues on mobile\n      'overflow-hidden': !containerMode, // Prevent scroll issues\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (!isOpen && !config.collapsible) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Overlay for mobile - show in both modes on small screens */}\n      {isOpen && !isCollapsed && (\n        <div\n          className={classNames(\n            \"bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300\",\n            containerMode ? \"absolute inset-0\" : \"fixed inset-0\"\n          )}\n          onClick={() => onToggle?.()}\n          style={{\n            // Ensure overlay covers the entire viewport on mobile\n            position: containerMode ? 'absolute' : 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            touchAction: 'none', // Prevent scrolling behind overlay\n          }}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={sidebarClasses}\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n        data-sidebar-position={config.position}\n        data-sidebar-size={config.size}\n        data-mobile-open={isMobile && isOpen && !isCollapsed ? 'true' : 'false'}\n        data-container-mode={containerMode ? 'true' : 'false'}\n      >\n        {/* Header */}\n        {config.showHeader !== false && (\n          <AdMeshSidebarHeader\n            title={title}\n            theme={theme}\n            collapsible={config.collapsible}\n            isCollapsed={isCollapsed}\n            onToggle={handleToggle}\n            onSearch={config.showSearch ? handleSearch : undefined}\n            showSearch={config.showSearch && !isCollapsed}\n          />\n        )}\n\n        {/* Content */}\n        {!isCollapsed && (\n          <AdMeshSidebarContent\n            recommendations={filteredRecommendations}\n            displayMode={config.displayMode}\n            theme={theme}\n            maxRecommendations={config.maxRecommendations}\n            onRecommendationClick={onRecommendationClick}\n            className=\"flex-1 overflow-hidden min-h-0\"\n          />\n        )}\n\n        {/* Collapsed state indicator */}\n        {isCollapsed && config.collapsible && (\n          <div className=\"flex-1 flex flex-col items-center justify-center p-2\">\n            <button\n              onClick={handleToggle}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors\"\n              title=\"Expand sidebar\"\n            >\n              <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </button>\n            <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap\">\n              {filteredRecommendations.length}\n            </div>\n          </div>\n        )}\n\n        {/* Powered by AdMesh */}\n        {!isCollapsed && (\n          <div className=\"p-3 border-t border-gray-200 dark:border-slate-700\">\n            <div className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n              Powered by AdMesh\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport interface AdMeshAutoRecommendationWidgetProps {\n  recommendations: AdMeshRecommendation[];\n  trigger?: string; // The query/context that triggered recommendations\n  theme?: AdMeshTheme;\n  title?: string;\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\n  size?: 'sm' | 'md' | 'lg';\n  autoShow?: boolean;\n  showDelay?: number; // Delay before showing (ms)\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n  onDismiss?: () => void;\n  className?: string;\n}\n\nexport const AdMeshAutoRecommendationWidget: React.FC<AdMeshAutoRecommendationWidgetProps> = ({\n  recommendations,\n  trigger,\n  theme,\n  title = 'AI Recommendations',\n  position = 'bottom-right',\n  size = 'md',\n  autoShow = true,\n  showDelay = 1000,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  // Auto-show with delay\n  useEffect(() => {\n    if (autoShow && recommendations.length > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, showDelay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoShow, recommendations.length, showDelay]);\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Get widget dimensions based on size\n  const getWidgetDimensions = () => {\n    switch (size) {\n      case 'sm': return 'w-72 max-h-80';\n      case 'md': return 'w-80 max-h-96';\n      case 'lg': return 'w-96 max-h-[28rem]';\n      default: return 'w-80 max-h-96';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const containerClasses = classNames(\n    'admesh-auto-recommendation-widget',\n    'fixed z-50 transition-all duration-500 ease-out',\n    getPositionClasses(),\n    getWidgetDimensions(),\n    {\n      'opacity-0 scale-95 translate-y-2': !hasAnimated,\n      'opacity-100 scale-100 translate-y-0': hasAnimated,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-sm\">{title}</h3>\n              {trigger && (\n                <p className=\"text-xs text-blue-100 truncate max-w-48\">\n                  Based on: \"{trigger}\"\n                </p>\n              )}\n            </div>\n          </div>\n          <button\n            onClick={handleDismiss}\n            className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n            aria-label=\"Dismiss recommendations\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\">\n          {/* Recommendations count */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {recommendations.length} intelligent match{recommendations.length > 1 ? 'es' : ''} found\n            </span>\n          </div>\n\n          {/* Recommendations */}\n          <AdMeshConversationalUnit\n            recommendations={recommendations}\n            config={{\n              displayMode: 'inline',\n              context: 'assistant',\n              maxRecommendations: 3,\n              showPoweredBy: false,\n              autoShow: true,\n              delayMs: 200\n            }}\n            theme={theme}\n            onRecommendationClick={onRecommendationClick}\n          />\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Powered by AdMesh\n            </span>\n            <button\n              onClick={handleDismiss}\n              className=\"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n            >\n              Dismiss\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import type { AdMeshTheme } from '../types';\n\n/**\n * Utility functions for theme customization in AdMesh UI SDK\n */\n\n/**\n * Creates a theme with sensible defaults and custom overrides\n */\nexport const createAdMeshTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const baseTheme: AdMeshTheme = {\n    mode: 'light',\n    primaryColor: '#3b82f6',\n    secondaryColor: '#10b981',\n    accentColor: '#3b82f6',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f9fafb',\n    borderColor: '#e5e7eb',\n    textColor: '#111827',\n    textSecondaryColor: '#6b7280',\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    fontSize: {\n      small: '12px',\n      base: '14px',\n      large: '16px',\n      title: '18px'\n    },\n    borderRadius: '8px',\n    spacing: {\n      small: '4px',\n      medium: '8px',\n      large: '16px'\n    },\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.1)'\n    },\n    icons: {\n      expandIcon: '▼',\n      collapseIcon: '▲',\n      starIcon: '★',\n      checkIcon: '✓',\n      arrowIcon: '→'\n    }\n  };\n\n  return {\n    ...baseTheme,\n    ...customTheme,\n    fontSize: {\n      ...baseTheme.fontSize,\n      ...customTheme.fontSize\n    },\n    spacing: {\n      ...baseTheme.spacing,\n      ...customTheme.spacing\n    },\n    shadows: {\n      ...baseTheme.shadows,\n      ...customTheme.shadows\n    },\n    icons: {\n      ...baseTheme.icons,\n      ...customTheme.icons\n    },\n    components: {\n      ...baseTheme.components,\n      ...customTheme.components\n    }\n  };\n};\n\n/**\n * Creates a dark theme variant\n */\nexport const createDarkTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const darkDefaults: Partial<AdMeshTheme> = {\n    mode: 'dark',\n    backgroundColor: '#1f2937',\n    surfaceColor: '#374151',\n    borderColor: '#4b5563',\n    textColor: '#f9fafb',\n    textSecondaryColor: '#9ca3af',\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.3)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.3)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.3)'\n    }\n  };\n\n  return createAdMeshTheme({\n    ...darkDefaults,\n    ...customTheme\n  });\n};\n\n/**\n * Predefined theme presets for common AI platforms\n */\nexport const themePresets = {\n  // Clean, minimal theme\n  minimal: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#666666',\n    borderRadius: '4px',\n    shadows: {\n      small: 'none',\n      medium: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      large: '0 2px 6px rgba(0, 0, 0, 0.1)'\n    }\n  }),\n\n  // Modern, colorful theme\n  vibrant: createAdMeshTheme({\n    primaryColor: '#8b5cf6',\n    secondaryColor: '#06b6d4',\n    accentColor: '#f59e0b',\n    borderRadius: '12px',\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #10b981 100%)',\n      accent: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)'\n    }\n  }),\n\n  // Professional, corporate theme\n  corporate: createAdMeshTheme({\n    primaryColor: '#1e40af',\n    secondaryColor: '#059669',\n    backgroundColor: '#f8fafc',\n    surfaceColor: '#ffffff',\n    borderColor: '#cbd5e1',\n    borderRadius: '6px',\n    fontFamily: '\"Inter\", -apple-system, BlinkMacSystemFont, sans-serif'\n  }),\n\n  // High contrast theme for accessibility\n  highContrast: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#ffffff',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f5f5f5',\n    borderColor: '#000000',\n    textColor: '#000000',\n    textSecondaryColor: '#333333',\n    borderRadius: '0px',\n    shadows: {\n      small: 'none',\n      medium: '0 0 0 2px #000000',\n      large: '0 0 0 3px #000000'\n    }\n  })\n};\n\n/**\n * Utility to merge multiple theme objects\n */\nexport const mergeThemes = (...themes: Partial<AdMeshTheme>[]): AdMeshTheme => {\n  const baseTheme = createAdMeshTheme();\n  return themes.reduce((merged, theme) => {\n    if (!theme) return merged;\n    return createAdMeshTheme({\n      ...merged,\n      ...theme\n    });\n  }, baseTheme);\n};\n\n/**\n * Utility to create a theme from CSS custom properties\n */\nexport const themeFromCSSProperties = (element: HTMLElement): Partial<AdMeshTheme> => {\n  const computedStyle = getComputedStyle(element);\n  \n  return {\n    primaryColor: computedStyle.getPropertyValue('--admesh-primary-color')?.trim() || undefined,\n    secondaryColor: computedStyle.getPropertyValue('--admesh-secondary-color')?.trim() || undefined,\n    backgroundColor: computedStyle.getPropertyValue('--admesh-background-color')?.trim() || undefined,\n    textColor: computedStyle.getPropertyValue('--admesh-text-color')?.trim() || undefined,\n    borderRadius: computedStyle.getPropertyValue('--admesh-border-radius')?.trim() || undefined,\n    fontFamily: computedStyle.getPropertyValue('--admesh-font-family')?.trim() || undefined\n  };\n};\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n\n  AdMeshLinkTracker,\n\n  AdMeshExpandableUnit,\n  AdMeshConversationSummary,\n  AdMeshCitationUnit,\n  AdMeshInlineRecommendation,\n  AdMeshConversationalUnit,\n  AdMeshCitationReference,\n  AdMeshFloatingChat,\n  AdMeshChatInterface,\n  AdMeshChatMessage,\n  AdMeshChatInput,\n  AdMeshSidebar,\n  AdMeshSidebarHeader,\n  AdMeshSidebarContent,\n  AdMeshAutoRecommendationWidget\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\nexport {\n  useAdMeshStyles\n} from './hooks/useAdMeshStyles';\n\n// Export theme utilities\nexport {\n  createAdMeshTheme,\n  createDarkTheme,\n  themePresets,\n  mergeThemes,\n  themeFromCSSProperties\n} from './utils/themeUtils';\n\n// Export disclosure utilities\nexport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getSectionDisclosure,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText,\n  getCtaText,\n  hasHighQualityMatches,\n  getPoweredByText\n} from './utils/disclosureUtils';\n\nexport type {\n  DisclosureConfig\n} from './utils/disclosureUtils';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n\n  AdMeshLinkTrackerProps,\n\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig,\n  ConversationalDisplayMode,\n  ConversationContext,\n  ConversationalAdConfig,\n  AdMeshConversationSummaryProps,\n  AdMeshCitationUnitProps,\n  AdMeshInlineRecommendationProps,\n  AdMeshChatInputProps,\n  AdMeshChatMessageProps,\n  AdMeshChatInterfaceProps,\n  AdMeshFloatingChatProps,\n  AdMeshCitationReferenceProps,\n  AdMeshConversationalUnitProps,\n  ChatMessage,\n  SidebarPosition,\n  SidebarSize,\n  SidebarDisplayMode,\n  AdMeshSidebarConfig,\n  AdMeshSidebarProps,\n  SidebarFilters,\n  AdMeshSidebarHeaderProps,\n  AdMeshSidebarContentProps,\n\n} from './types/index';\n\n// Version info\nexport const VERSION = '0.2.1';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "i", "arg", "appendClass", "parseValue", "newClass", "module", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "useMemo", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "trackingData", "className", "style", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "jsx", "ADMESH_STYLES", "stylesInjected", "useAdMeshStyles", "styleElement", "existingStyle", "getRecommendationLabel", "matchScore", "customLabels", "getLabelTooltip", "_label", "getSectionDisclosure", "hasHighMatches", "isExpanded", "getInlineDisclosure", "compact", "getInlineTooltip", "getBadgeText", "badgeType", "getCtaText", "context", "productName", "hasHighQualityMatches", "recommendations", "rec", "getPoweredByText", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "variation", "expandable", "setIsExpanded", "badges", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "badge", "inlineDisclosure", "inlineTooltip", "matchScorePercentage", "content", "variations", "cardClasses", "cardStyle", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "jsxs", "_k", "_l", "Fragment", "e", "feature", "j", "integration", "_m", "AdMeshCompareTable", "maxProducts", "showMatchScores", "showFeatures", "productsToCompare", "containerClasses", "containerStyle", "product", "index", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "effectiveVariant", "icon", "badgeClasses", "AdMeshExpandableUnit", "showPoweredBy", "initialExpanded", "sections", "ctaText", "collapsible", "handleToggleExpand", "apiFeatureSections", "defaultSections", "displaySections", "displayCtaText", "colors", "customStyles", "section", "AdMeshInlineRecommendation", "showReason", "AdMeshConversationSummary", "conversation<PERSON><PERSON><PERSON><PERSON>", "showTopRecommendations", "onRecommendationClick", "onStartNewConversation", "topRecommendations", "a", "b", "AdMeshCitationReference", "citationNumber", "citationStyle", "showTooltip", "onHover", "isHovered", "setIsHovered", "handleMouseEnter", "handleMouseLeave", "getCitationDisplay", "citationClasses", "AdMeshCitationUnit", "conversationText", "showCitationList", "onCitationHover", "hoveredRecommendation", "setHoveredRecommendation", "processedContent", "processedText", "citationMap", "title", "titleRegex", "match", "keywords", "inserted", "keywordRegex", "renderTextWithCitations", "text", "part", "citationMatch", "AdMeshConversationalUnit", "sessionId", "on<PERSON><PERSON><PERSON>", "isVisible", "setIsVisible", "hasAnimated", "setHasAnimated", "timer", "maxRecommendations", "displayRecommendations", "handleRecommendationClick", "handle<PERSON><PERSON><PERSON>", "renderContent", "AdMeshChatMessage", "isUser", "isAssistant", "messageClasses", "bubbleClasses", "formatTime", "timestamp", "AdMeshChatInput", "placeholder", "disabled", "suggestions", "onSendMessage", "setMessage", "showSuggestions", "setShowSuggestions", "filteredSuggestions", "setFilteredSuggestions", "inputRef", "handleInputChange", "filtered", "suggestion", "handleKeyDown", "handleSend", "trimmedMessage", "handleSuggestionClick", "inputClasses", "sendButtonClasses", "AdMeshChatInterface", "messages", "isLoading", "messagesEndRef", "messagesContainerRef", "displayMessages", "AdMeshFloatingChat", "subtitle", "controlledIsOpen", "onToggle", "autoRecommendations", "autoRecommendationTrigger", "showInputField", "autoShowRecommendations", "onAutoRecommendationDismiss", "internalIsOpen", "setInternalIsOpen", "setMessages", "setIsLoading", "hasInteracted", "setHasInteracted", "isOpen", "welcomeMessage", "autoMessage", "prev", "msg", "handleToggle", "handleSendMessage", "messageContent", "userMessage", "errorMessage", "getChatDimensions", "chatClasses", "AdMeshSidebarHeader", "isCollapsed", "onSearch", "showSearch", "searchQuery", "setSearch<PERSON>uery", "isSearchFocused", "setIsSearchFocused", "isMobile", "setIsMobile", "checkMobile", "handleSearchChange", "handleSearchClear", "headerClasses", "AdMeshSidebarContent", "displayMode", "showFilters", "setShowFilters", "activeTab", "setActiveTab", "tabRecommendations", "contentClasses", "renderRecommendations", "AdMeshSidebar", "containerMode", "setIsCollapsed", "filters", "originalStyle", "interval", "filteredRecommendations", "query", "cat", "handleSearch", "sidebarClasses", "AdMeshAutoRecommendationWidget", "trigger", "position", "autoShow", "showDelay", "getWidgetDimensions", "getPositionClasses", "createAdMeshTheme", "customTheme", "baseTheme", "createDarkTheme", "themePresets", "mergeThemes", "themes", "merged", "themeFromCSSProperties", "element", "computedStyle", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;;;;;;;;;;;AAWA,MAAIA,IAAqB,OAAO,IAAI,4BAA4B,GAC9DC,IAAsB,OAAO,IAAI,gBAAgB;AACnD,WAASC,EAAQC,GAAMC,GAAQC,GAAU;AACvC,QAAIC,IAAM;AAGV,QAFWD,MAAX,WAAwBC,IAAM,KAAKD,IACxBD,EAAO,QAAlB,WAA0BE,IAAM,KAAKF,EAAO,MACxC,SAASA,GAAQ;AACnB,MAAAC,IAAW,CAAA;AACX,eAASE,KAAYH;AACnB,QAAUG,MAAV,UAAuBF,EAASE,CAAQ,IAAIH,EAAOG,CAAQ;AAAA,UACxD,CAAAF,IAAWD;AAClB,WAAAA,IAASC,EAAS,KACX;AAAA,MACL,UAAUL;AAAA,MACV,MAAMG;AAAA,MACN,KAAKG;AAAA,MACL,KAAgBF,MAAX,SAAoBA,IAAS;AAAA,MAClC,OAAOC;AAAA;EAEX;AACA,SAAAG,EAAA,WAAmBP,GACnBO,EAAA,MAAcN,GACdM,EAAA,OAAeN;;;;;;;;;;;;;;wBCtBE,QAAQ,IAAI,aAA7B,gBACG,WAAY;AACX,aAASO,EAAyBN,GAAM;AACtC,UAAYA,KAAR,KAAc,QAAO;AACzB,UAAmB,OAAOA,KAAtB;AACF,eAAOA,EAAK,aAAaO,IACrB,OACAP,EAAK,eAAeA,EAAK,QAAQ;AACvC,UAAiB,OAAOA,KAApB,SAA0B,QAAOA;AACrC,cAAQA,GAAI;AAAA,QACV,KAAKF;AACH,iBAAO;AAAA,QACT,KAAKU;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,QACT,KAAKC;AACH,iBAAO;AAAA,MACjB;AACM,UAAiB,OAAOZ,KAApB;AACF,gBACgB,OAAOA,EAAK,OAAzB,YACC,QAAQ;AAAA,UACN;AAAA,WAEJA,EAAK,UACf;AAAA,UACU,KAAKa;AACH,mBAAO;AAAA,UACT,KAAKC;AACH,oBAAQd,EAAK,eAAe,aAAa;AAAA,UAC3C,KAAKe;AACH,oBAAQf,EAAK,SAAS,eAAe,aAAa;AAAA,UACpD,KAAKgB;AACH,gBAAIC,IAAYjB,EAAK;AACrB,mBAAAA,IAAOA,EAAK,aACZA,MACIA,IAAOiB,EAAU,eAAeA,EAAU,QAAQ,IACnDjB,IAAcA,MAAP,KAAc,gBAAgBA,IAAO,MAAM,eAC9CA;AAAA,UACT,KAAKkB;AACH,mBACGD,IAAYjB,EAAK,eAAe,MACxBiB,MAAT,OACIA,IACAX,EAAyBN,EAAK,IAAI,KAAK;AAAA,UAE/C,KAAKmB;AACH,YAAAF,IAAYjB,EAAK,UACjBA,IAAOA,EAAK;AACZ,gBAAI;AACF,qBAAOM,EAAyBN,EAAKiB,CAAS,CAAC;AAAA,oBACrC;AAAA,YAAA;AAAA,QACxB;AACM,aAAO;AAAA,IACb;AACI,aAASG,EAAmBC,GAAO;AACjC,aAAO,KAAKA;AAAA,IAClB;AACI,aAASC,EAAuBD,GAAO;AACrC,UAAI;AACF,QAAAD,EAAmBC,CAAK;AACxB,YAAIE,IAA2B;AAAA,cACrB;AACV,QAAAA,IAA2B;AAAA,MACnC;AACM,UAAIA,GAA0B;AAC5B,QAAAA,IAA2B;AAC3B,YAAIC,IAAwBD,EAAyB,OACjDE,IACc,OAAO,UAAtB,cACC,OAAO,eACPJ,EAAM,OAAO,WAAW,KAC1BA,EAAM,YAAY,QAClB;AACF,eAAAG,EAAsB;AAAA,UACpBD;AAAA,UACA;AAAA,UACAE;AAAA,WAEKL,EAAmBC,CAAK;AAAA,MACvC;AAAA,IACA;AACI,aAASK,EAAY1B,GAAM;AACzB,UAAIA,MAASF,EAAqB,QAAO;AACzC,UACe,OAAOE,KAApB,YACSA,MAAT,QACAA,EAAK,aAAamB;AAElB,eAAO;AACT,UAAI;AACF,YAAIQ,IAAOrB,EAAyBN,CAAI;AACxC,eAAO2B,IAAO,MAAMA,IAAO,MAAM;AAAA,cACvB;AACV,eAAO;AAAA,MACf;AAAA,IACA;AACI,aAASC,IAAW;AAClB,UAAIC,IAAaC,EAAqB;AACtC,aAAgBD,MAAT,OAAsB,OAAOA,EAAW,SAAQ;AAAA,IAC7D;AACI,aAASE,IAAe;AACtB,aAAO,MAAM,uBAAuB;AAAA,IAC1C;AACI,aAASC,EAAY/B,GAAQ;AAC3B,UAAIgC,EAAe,KAAKhC,GAAQ,KAAK,GAAG;AACtC,YAAIiC,IAAS,OAAO,yBAAyBjC,GAAQ,KAAK,EAAE;AAC5D,YAAIiC,KAAUA,EAAO,eAAgB,QAAO;AAAA,MACpD;AACM,aAAkBjC,EAAO,QAAlB;AAAA,IACb;AACI,aAASkC,EAA2BC,GAAOC,GAAa;AACtD,eAASC,IAAwB;AAC/B,QAAAC,MACIA,IAA6B,IAC/B,QAAQ;AAAA,UACN;AAAA,UACAF;AAAA,QACZ;AAAA,MACA;AACM,MAAAC,EAAsB,iBAAiB,IACvC,OAAO,eAAeF,GAAO,OAAO;AAAA,QAClC,KAAKE;AAAA,QACL,cAAc;AAAA,MACtB,CAAO;AAAA,IACP;AACI,aAASE,IAAyC;AAChD,UAAIC,IAAgBnC,EAAyB,KAAK,IAAI;AACtD,aAAAoC,EAAuBD,CAAa,MAChCC,EAAuBD,CAAa,IAAI,IAC1C,QAAQ;AAAA,QACN;AAAA,MACV,IACMA,IAAgB,KAAK,MAAM,KACTA,MAAX,SAA2BA,IAAgB;AAAA,IACxD;AACI,aAASE,EACP3C,GACAG,GACAyC,GACAC,GACAC,GACAV,GACAW,IACAC,IACA;AACA,aAAAJ,IAAOR,EAAM,KACbpC,IAAO;AAAA,QACL,UAAUH;AAAA,QACV,MAAMG;AAAA,QACN,KAAKG;AAAA,QACL,OAAOiC;AAAA,QACP,QAAQU;AAAA,UAEWF,MAAX,SAAkBA,IAAO,UAAnC,OACI,OAAO,eAAe5C,GAAM,OAAO;AAAA,QACjC,YAAY;AAAA,QACZ,KAAKwC;AAAA,OACN,IACD,OAAO,eAAexC,GAAM,OAAO,EAAE,YAAY,IAAI,OAAO,MAAM,GACtEA,EAAK,SAAS,CAAA,GACd,OAAO,eAAeA,EAAK,QAAQ,aAAa;AAAA,QAC9C,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACf,CAAO,GACD,OAAO,eAAeA,GAAM,cAAc;AAAA,QACxC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACf,CAAO,GACD,OAAO,eAAeA,GAAM,eAAe;AAAA,QACzC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO+C;AAAA,MACf,CAAO,GACD,OAAO,eAAe/C,GAAM,cAAc;AAAA,QACxC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAOgD;AAAA,MACf,CAAO,GACD,OAAO,WAAW,OAAO,OAAOhD,EAAK,KAAK,GAAG,OAAO,OAAOA,CAAI,IACxDA;AAAA,IACb;AACI,aAASiD,EACPjD,GACAC,GACAC,GACAgD,GACAL,GACAD,GACAG,IACAC,IACA;AACA,UAAIG,IAAWlD,EAAO;AACtB,UAAekD,MAAX;AACF,YAAID;AACF,cAAIE,EAAYD,CAAQ,GAAG;AACzB,iBACED,IAAmB,GACnBA,IAAmBC,EAAS,QAC5BD;AAEA,cAAAG,EAAkBF,EAASD,CAAgB,CAAC;AAC9C,mBAAO,UAAU,OAAO,OAAOC,CAAQ;AAAA;AAEvC,oBAAQ;AAAA,cACN;AAAA;YAED,CAAAE,EAAkBF,CAAQ;AACjC,UAAIlB,EAAe,KAAKhC,GAAQ,KAAK,GAAG;AACtC,QAAAkD,IAAW7C,EAAyBN,CAAI;AACxC,YAAIsD,IAAO,OAAO,KAAKrD,CAAM,EAAE,OAAO,SAAUsD,IAAG;AACjD,iBAAiBA,OAAV;AAAA,QACjB,CAAS;AACD,QAAAL,IACE,IAAII,EAAK,SACL,oBAAoBA,EAAK,KAAK,SAAS,IAAI,WAC3C,kBACNE,EAAsBL,IAAWD,CAAgB,MAC7CI,IACA,IAAIA,EAAK,SAAS,MAAMA,EAAK,KAAK,SAAS,IAAI,WAAW,MAC5D,QAAQ;AAAA,UACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UACAJ;AAAA,UACAC;AAAA,UACAG;AAAA,UACAH;AAAA,WAEDK,EAAsBL,IAAWD,CAAgB,IAAI;AAAA,MAChE;AAMM,UALAC,IAAW,MACAjD,MAAX,WACGoB,EAAuBpB,CAAQ,GAAIiD,IAAW,KAAKjD,IACtD8B,EAAY/B,CAAM,MACfqB,EAAuBrB,EAAO,GAAG,GAAIkD,IAAW,KAAKlD,EAAO,MAC3D,SAASA,GAAQ;AACnB,QAAAC,IAAW,CAAA;AACX,iBAASE,MAAYH;AACnB,UAAUG,OAAV,UAAuBF,EAASE,EAAQ,IAAIH,EAAOG,EAAQ;AAAA,YACxD,CAAAF,IAAWD;AAClB,aAAAkD,KACEhB;AAAA,QACEjC;AAAA,QACe,OAAOF,KAAtB,aACIA,EAAK,eAAeA,EAAK,QAAQ,YACjCA;AAAA,SAED2C;AAAA,QACL3C;AAAA,QACAmD;AAAA,QACAP;AAAA,QACAC;AAAA,QACAjB,EAAQ;AAAA,QACR1B;AAAA,QACA6C;AAAA,QACAC;AAAA;IAER;AACI,aAASK,EAAkBI,GAAM;AAC/B,MAAa,OAAOA,KAApB,YACWA,MAAT,QACAA,EAAK,aAAa5D,KAClB4D,EAAK,WACJA,EAAK,OAAO,YAAY;AAAA,IACjC;AACI,QAAIC,IAAQC,IACV9D,IAAqB,OAAO,IAAI,4BAA4B,GAC5DgB,IAAoB,OAAO,IAAI,cAAc,GAC7Cf,IAAsB,OAAO,IAAI,gBAAgB,GACjDW,IAAyB,OAAO,IAAI,mBAAmB,GACvDD,IAAsB,OAAO,IAAI,gBAAgB,GAE/CO,IAAsB,OAAO,IAAI,gBAAgB,GACnDD,IAAqB,OAAO,IAAI,eAAe,GAC/CE,IAAyB,OAAO,IAAI,mBAAmB,GACvDN,IAAsB,OAAO,IAAI,gBAAgB,GACjDC,IAA2B,OAAO,IAAI,qBAAqB,GAC3DO,IAAkB,OAAO,IAAI,YAAY,GACzCC,IAAkB,OAAO,IAAI,YAAY,GACzCP,IAAsB,OAAO,IAAI,gBAAgB,GACjDL,IAAyB,OAAO,IAAI,wBAAwB,GAC5DuB,IACE4B,EAAM,iEACRzB,IAAiB,OAAO,UAAU,gBAClCmB,IAAc,MAAM,SACpBQ,IAAa,QAAQ,aACjB,QAAQ,aACR,WAAY;AACV,aAAO;AAAA;AAEf,IAAAF,IAAQ;AAAA,MACN,4BAA4B,SAAUG,GAAmB;AACvD,eAAOA,EAAiB;AAAA,MAChC;AAAA;AAEI,QAAItB,GACAG,IAAyB,CAAA,GACzBoB,IAAyBJ,EAAM,0BAA0B,EAAE;AAAA,MAC7DA;AAAA,MACA3B;AAAA,IACN,EAAK,GACGgC,IAAwBH,EAAWlC,EAAYK,CAAY,CAAC,GAC5DyB,IAAwB,CAAA;AAC5B,IAAAQ,GAAA,WAAmBlE,GACnBkE,GAAA,MAAc,SAAUhE,GAAMC,GAAQC,GAAU2C,GAAQD,GAAM;AAC5D,UAAIqB,IACF,MAAMnC,EAAqB;AAC7B,aAAOmB;AAAA,QACLjD;AAAA,QACAC;AAAA,QACAC;AAAA,QACA;AAAA,QACA2C;AAAA,QACAD;AAAA,QACAqB,IACI,MAAM,uBAAuB,IAC7BH;AAAA,QACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;AAAA;OAGvDC,GAAA,OAAe,SAAUhE,GAAMC,GAAQC,GAAU2C,GAAQD,GAAM;AAC7D,UAAIqB,IACF,MAAMnC,EAAqB;AAC7B,aAAOmB;AAAA,QACLjD;AAAA,QACAC;AAAA,QACAC;AAAA,QACA;AAAA,QACA2C;AAAA,QACAD;AAAA,QACAqB,IACI,MAAM,uBAAuB,IAC7BH;AAAA,QACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;AAAA;;EAG3D,EAAG;;;;wBCnWC,QAAQ,IAAI,aAAa,eAC3BG,GAAA,UAAiBP,GAAA,IAEjBO,GAAA,UAAiBC,GAAA;;;;;;;;;;;ACEnB,KAAC,WAAY;AAGZ,UAAIC,IAAS,CAAA,EAAG;AAEhB,eAASC,IAAc;AAGtB,iBAFIC,IAAU,IAELC,IAAI,GAAGA,IAAI,UAAU,QAAQA,KAAK;AAC1C,cAAIC,IAAM,UAAUD,CAAC;AACrB,UAAIC,MACHF,IAAUG,EAAYH,GAASI,EAAWF,CAAG,CAAC;AAAA,QAElD;AAEE,eAAOF;AAAA,MACT;AAEC,eAASI,EAAYF,GAAK;AACzB,YAAI,OAAOA,KAAQ,YAAY,OAAOA,KAAQ;AAC7C,iBAAOA;AAGR,YAAI,OAAOA,KAAQ;AAClB,iBAAO;AAGR,YAAI,MAAM,QAAQA,CAAG;AACpB,iBAAOH,EAAW,MAAM,MAAMG,CAAG;AAGlC,YAAIA,EAAI,aAAa,OAAO,UAAU,YAAY,CAACA,EAAI,SAAS,SAAQ,EAAG,SAAS,eAAe;AAClG,iBAAOA,EAAI,SAAQ;AAGpB,YAAIF,IAAU;AAEd,iBAASnE,KAAOqE;AACf,UAAIJ,EAAO,KAAKI,GAAKrE,CAAG,KAAKqE,EAAIrE,CAAG,MACnCmE,IAAUG,EAAYH,GAASnE,CAAG;AAIpC,eAAOmE;AAAA,MACT;AAEC,eAASG,EAAapD,GAAOsD,GAAU;AACtC,eAAKA,IAIDtD,IACIA,IAAQ,MAAMsD,IAGftD,IAAQsD,IAPPtD;AAAA,MAQV;AAEC,MAAqCuD,EAAO,WAC3CP,EAAW,UAAUA,GACrBO,YAAiBP,KAOjB,OAAO,aAAaA;AAAA,IAEtB;;;;kCCxEMQ,KAAuB;AAW7B,IAAIC,KAA+B;AAAA,EACjC,YAAYD;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,eAAe;AAAA,EACf,YAAY;AACd;AAEO,MAAME,KAAyB,CAAC9E,MAAoC;AACzE,EAAA6E,KAAe,EAAE,GAAGA,IAAc,GAAG7E,EAAA;AACvC,GAEa+E,KAAmB,CAAC/E,MAA6D;AAC5F,QAAM,CAACgF,GAAYC,CAAa,IAAIC,EAAS,EAAK,GAC5C,CAACC,GAAOC,CAAQ,IAAIF,EAAwB,IAAI,GAEhDG,IAAeC,GAAQ,OAAO,EAAE,GAAGT,IAAc,GAAG7E,EAAA,IAAW,CAACA,CAAM,CAAC,GAEvEuF,IAAMC,EAAY,CAACC,GAAiBC,MAAmB;AAC3D,IAAIL,EAAa,SACf,QAAQ,IAAI,oBAAoBI,CAAO,IAAIC,CAAI;AAAA,EACjD,GACC,CAACL,EAAa,KAAK,CAAC,GAEjBM,IAAoBH,EAAY,OACpCI,GACAF,MACkB;AAClB,QAAI,CAACL,EAAa,SAAS;AACzB,MAAAE,EAAI,qCAAqC,EAAE,WAAAK,GAAW,MAAAF,EAAA,CAAM;AAC5D;AAAA,IAAA;AAGF,QAAI,CAACA,EAAK,QAAQ,CAACA,EAAK,YAAY;AAClC,YAAMG,IAAW;AACjB,MAAAN,EAAIM,GAAUH,CAAI,GAClBN,EAASS,CAAQ;AACjB;AAAA,IAAA;AAGF,IAAAZ,EAAc,EAAI,GAClBG,EAAS,IAAI;AAEb,UAAMU,IAAU;AAAA,MACd,YAAYF;AAAA,MACZ,OAAOF,EAAK;AAAA,MACZ,aAAaA,EAAK;AAAA,MAClB,YAAYA,EAAK;AAAA,MACjB,SAASA,EAAK;AAAA,MACd,YAAYA,EAAK;AAAA,MACjB,SAASA,EAAK;AAAA,MACd,iBAAiBA,EAAK;AAAA,MACtB,UAAUA,EAAK;AAAA,MACf,YAAW,oBAAI,KAAA,GAAO,YAAA;AAAA,MACtB,YAAY,UAAU;AAAA,MACtB,UAAU,SAAS;AAAA,MACnB,UAAU,OAAO,SAAS;AAAA,IAAA;AAG5B,IAAAH,EAAI,WAAWK,CAAS,UAAUE,CAAO;AAEzC,QAAIC,IAA0B;AAE9B,aAASC,IAAU,GAAGA,MAAYX,EAAa,iBAAiB,IAAIW;AAClE,UAAI;AACF,cAAMC,IAAW,MAAM,MAAM,GAAGZ,EAAa,UAAU,WAAW;AAAA,UAChE,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,UAAA;AAAA,UAElB,MAAM,KAAK,UAAUS,CAAO;AAAA,QAAA,CAC7B;AAED,YAAI,CAACG,EAAS;AACZ,gBAAM,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE;AAGnE,cAAMC,IAAS,MAAMD,EAAS,KAAA;AAC9B,QAAAV,EAAI,GAAGK,CAAS,+BAA+BM,CAAM,GACrDjB,EAAc,EAAK;AACnB;AAAA,MAAA,SAEOkB,GAAK;AACZ,QAAAJ,IAAYI,GACZZ,EAAI,WAAWS,CAAO,eAAeJ,CAAS,UAAUO,CAAG,GAEvDH,KAAWX,EAAa,iBAAiB,MAC3C,MAAM,IAAI;AAAA,UAAQ,OAChB,WAAWe,IAAUf,EAAa,cAAc,OAAQW,CAAO;AAAA,QAAA;AAAA,MAEnE;AAKJ,UAAMH,IAAW,mBAAmBD,CAAS,gBAAgBP,EAAa,aAAa,cAAcU,KAAA,gBAAAA,EAAW,OAAO;AACvH,IAAAR,EAAIM,GAAUE,CAAS,GACvBX,EAASS,CAAQ,GACjBZ,EAAc,EAAK;AAAA,EAAA,GAClB,CAACI,GAAcE,CAAG,CAAC,GAEhBc,IAAab,EAAY,OAAOE,MAC7BC,EAAkB,SAASD,CAAI,GACrC,CAACC,CAAiB,CAAC,GAEhBW,IAAYd,EAAY,OAAOE,MAC5BC,EAAkB,QAAQD,CAAI,GACpC,CAACC,CAAiB,CAAC,GAEhBY,IAAkBf,EAAY,OAAOE,OACrC,CAACA,EAAK,WAAW,CAACA,EAAK,kBACzBH,EAAI,mEAAmEG,CAAI,GAEtEC,EAAkB,cAAcD,CAAI,IAC1C,CAACC,GAAmBJ,CAAG,CAAC;AAE3B,SAAO;AAAA,IACL,YAAAc;AAAA,IACA,WAAAC;AAAA,IACA,iBAAAC;AAAA,IACA,YAAAvB;AAAA,IACA,OAAAG;AAAA,EAAA;AAEJ,GAGaqB,KAAkB,CAC7BC,GACAC,GACAC,MACW;AACX,MAAI;AACF,UAAMC,IAAM,IAAI,IAAIH,CAAQ;AAC5B,WAAAG,EAAI,aAAa,IAAI,SAASF,CAAI,GAClCE,EAAI,aAAa,IAAI,cAAc,QAAQ,GAC3CA,EAAI,aAAa,IAAI,cAAc,gBAAgB,GAE/CD,KACF,OAAO,QAAQA,CAAgB,EAAE,QAAQ,CAAC,CAACzG,GAAKkB,CAAK,MAAM;AACzD,MAAAwF,EAAI,aAAa,IAAI1G,GAAKkB,CAAK;AAAA,IAAA,CAChC,GAGIwF,EAAI,SAAA;AAAA,EAAS,SACbT,GAAK;AACZ,mBAAQ,KAAK,qDAAqDM,GAAUN,CAAG,GACxEM;AAAA,EAAA;AAEX,GAGaI,KAAsB,CACjCC,GACAC,OAEO;AAAA,EACL,MAAMD,EAAe;AAAA,EACrB,YAAYA,EAAe;AAAA,EAC3B,WAAWA,EAAe;AAAA,EAC1B,GAAGC;AAAA,IC1KMC,IAAsD,CAAC;AAAA,EAClE,MAAAN;AAAA,EACA,YAAAO;AAAA,EACA,WAAAC;AAAA,EACA,UAAAhE;AAAA,EACA,cAAAiE;AAAA,EACA,WAAAC;AAAA,EACA,OAAAC;AACF,MAAM;AACJ,QAAM,EAAE,YAAAhB,GAAY,WAAAC,EAAA,IAAcvB,GAAA,GAC5BuC,IAAaC,GAAuB,IAAI,GACxCC,IAAiBD,GAAO,EAAK;AAGnC,EAAAE,EAAU,MAAM;AACd,QAAI,CAACH,EAAW,WAAWE,EAAe,QAAS;AAEnD,UAAME,IAAW,IAAI;AAAA,MACnB,CAACC,MAAY;AACX,QAAAA,EAAQ,QAAQ,CAACC,MAAU;AACzB,UAAIA,EAAM,kBAAkB,CAACJ,EAAe,YAC1CA,EAAe,UAAU,IACzBlB,EAAU;AAAA,YACR,MAAAI;AAAA,YACA,YAAAO;AAAA,YACA,WAAAC;AAAA,YACA,GAAGC;AAAA,UAAA,CACJ,EAAE,MAAM,QAAQ,KAAK;AAAA,QACxB,CACD;AAAA,MAAA;AAAA,MAEH;AAAA,QACE,WAAW;AAAA;AAAA,QACX,YAAY;AAAA,MAAA;AAAA,IACd;AAGF,WAAAO,EAAS,QAAQJ,EAAW,OAAO,GAE5B,MAAM;AACX,MAAAI,EAAS,WAAA;AAAA,IAAW;AAAA,EACtB,GACC,CAAChB,GAAMO,GAAYC,GAAWC,GAAcb,CAAS,CAAC;AAEzD,QAAMuB,IAAcrC,EAAY,OAAOsC,MAA4B;AAEjE,QAAI;AACF,YAAMzB,EAAW;AAAA,QACf,MAAAK;AAAA,QACA,YAAAO;AAAA,QACA,WAAAC;AAAA,QACA,GAAGC;AAAA,MAAA,CACJ;AAAA,IAAA,SACMhC,GAAO;AACd,cAAQ,MAAM,0BAA0BA,CAAK;AAAA,IAAA;AAU/C,IAHe2C,EAAM,OACD,QAAQ,GAAG,KAI7B,OAAO,KAAKb,GAAY,UAAU,qBAAqB;AAAA,EACzD,GAEC,CAACP,GAAMO,GAAYC,GAAWC,GAAcd,CAAU,CAAC;AAE1D,SACE0B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,KAAKT;AAAA,MACL,WAAAF;AAAA,MACA,SAASS;AAAA,MACT,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,GAAGR;AAAA,MAAA;AAAA,MAGJ,UAAAnE;AAAA,IAAA;AAAA,EAAA;AAGP;AAEA8D,EAAkB,cAAc;ACvFhC,MAAMgB,KAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsoBtB,IAAIC,KAAiB;AAEd,MAAMC,KAAkB,MAAM;AACnC,EAAAT,EAAU,MAAM;AACd,QAAIQ,GAAgB;AAGpB,UAAME,IAAe,SAAS,cAAc,OAAO;AACnD,WAAAA,EAAa,KAAK,wBAClBA,EAAa,cAAcH,IAGtB,SAAS,eAAe,sBAAsB,MACjD,SAAS,KAAK,YAAYG,CAAY,GACtCF,KAAiB,KAIZ,MAAM;AACX,YAAMG,IAAgB,SAAS,eAAe,sBAAsB;AACpE,MAAIA,KAAiB,SAAS,KAAK,SAASA,CAAa,MACvD,SAAS,KAAK,YAAYA,CAAa,GACvCH,KAAiB;AAAA,IACnB;AAAA,EACF,GACC,EAAE;AACP,GC/oBaI,IAAyB,CACpCvB,GACA9G,IAA2B,OAChB;AACX,QAAMsI,IAAaxB,EAAe,sBAAsB,GAClDyB,IAAevI,EAAO,gBAAgB,CAAA;AAG5C,SAAIsI,KAAc,MACTC,EAAa,aAAa,eAI/BD,KAAc,MACTC,EAAa,gBAAgB,kBAIlCD,KAAc,MACTC,EAAa,kBAAkB,oBAIjCA,EAAa,iBAAiB;AACvC,GAKaC,KAAkB,CAC7B1B,GACA2B,MACW;AACX,QAAMH,IAAaxB,EAAe,sBAAsB;AAExD,SAAIwB,KAAc,MACT,kIAGLA,KAAc,MACT,sHAGLA,KAAc,MACT,sHAGF;AACT,GAKaI,KAAuB,CAClCC,IAA0B,IAC1BC,IAAsB,OAEjBD,IAIDC,IACK,qFAGF,qLAPE,iKAaEC,KAAsB,CACjC/B,GACAgC,IAAmB,OACR;AACX,QAAMR,IAAaxB,EAAe,sBAAsB;AAExD,SAAIgC,IACK,aAGLR,KAAc,MACT,yBAGLA,KAAc,MACT,kBAGF;AACT,GAKaS,KAAmB,MACvB,uKAMIC,KAAe,CAACC,OACc;AAAA,EACvC,aAAa;AAAA,EACb,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,aAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAW;AAAA,EACX,KAAO;AAAA,EACP,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,GAGJA,CAAS,KAAKA,GAMnBC,KAAa,CACxBpC,GACAqC,IAA6B,aAClB;AACX,QAAMC,IAActC,EAAe,wBAAwBA,EAAe;AAE1E,SAAIqC,MAAY,SACPC,IAILtC,EAAe,cAAcA,EAAe,aAAa,IACpD,OAAOsC,CAAW,KAGpB;AACT,GAKaC,KAAwB,CAACC,MAC7BA,EAAgB,KAAK,CAAAC,OAAQA,EAAI,sBAAsB,MAAM,GAAG,GAM5DC,KAAmB,CAACV,IAAmB,OAC9CA,IACK,sBAGF,qCCzKIW,KAAsD,CAAC;AAAA,EAClE,gBAAA3C;AAAA,EACA,OAAA4C;AAAA,EACA,gBAAAC,IAAiB;AAAA,EACjB,YAAAC,IAAa;AAAA,EACb,WAAAC,IAAY;AAAA,EACZ,YAAAC,IAAa;AAAA,EACb,WAAA1C;AAAA,EACA,OAAAC;AACF,MAAM;;AAEJ,EAAAa,GAAA;AAGA,QAAM,CAACU,GAAYmB,CAAa,IAAI7E,EAAS,EAAK,GAE5C8E,IAAS1E,GAAQ,MAAmB;;AACxC,UAAM2E,IAA+B,CAAA;AAIrC,IADqB5B,EAAuBvB,CAAc,MACrC,gBACnBmD,EAAgB,KAAK,WAAW,GAI9BnD,EAAe,cAAcA,EAAe,aAAa,KAC3DmD,EAAgB,KAAK,iBAAiB;AAIxC,UAAMC,IAAa,CAAC,MAAM,2BAA2B,oBAAoB,MAAM,YAAY;AAK3F,cAJsBC,IAAArD,EAAe,aAAf,gBAAAqD,EAAyB;AAAA,MAAK,CAAAC,MAClDF,EAAW,KAAK,CAAAG,MAAMD,EAAQ,YAAA,EAAc,SAASC,CAAE,CAAC;AAAA,UACrDvD,EAAe,MAAM,YAAA,EAAc,SAAS,IAAI,MAGnDmD,EAAgB,KAAK,YAAY,GAI/BnD,EAAe,UAAUA,EAAe,OAAO,SAAS,KAC1DA,EAAe,OAAO,QAAQ,CAAAwD,MAAS;AAErC,MAAI,CAAC,aAAa,aAAa,cAAc,WAAW,OAAO,iBAAiB,EAAE,SAASA,CAAK,KAC5F,CAACL,EAAgB,SAASK,CAAkB,KAC9CL,EAAgB,KAAKK,CAAkB;AAAA,IACzC,CACD,GAKIL;AAAA,EAAA,GACN,CAACnD,CAAc,CAAC,GAGbyD,IAAmB1B,GAAoB/B,GAAgB,EAAK,GAC5D0D,IAAgBzB,GAAA,GAGhB0B,IAAuB,KAAK,MAAM3D,EAAe,qBAAqB,GAAG,GAmCzE4D,KAhCsB,MAAM;AAChC,UAAMC,IAAa7D,EAAe;AAElC,WAAI+C,MAAc,WACT;AAAA,MACL,OAAO/C,EAAe,wBAAwBA,EAAe;AAAA,MAC7D,aAAaA,EAAe,8BAA8BA,EAAe,eAAeA,EAAe;AAAA,MACvG,SAASA,EAAe,wBAAwBA,EAAe;AAAA,MAC/D,UAAU;AAAA,IAAA,IAEH+C,MAAc,eAAcc,KAAA,QAAAA,EAAY,YAC1C;AAAA,MACL,OAAOA,EAAW,SAAS,OAAO7D,EAAe,wBAAwBA,EAAe;AAAA,MACxF,aAAa6D,EAAW,SAAS;AAAA,MACjC,SAASA,EAAW,SAAS,OAAO7D,EAAe,wBAAwBA,EAAe;AAAA,IAAA,IAEnF+C,MAAc,gBAAec,KAAA,QAAAA,EAAY,aAC3C;AAAA,MACL,OAAO7D,EAAe,wBAAwBA,EAAe;AAAA,MAC7D,aAAa6D,EAAW,UAAU;AAAA,MAClC,SAASA,EAAW,UAAU,OAAO7D,EAAe,wBAAwBA,EAAe;AAAA,IAAA,IAItF;AAAA,MACL,OAAOA,EAAe,wBAAwBA,EAAe;AAAA,MAC7D,aAAaA,EAAe,8BAA8BA,EAAe,eAAeA,EAAe;AAAA,MACvG,SAASA,EAAe,wBAAwBA,EAAe;AAAA,IAAA;AAAA,EAEnE,GAGc,GAEV8D,IAAcxG;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIyD,IAAYnB,IAAQ;AAAA,IACxB,oBAAoBA,EAAM,gBAAgBA,EAAM,eAAe;AAAA,IAC/D,sBAAsBA,EAAM,kBAAkB;AAAA,IAC9C,mBAAmBA,EAAM,eAAe;AAAA,IACxC,uBAAuBA,EAAM;AAAA,IAC7B,oBAAoBA,EAAM;AAAA,IAC1B,mBAAmBA,EAAM;AAAA,IACzB,iBAAiBA,EAAM;AAAA,IACvB,2BAA2BA,EAAM;AAAA,IACjC,mBAAmBA,EAAM,gBAAgB;AAAA,IACzC,uBAAsBS,IAAAT,EAAM,YAAN,gBAAAS,EAAe;AAAA,IACrC,uBAAsBW,IAAApB,EAAM,YAAN,gBAAAoB,EAAe;AAAA,IACrC,uBAAsBC,IAAArB,EAAM,YAAN,gBAAAqB,EAAe;AAAA,IACrC,wBAAuBC,IAAAtB,EAAM,YAAN,gBAAAsB,EAAe;AAAA,IACtC,wBAAuBC,IAAAvB,EAAM,YAAN,gBAAAuB,EAAe;AAAA,IACtC,wBAAuBC,IAAAxB,EAAM,YAAN,gBAAAwB,EAAe;AAAA,IACtC,0BAAyBC,IAAAzB,EAAM,aAAN,gBAAAyB,EAAgB;AAAA,IACzC,4BAA2BC,IAAA1B,EAAM,aAAN,gBAAA0B,EAAgB;AAAA,IAC3C,0BAAyBC,IAAA3B,EAAM,aAAN,gBAAA2B,EAAgB;AAAA,IACzC,6BAA4BC,IAAA5B,EAAM,aAAN,gBAAA4B,EAAgB;AAAA,IAC5C,YAAY5B,EAAM;AAAA,EAAA,IACO;AAG3B,SAAIG,MAAc,WAGd0B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWnH;AAAA,QACT;AAAA,QACA;AAAA,QACAgD;AAAA,MAAA;AAAA,MAEF,OAAO;AAAA,QACL,aAAYsC,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAG8B,IAAA9B,KAAA,gBAAAA,EAAO,eAAP,gBAAA8B,EAAmB;AAAA,QACtB,GAAGnE;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA3B,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,OAAO;AAAA,cACL,UAAU;AAAA,cACV,YAAY;AAAA,cACZ,QAAO2B,KAAA,gBAAAA,EAAO,gBAAe;AAAA,cAC7B,kBAAiBA,KAAA,gBAAAA,EAAO,UAAS,SAAS,YAAY;AAAA,cACtD,SAAS;AAAA,cACT,cAAc;AAAA,cACd,aAAa;AAAA,YAAA;AAAA,YAEf,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;AAAA,YAE5E,YAAuBA,CAAc;AAAA,UAAA;AAAA,QAAA;AAAA,QAIxCyE,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,OAAO;AAAA,cACL,QAAO7B,KAAA,gBAAAA,EAAO,UAAS,SAAS,YAAY;AAAA,cAC5C,aAAa;AAAA,YAAA;AAAA,YAGd,UAAA;AAAA,cAAAgB,EAAQ;AAAA,cAAa;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA,QAIxB3C,gBAAAA,EAAAA;AAAAA,UAACf;AAAA,UAAA;AAAA,YACC,MAAMF,EAAe;AAAA,YACrB,YAAYA,EAAe;AAAA,YAC3B,WAAWA,EAAe;AAAA,YAC1B,cAAc;AAAA,cACZ,OAAOA,EAAe;AAAA,cACtB,YAAYA,EAAe;AAAA,cAC3B,WAAW;AAAA,YAAA;AAAA,YAGb,UAAAiB,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,OAAO;AAAA,kBACL,QAAO2B,KAAA,gBAAAA,EAAO,gBAAe;AAAA,kBAC7B,gBAAgB;AAAA,kBAChB,QAAQ;AAAA,kBACR,UAAU;AAAA,kBACV,YAAY;AAAA,gBAAA;AAAA,gBAGb,UAAAgB,EAAQ;AAAA,cAAA;AAAA,YAAA;AAAA,UACX;AAAA,QAAA;AAAA,QAIFa,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,OAAO;AAAA,cACL,UAAU;AAAA,cACV,QAAO7B,KAAA,gBAAAA,EAAO,UAAS,SAAS,YAAY;AAAA,cAC5C,YAAY;AAAA,YAAA;AAAA,YAEd,OAAOc;AAAA,YACR,UAAA;AAAA,cAAA;AAAA,cACGD;AAAA,cAAiB;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACrB;AAAA,IAAA;AAAA,EAAA,IAKFV,MAAc,cAAcA,MAAc,cAG1C9B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW3D;AAAA,QACT;AAAA,QACA0F,KAAclB,IACV,qKACA;AAAA,QACJxB;AAAA,MAAA;AAAA,MAEF,OAAO;AAAA,QACL,aAAYsC,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAG+B,IAAA/B,KAAA,gBAAAA,EAAO,eAAP,gBAAA+B,EAAmB;AAAA,QACtB,GAAGpE;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAEzB,UAAA,CAACI,KAAc,CAAClB;AAAA;AAAA,QAEf2C,gBAAAA,OAAAG,EAAAA,UAAA,EAEE,UAAA;AAAA,UAAA3D,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,OAAO;AAAA,gBACL,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,QAAO2B,KAAA,gBAAAA,EAAO,gBAAe;AAAA,gBAC7B,kBAAiBA,KAAA,gBAAAA,EAAO,UAAS,SAAS,YAAY;AAAA,gBACtD,SAAS;AAAA,gBACT,cAAc;AAAA,cAAA;AAAA,cAEhB,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;AAAA,cAE5E,YAAuBA,CAAc;AAAA,YAAA;AAAA,UAAA,GAE1C;AAAA,UAEAyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2CAEb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,kBACb,UAAAwD,gBAAAA,EAAAA,KAAC,KAAA,EAAE,WAAU,4DACV,UAAA;AAAA,cAAAb,EAAQ;AAAA,cAAa;AAAA,cACtB3C,gBAAAA,EAAAA;AAAAA,gBAACf;AAAA,gBAAA;AAAA,kBACC,MAAMF,EAAe;AAAA,kBACrB,YAAYA,EAAe;AAAA,kBAC3B,WAAWA,EAAe;AAAA,kBAC1B,cAAc;AAAA,oBACZ,OAAOA,EAAe;AAAA,oBACtB,YAAYA,EAAe;AAAA,oBAC3B,WAAW;AAAA,kBAAA;AAAA,kBAGb,UAAAiB,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,WAAU;AAAA,sBAET,UAAA2C,EAAQ;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBACX;AAAA,cAAA;AAAA,YACF,EAAA,CACF,EAAA,CACF;AAAA,YAGCZ,KACC/B,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yCACb,UAAAwD,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAS,MAAMxB,EAAc,EAAI;AAAA,gBACjC,WAAU;AAAA,gBACV,OAAM;AAAA,gBAEN,UAAA;AAAA,kBAAAhC,gBAAAA,EAAAA,IAAC,UAAK,UAAA,eAAA,CAAY;AAAA,kBAClBA,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6DAA4D,EAAA,CACrI;AAAA,gBAAA;AAAA,cAAA;AAAA,YAAA,EACF,CACF;AAAA,UAAA,EAAA,CAEF;AAAA,QAAA,EAAA,CAGF;AAAA;AAAA;AAAA,QAGAwD,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAU;AAAA,YACV,OAAOV;AAAA,YACP,qBAAmBnB,KAAA,gBAAAA,EAAO;AAAA,YAG1B,UAAA;AAAA,cAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0EACb,UAAA;AAAA,gBAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kEACZ,UAAA;AAAA,kBAAA3B,KAAcI,EAAO,SAAS,WAAW,KACxCjC,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,WAAU;AAAA,sBACV,OAAO;AAAA,wBACL,kBAAiB2B,KAAA,gBAAAA,EAAO,kBAAgBA,KAAA,gBAAAA,EAAO,gBAAe;AAAA,wBAC9D,eAAcA,KAAA,gBAAAA,EAAO,iBAAgB;AAAA,sBAAA;AAAA,sBAEvC,OAAOlB,GAAgB1B,CAA4B;AAAA,sBAElD,aAAa,WAAW;AAAA,oBAAA;AAAA,kBAAA;AAAA,kBAG7ByE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,mCACZ,UAAA;AAAA,oBAAAzE,EAAe,gBACdiB,gBAAAA,EAAAA;AAAAA,sBAAC;AAAA,sBAAA;AAAA,wBACC,KAAKjB,EAAe,aAAa;AAAA,wBACjC,KAAK,GAAGA,EAAe,KAAK;AAAA,wBAC5B,WAAU;AAAA,wBACV,SAAS,CAAC6E,MAAM;AAEb,0BAAAA,EAAE,OAA4B,MAAM,UAAU;AAAA,wBAAA;AAAA,sBACjD;AAAA,oBAAA;AAAA,oBAGJ5D,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,gFACX,YAAQ,MAAA,CACX;AAAA,kBAAA,EAAA,CACF;AAAA,gBAAA,GACF;AAAA,gBAEAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,4BACb,UAAA;AAAA,kBAAAA,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,SAAS,MAAMxB,EAAc,EAAK;AAAA,sBAClC,WAAU;AAAA,sBACV,OAAM;AAAA,sBAEN,UAAA;AAAA,wBAAAhC,gBAAAA,EAAAA,IAAC,UAAK,UAAA,eAAA,CAAY;AAAA,wBAClBA,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,YAAW,EAAA,CAClF;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBAAA;AAAA,kBAEFA,gBAAAA,EAAAA;AAAAA,oBAACf;AAAA,oBAAA;AAAA,sBACC,MAAMF,EAAe;AAAA,sBACrB,YAAYA,EAAe;AAAA,sBAC3B,WAAWA,EAAe;AAAA,sBAC1B,cAAc;AAAA,wBACZ,OAAOA,EAAe;AAAA,wBACtB,YAAYA,EAAe;AAAA,wBAC3B,WAAW;AAAA,sBAAA;AAAA,sBAGb,UAAAyE,gBAAAA,EAAAA,KAAC,UAAA,EAAO,WAAU,+OACf,UAAA;AAAA,wBAAA1B,MAAc,aAAa,QAAQ;AAAA,wBAAQ;AAAA,wBAAEa,EAAQ;AAAA,wBACtD3C,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gFAA+E,EAAA,CACtJ;AAAA,sBAAA,EAAA,CACF;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBACF,EAAA,CACF;AAAA,cAAA,GACF;AAAA,cAGAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAE,WAAU,4DACV,UAAA2C,EAAQ,YAAA,CACX,EAAA,CACF;AAAA,cAGA3C,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,WAAU;AAAA,kBACV,OAAOyC;AAAA,kBAEN,UAAAD;AAAA,gBAAA;AAAA,cAAA,GAEL;AAAA,cAGCZ,KAAkB,OAAO7C,EAAe,sBAAuB,YAC9DyE,gBAAAA,OAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,gBAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,8FACb,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,eAAc,UAAA,eAAW;AAAA,kBACzCwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,yGAAyG,UAAA;AAAA,oBAAAd;AAAA,oBAAqB;AAAA,kBAAA,EAAA,CAAO;AAAA,gBAAA,GACvJ;AAAA,gBACA1C,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yEACb,UAAAA,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,OAAO,EAAE,OAAO,GAAG0C,CAAoB,IAAA;AAAA,kBAAI;AAAA,gBAAA,EAC7C,CACF;AAAA,cAAA,GACF;AAAA,cAGFc,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,qCACZ,UAAA;AAAA,gBAAAzE,EAAe,WACdyE,gBAAAA,OAAC,QAAA,EAAK,WAAU,0NACd,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6IAA4I,EAAA,CACnN;AAAA,kBACCjB,EAAe;AAAA,gBAAA,GAClB;AAAA,gBAGDA,EAAe,cAAcA,EAAe,aAAa,KACxDyE,gBAAAA,OAAC,QAAA,EAAK,WAAU,8MACd,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6DAA4D,EAAA,CACnI;AAAA,kBACCjB,EAAe;AAAA,kBAAW;AAAA,gBAAA,EAAA,CAC7B;AAAA,cAAA,GAEJ;AAAA,cAGCA,EAAe,YAAYA,EAAe,SAAS,SAAS,KAC3DyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6DAA4D,UAAA,kBAE3E;AAAA,gBACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACZ,UAAA;AAAA,kBAAAzE,EAAe,SAAS,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC8E,GAASC,MACjDN,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBAEC,WAAU;AAAA,sBAEV,UAAA;AAAA,wBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,yCAAwC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC/F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,iDAAgD,EAAA,CACvH;AAAA,wBACC6D;AAAA,sBAAA;AAAA,oBAAA;AAAA,oBANIC;AAAA,kBAAA,CAQR;AAAA,kBACA/E,EAAe,SAAS,SAAS,KAChCyE,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,sDAAqD,UAAA;AAAA,oBAAA;AAAA,oBACjEzE,EAAe,SAAS,SAAS;AAAA,oBAAE;AAAA,kBAAA,EAAA,CACvC;AAAA,gBAAA,EAAA,CAEJ;AAAA,cAAA,GACF;AAAA,cAIDA,EAAe,gBAAgBA,EAAe,aAAa,SAAS,KACnEyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6DAA4D,UAAA,mBAE3E;AAAA,gBACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACZ,UAAA;AAAA,kBAAAzE,EAAe,aAAa,MAAM,GAAG,CAAC,EAAE,IAAI,CAACgF,GAAaD,MACzDN,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBAEC,WAAU;AAAA,sBAEV,UAAA;AAAA,wBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,yCAAwC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC/F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,0JAAyJ,EAAA,CAChO;AAAA,wBACC+D;AAAA,sBAAA;AAAA,oBAAA;AAAA,oBANID;AAAA,kBAAA,CAQR;AAAA,kBACA/E,EAAe,aAAa,SAAS,KACpCyE,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,sDAAqD,UAAA;AAAA,oBAAA;AAAA,oBACjEzE,EAAe,aAAa,SAAS;AAAA,oBAAE;AAAA,kBAAA,EAAA,CAC3C;AAAA,gBAAA,EAAA,CAEJ;AAAA,cAAA,GACF;AAAA,cAIFiB,gBAAAA,EAAAA,IAAC,SAAI,WAAU,iCACb,gCAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA,oBAAA,CAE3D,EAAA,CACF;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA;AAAA,IACF;AAAA,EAAA,IAQNA,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW6C;AAAA,MACX,OAAO;AAAA,QACL,aAAYlB,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAGqC,IAAArC,KAAA,gBAAAA,EAAO,eAAP,gBAAAqC,EAAmB;AAAA,QACtB,GAAG1E;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAE1B,UAAA6B,gBAAAA,EAAAA;AAAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,OAAOV;AAAA,UAGP,UAAA;AAAA,YAAA9C,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,OAAO;AAAA,kBACL,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,QAAO2B,KAAA,gBAAAA,EAAO,gBAAe;AAAA,kBAC7B,kBAAiBA,KAAA,gBAAAA,EAAO,UAAS,SAAS,YAAY;AAAA,kBACtD,SAAS;AAAA,kBACT,cAAc;AAAA,gBAAA;AAAA,gBAEhB,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;AAAA,gBAE5E,YAAuBA,CAAc;AAAA,cAAA;AAAA,YAAA,GAE1C;AAAA,YAGAyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0EACb,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0CACZ,UAAA;AAAA,gBAAAzE,EAAe,gBACdiB,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,KAAKjB,EAAe,aAAa;AAAA,oBACjC,KAAK,GAAGA,EAAe,KAAK;AAAA,oBAC5B,WAAU;AAAA,oBACV,SAAS,CAAC6E,MAAM;AAEb,sBAAAA,EAAE,OAA4B,MAAM,UAAU;AAAA,oBAAA;AAAA,kBACjD;AAAA,gBAAA;AAAA,gBAGJ5D,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,gFACX,YAAQ,MAAA,CACX;AAAA,cAAA,GACF;AAAA,cAEAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,4BACb,UAAAA,gBAAAA,EAAAA;AAAAA,gBAACf;AAAA,gBAAA;AAAA,kBACC,MAAMF,EAAe;AAAA,kBACrB,YAAYA,EAAe;AAAA,kBAC3B,WAAWA,EAAe;AAAA,kBAC1B,cAAc;AAAA,oBACZ,OAAOA,EAAe;AAAA,oBACtB,YAAYA,EAAe;AAAA,oBAC3B,WAAW;AAAA,kBAAA;AAAA,kBAGb,UAAAyE,gBAAAA,EAAAA,KAAC,UAAA,EAAO,WAAU,+OAA8O,UAAA;AAAA,oBAAA;AAAA,oBACvPb,EAAQ;AAAA,oBACf3C,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gFAA+E,EAAA,CACtJ;AAAA,kBAAA,EAAA,CACF;AAAA,gBAAA;AAAA,cAAA,EACF,CACF;AAAA,YAAA,GACF;AAAA,YAGAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAE,WAAU,4DACV,UAAA2C,EAAQ,YAAA,CACX,EAAA,CACF;AAAA,YAGCf,KAAkB,OAAO7C,EAAe,sBAAuB,YAC9DyE,gBAAAA,OAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,8FACb,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,eAAc,UAAA,eAAW;AAAA,gBACzCwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,yGAAyG,UAAA;AAAA,kBAAAd;AAAA,kBAAqB;AAAA,gBAAA,EAAA,CAAO;AAAA,cAAA,GACvJ;AAAA,cACA1C,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yEACb,UAAAA,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,WAAU;AAAA,kBACV,OAAO,EAAE,OAAO,GAAG0C,CAAoB,IAAA;AAAA,gBAAI;AAAA,cAAA,EAC7C,CACF;AAAA,YAAA,GACF;AAAA,YAGFc,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,qCACZ,UAAA;AAAA,cAAAzE,EAAe,WACdyE,gBAAAA,OAAC,QAAA,EAAK,WAAU,0NACd,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6IAA4I,EAAA,CACnN;AAAA,gBACCjB,EAAe;AAAA,cAAA,GAClB;AAAA,cAGDA,EAAe,cAAcA,EAAe,aAAa,KACxDyE,gBAAAA,OAAC,QAAA,EAAK,WAAU,8MACd,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6DAA4D,EAAA,CACnI;AAAA,gBACCjB,EAAe;AAAA,gBAAW;AAAA,cAAA,EAAA,CAC7B;AAAA,YAAA,GAEJ;AAAA,YAGCA,EAAe,YAAYA,EAAe,SAAS,SAAS,KAC3DyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6DAA4D,UAAA,kBAE3E;AAAA,cACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACZ,UAAA;AAAA,gBAAAzE,EAAe,SAAS,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC8E,GAASC,MACjDN,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBAEC,WAAU;AAAA,oBAEV,UAAA;AAAA,sBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,yCAAwC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC/F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,iDAAgD,EAAA,CACvH;AAAA,sBACC6D;AAAA,oBAAA;AAAA,kBAAA;AAAA,kBANIC;AAAA,gBAAA,CAQR;AAAA,gBACA/E,EAAe,SAAS,SAAS,KAChCyE,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,sDAAqD,UAAA;AAAA,kBAAA;AAAA,kBACjEzE,EAAe,SAAS,SAAS;AAAA,kBAAE;AAAA,gBAAA,EAAA,CACvC;AAAA,cAAA,EAAA,CAEJ;AAAA,YAAA,GACF;AAAA,YAIDA,EAAe,gBAAgBA,EAAe,aAAa,SAAS,KACnEyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6DAA4D,UAAA,mBAE3E;AAAA,cACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACZ,UAAA;AAAA,gBAAAzE,EAAe,aAAa,MAAM,GAAG,CAAC,EAAE,IAAI,CAACgF,GAAaD,MACzDN,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBAEC,WAAU;AAAA,oBAEV,UAAA;AAAA,sBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,yCAAwC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC/F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,0JAAyJ,EAAA,CAChO;AAAA,sBACC+D;AAAA,oBAAA;AAAA,kBAAA;AAAA,kBANID;AAAA,gBAAA,CAQR;AAAA,gBACA/E,EAAe,aAAa,SAAS,KACpCyE,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,sDAAqD,UAAA;AAAA,kBAAA;AAAA,kBACjEzE,EAAe,aAAa,SAAS;AAAA,kBAAE;AAAA,gBAAA,EAAA,CAC3C;AAAA,cAAA,EAAA,CAEJ;AAAA,YAAA,GACF;AAAA,kCAYD,OAAA,EAAI,WAAU,+DACb,UAAAyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,8EACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,QAAA,EAAK,OAAOyC,GACV,UAAAD,GACH;AAAA,cACAxC,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,oCAAmC,UAAA,oBAAA,CAEnD;AAAA,YAAA,EAAA,CACF,EAAA,CACF;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EAAA;AAGN;AAEA0B,GAAkB,cAAc;ACzqBzB,MAAMuC,KAAwD,CAAC;AAAA,EACpE,iBAAA1C;AAAA,EACA,OAAAI;AAAA,EACA,aAAAuC,IAAc;AAAA,EACd,iBAAAC,IAAkB;AAAA,EAClB,cAAAC,IAAe;AAAA,EACf,WAAA/E;AAAA,EACA,OAAAC;AACF,MAAM;;AAEJ,EAAAa,GAAA;AAGA,QAAMkE,IAAoB9G,GAAQ,MACzBgE,EAAgB,MAAM,GAAG2C,CAAW,GAC1C,CAAC3C,GAAiB2C,CAAW,CAAC,GAI3BI,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SAAI0C,EAAkB,WAAW,IAE7BrE,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWsE;AAAA,MACX,OAAO;AAAA,QACL,GAAGC;AAAA,QACH,aAAY5C,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAGS,IAAAT,KAAA,gBAAAA,EAAO,eAAP,gBAAAS,EAAmB;AAAA,QACtB,GAAG9C;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAE1B,gCAAC,OAAA,EAAI,WAAU,oDACb,UAAA3B,gBAAAA,EAAAA,IAAC,KAAA,EAAE,oCAAsB,EAAA,CAC3B;AAAA,IAAA;AAAA,EAAA,IAMJA,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWsE;AAAA,MACX,OAAO;AAAA,QACL,GAAGC;AAAA,QACH,aAAY5C,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAGoB,IAAApB,KAAA,gBAAAA,EAAO,eAAP,gBAAAoB,EAAmB;AAAA,QACtB,GAAGzD;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAE1B,UAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,aAEb,UAAA;AAAA,QAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,eACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,+CACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wMAAuM,EAAA,CAC9Q;AAAA,YACAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,0DAAyD,UAAA,mBAAA,CAEvE;AAAA,UAAA,GACF;AAAA,UACAwD,gBAAAA,EAAAA,KAAC,KAAA,EAAE,WAAU,4CACV,UAAA;AAAA,YAAAa,EAAkB;AAAA,YAAO;AAAA,UAAA,EAAA,CAC5B;AAAA,QAAA,GACF;AAAA,QAGArE,gBAAAA,EAAAA,IAAC,SAAI,WAAU,wDACZ,YAAkB,IAAI,CAACwE,GAASC,MAC/BjB,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YAEC,WAAU;AAAA,YAGV,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,yCACb,UAAA;AAAA,gBAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACZ,UAAA;AAAA,kBAAAiB,MAAU,KACTzE,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,sEAAqE,UAAA,aAErF;AAAA,kBAEFwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA;AAAA,oBAAA;AAAA,oBACvDiB,IAAQ;AAAA,kBAAA,EAAA,CACZ;AAAA,gBAAA,GACF;AAAA,gBACCN,KACCX,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,8DACZ,UAAA;AAAA,kBAAA,KAAK,MAAMgB,EAAQ,qBAAqB,GAAG;AAAA,kBAAE;AAAA,gBAAA,EAAA,CAChD;AAAA,cAAA,GAEJ;AAAA,cAGAxE,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,uDACX,YAAQ,OACX;AAAA,cAGCmE,KACCX,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,gBAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,mFACb,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,UAAK,UAAA,cAAA,CAAW;AAAA,kBACjBwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,qBAAqB,UAAA;AAAA,oBAAA,KAAK,MAAMgB,EAAQ,qBAAqB,GAAG;AAAA,oBAAE;AAAA,kBAAA,EAAA,CAAO;AAAA,gBAAA,GAC3F;AAAA,gBACAxE,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sEACb,UAAAA,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,OAAO,EAAE,OAAO,GAAG,KAAK,MAAMwE,EAAQ,qBAAqB,GAAG,CAAC,IAAA;AAAA,kBAAI;AAAA,gBAAA,EACrE,CACF;AAAA,cAAA,GACF;AAAA,cAIFhB,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,qCACZ,UAAA;AAAA,gBAAAgB,EAAQ,WACPhB,gBAAAA,OAAC,QAAA,EAAK,WAAU,sDACd,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6IAA4I,EAAA,CACnN;AAAA,kBACCwE,EAAQ;AAAA,gBAAA,GACX;AAAA,gBAKDA,EAAQ,cAAcA,EAAQ,aAAa,KAC1ChB,gBAAAA,OAAC,QAAA,EAAK,WAAU,sDACd,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,gBAAe,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACtE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6DAA4D,EAAA,CACnI;AAAA,kBACCwE,EAAQ;AAAA,kBAAW;AAAA,gBAAA,EAAA,CACtB;AAAA,cAAA,GAEJ;AAAA,cAGCJ,KAAgBI,EAAQ,YAAYA,EAAQ,SAAS,SAAS,KAC7DhB,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iDAAgD,UAAA,iBAE/D;AAAA,gBACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACZ,UAAA;AAAA,kBAAAgB,EAAQ,SAAS,MAAM,GAAG,CAAC,EAAE,IAAI,CAACX,GAASC,MAC1CN,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBAEC,WAAU;AAAA,sBAEV,UAAA;AAAA,wBAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,uCAAsC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC7F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,iDAAgD,EAAA,CACvH;AAAA,wBACC6D;AAAA,sBAAA;AAAA,oBAAA;AAAA,oBANIC;AAAA,kBAAA,CAQR;AAAA,mBACCU,EAAQ,SAAS,UAAU,KAAK,KAChChB,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,mDAAkD,UAAA;AAAA,oBAAA;AAAA,oBAC9DgB,EAAQ,SAAS,SAAS;AAAA,oBAAE;AAAA,kBAAA,EAAA,CAChC;AAAA,gBAAA,EAAA,CAEJ;AAAA,cAAA,GACF;AAAA,cAIFxE,gBAAAA,EAAAA;AAAAA,gBAACf;AAAA,gBAAA;AAAA,kBACC,MAAMuF,EAAQ;AAAA,kBACd,YAAYA,EAAQ;AAAA,kBACpB,WAAWA,EAAQ;AAAA,kBACnB,cAAc;AAAA,oBACZ,OAAOA,EAAQ;AAAA,oBACf,YAAYA,EAAQ;AAAA,oBACpB,WAAW;AAAA,kBAAA;AAAA,kBAGb,UAAAhB,gBAAAA,EAAAA,KAAC,UAAA,EAAO,WAAU,8IAA6I,UAAA;AAAA,oBAAA;AAAA,oBAE7JxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gFAA+E,EAAA,CACtJ;AAAA,kBAAA,EAAA,CACF;AAAA,gBAAA;AAAA,cAAA;AAAA,YACF;AAAA,UAAA;AAAA,UA9GKwE,EAAQ,cAAcC;AAAA,QAAA,CAgH9B,GACH;AAAA,8BAGC,OAAA,EAAI,WAAU,kGACb,UAAAjB,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,sEACd,UAAA;AAAA,UAAAxD,gBAAAA,MAAC,OAAA,EAAI,WAAU,2BAA0B,MAAK,gBAAe,SAAQ,aACnE,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,UAAS,WAAU,GAAE,oHAAmH,UAAS,WAAU,GACnK;AAAA,UACAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,eAAc,UAAA,cAAU;AAAA,UACxCA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,sDAAqD,UAAA,SAAA,CAAM;AAAA,QAAA,EAAA,CAC7E,EAAA,CACF;AAAA,MAAA,EAAA,CACF;AAAA,IAAA;AAAA,EAAA;AAGN;AAEAiE,GAAmB,cAAc;AClNjC,MAAMS,KAA+C;AAAA,EACnD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAW;AAAA,EACX,KAAO;AAAA,EACP,mBAAmB;AACrB,GAGMC,KAAqD;AAAA,EACzD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAW;AAAA,EACX,KAAO;AAAA,EACP,mBAAmB;AACrB,GAEaC,KAA0C,CAAC;AAAA,EACtD,MAAA5M;AAAA,EACA,SAAA6M;AAAA,EACA,MAAAC,IAAO;AAAA,EACP,WAAAzF;AAAA,EACA,OAAAC;AACF,MAAM;AACJ,QAAMyF,IAAmBF,KAAWH,GAAkB1M,CAAI,KAAK,aACzDgN,IAAOL,GAAe3M,CAAI,GAE1BiN,IAAe5I;AAAA,IACnB;AAAA,IACA;AAAA,IACA,iBAAiB0I,CAAgB;AAAA,IACjC,iBAAiBD,CAAI;AAAA,IACrBzF;AAAA,EAAA;AAGF,SACEmE,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWyB;AAAA,MACX,OAAA3F;AAAA,MAEC,UAAA;AAAA,QAAA0F,KAAQhF,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,sBAAsB,UAAAgF,GAAK;AAAA,QACpDhF,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,sBAAsB,UAAAhI,EAAA,CAAK;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGjD;AAEA4M,GAAY,cAAc;ACVnB,MAAMM,KAA4D,CAAC;AAAA,EACxE,gBAAAnG;AAAA,EACA,OAAA4C,IAAQ,EAAE,MAAM,QAAA;AAAA,EAChB,WAAAtC,IAAY;AAAA,EACZ,OAAAC;AAAA,EACA,eAAA6F,IAAgB;AAAA,EAChB,iBAAAC,IAAkB;AAAA,EAClB,UAAAC;AAAA,EACA,SAAAC;AAAA,EACA,aAAAC,IAAc;AAChB,MAAM;;AAEJ,EAAApF,GAAA;AAEA,QAAM,CAACU,GAAYmB,CAAa,IAAI7E,EAASiI,CAAe,GAItDI,IAAqB,MAAM;AAC/B,IAAID,KACFvD,EAAc,CAACnB,CAAU;AAAA,EAC3B,GAII4E,IAAqB1G,EAAe,oBAAoB,CAAA,GAExD2G,IAAkB;AAAA,IACtB;AAAA,MACE,OAAO;AAAA,MACP,aAAa,oBAAoB3G,EAAe,wBAAwBA,EAAe,KAAK;AAAA,MAC5F,MAAM;AAAA,IAAA;AAAA,IAER;AAAA,MACE,OAAO;AAAA,MACP,aAAa,6BAA6BA,EAAe,wBAAwBA,EAAe,KAAK;AAAA,MACrG,MAAM;AAAA,IAAA;AAAA,IAER;AAAA,MACE,OAAO,GAAGA,EAAe,wBAAwBA,EAAe,KAAK;AAAA,MACrE,aAAaA,EAAe,8BAA8BA,EAAe,eAAe,GAAGA,EAAe,wBAAwBA,EAAe,KAAK;AAAA,MACtJ,MAAM;AAAA,IAAA;AAAA,IAER;AAAA,MACE,OAAO;AAAA,MACP,aAAa,iCAAiCA,EAAe,wBAAwBA,EAAe,KAAK;AAAA,MACzG,MAAM;AAAA,IAAA;AAAA,EACR,GAIIyD,IAAmB1B,GAAoB/B,GAAgB,EAAK,GAC5D0D,IAAgBzB,GAAA,GAGhB2E,IAAkBN,MAAaI,EAAmB,SAAS,IAAIA,IAAqBC,IACpFE,IAAiBN,KAAW,OAAOvG,EAAe,wBAAwBA,EAAe,KAAK,IAG9F8G,IAAS;AAAA,IACb,YAAYlE,EAAM,oBAAoBA,EAAM,SAAS,SAAS,YAAY;AAAA,IAC1E,SAASA,EAAM,iBAAiBA,EAAM,SAAS,SAAS,YAAY;AAAA,IACpE,QAAQA,EAAM,gBAAgBA,EAAM,SAAS,SAAS,YAAY;AAAA,IAClE,MAAMA,EAAM,cAAcA,EAAM,SAAS,SAAS,YAAY;AAAA,IAC9D,eAAeA,EAAM,uBAAuBA,EAAM,SAAS,SAAS,YAAY;AAAA,IAChF,QAAQA,EAAM,eAAeA,EAAM,gBAAgB;AAAA,IACnD,WAAWA,EAAM,kBAAkB;AAAA;AAAA,IAEnC,YAAUS,IAAAT,EAAM,cAAN,gBAAAS,EAAiB,aAAYT,EAAM,SAAS,SAAS,YAAY;AAAA,IAC3E,aAAWoB,IAAApB,EAAM,cAAN,gBAAAoB,EAAiB,eAAcpB,EAAM,SAAS,SAAS,YAAY;AAAA,EAAA,GAI1EmE,IAAenE,EAAM,uBAAuB,KAAK;AAAA,IACrD,YAAYA,EAAM,cAAc;AAAA,IAChC,cAAcA,EAAM,gBAAgB;AAAA,IACpC,QAAQ,aAAakE,EAAO,MAAM;AAAA,IAClC,YAAYA,EAAO;AAAA,IACnB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAW7C,IAAArB,EAAM,YAAN,gBAAAqB,EAAe,YAAWrB,EAAM,SAAS,SAChD,yEACA;AAAA,IACJ,UAAU;AAAA,IACV,YAAY;AAAA,EAAA;AAGd,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW,2CAA2CnE,CAAS;AAAA,MAC/D,OAAO;AAAA,QACL,GAAGyG;AAAA,QACH,IAAG7C,IAAAtB,EAAM,eAAN,gBAAAsB,EAAkB;AAAA,QACrB,GAAG3D;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,EAAM;AAAA,MAGzB,UAAA;AAAA,QAAA3B,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,OAAO;AAAA,cACL,YAAY6F,EAAO;AAAA,cACnB,SAAS;AAAA,cACT,cAAchF,KAAc,CAAC0E,IAAc,aAAaM,EAAO,MAAM,KAAK;AAAA,cAC1E,UAAU;AAAA,cACV,YAAY;AAAA,YAAA;AAAA,YAGd,UAAArC,gBAAAA,EAAAA,KAAC,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,gBAAgB,iBAAiB,KAAK,OAAA,GACzF,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,QAAQ,MAAM,GAAG,UAAU,KACnF,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,OAAO;AAAA,sBACL,OAAO;AAAA,sBACP,QAAQ;AAAA,sBACR,cAAc2B,EAAM,gBAAgB;AAAA,sBACpC,YAAYkE,EAAO;AAAA,sBACnB,SAAS;AAAA,sBACT,YAAY;AAAA,sBACZ,gBAAgB;AAAA,sBAChB,OAAO;AAAA,sBACP,YAAU3C,IAAAvB,EAAM,aAAN,gBAAAuB,EAAgB,SAAQ;AAAA,sBAClC,YAAY;AAAA,sBACZ,aAAWC,IAAAxB,EAAM,YAAN,gBAAAwB,EAAe,UAAS;AAAA,sBACnC,QAAQ,aAAa0C,EAAO,MAAM;AAAA,oBAAA;AAAA,oBAGlC,aAAe,wBAAwB9G,EAAe,OAAO,OAAO,CAAC,EAAE,YAAA;AAAA,kBAAY;AAAA,gBAAA;AAAA,gBAEvFyE,gBAAAA,OAAC,SAAI,OAAO,EAAE,MAAM,GAAG,UAAU,KAC/B,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,OAAO;AAAA,wBACL,QAAQ;AAAA,wBACR,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO6F,EAAO;AAAA,wBACd,YAAY;AAAA,wBACZ,UAAU;AAAA,wBACV,cAAc;AAAA,wBACd,YAAY;AAAA,sBAAA;AAAA,sBAGb,UAAA9G,EAAe,wBAAwBA,EAAe;AAAA,oBAAA;AAAA,kBAAA;AAAA,kBAEzDyE,gBAAAA,EAAAA;AAAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,OAAO;AAAA,wBACL,QAAQ;AAAA,wBACR,UAAU;AAAA,wBACV,OAAOqC,EAAO;AAAA,wBACd,YAAY;AAAA,wBACZ,UAAU;AAAA,wBACV,cAAc;AAAA,wBACd,YAAY;AAAA,sBAAA;AAAA,sBAEd,OAAOpD;AAAA,sBAEN,UAAA;AAAA,wBAAAD;AAAA,wBAAiB;AAAA,wBAAI,IAAI,IAAIzD,EAAe,OAAOA,EAAe,WAAW,EAAE;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBAClF,EAAA,CACF;AAAA,cAAA,GACF;AAAA,cAEAyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,OAAA,GAEvD,UAAA;AAAA,gBAAA,CAAC3C,KAAc0E,KACdvF,gBAAAA,EAAAA;AAAAA,kBAACf;AAAA,kBAAA;AAAA,oBACC,MAAMF,EAAe;AAAA,oBACrB,YAAYA,EAAe;AAAA,oBAC3B,WAAWA,EAAe;AAAA,oBAC1B,cAAc;AAAA,sBACZ,OAAOA,EAAe,wBAAwBA,EAAe;AAAA,sBAC7D,WAAW;AAAA,sBACX,UAAU;AAAA,sBACV,UAAU;AAAA,oBAAA;AAAA,oBAGZ,UAAAiB,gBAAAA,EAAAA;AAAAA,sBAAC;AAAA,sBAAA;AAAA,wBACC,OAAO;AAAA,0BACL,UAASoD,IAAAzB,EAAM,YAAN,QAAAyB,EAAe,QAAQ,GAAGzB,EAAM,QAAQ,KAAK,IAAIA,EAAM,QAAQ,UAAU,MAAM,KAAK;AAAA,0BAC7F,iBAAiBkE,EAAO;AAAA,0BACxB,OAAO;AAAA,0BACP,QAAQ;AAAA,0BACR,cAAclE,EAAM,gBAAgB;AAAA,0BACpC,YAAU0B,IAAA1B,EAAM,aAAN,gBAAA0B,EAAgB,UAAS;AAAA,0BACnC,YAAY;AAAA,0BACZ,QAAQ;AAAA,0BACR,YAAY;AAAA,0BACZ,aAAWC,IAAA3B,EAAM,YAAN,gBAAA2B,EAAe,UAAS;AAAA,0BACnC,YAAY;AAAA,0BACZ,IAAGC,IAAA5B,EAAM,eAAN,gBAAA4B,EAAkB;AAAA,wBAAA;AAAA,wBAEvB,aAAa,CAACK,MAAM;;AAClB,0BAAKjC,EAAM,yBACTiC,EAAE,cAAc,MAAM,YAAY,oBAClCA,EAAE,cAAc,MAAM,cAAYxB,IAAAT,EAAM,YAAN,gBAAAS,EAAe,WAAU;AAAA,wBAC7D;AAAA,wBAEF,YAAY,CAACwB,MAAM;;AACjB,0BAAKjC,EAAM,yBACTiC,EAAE,cAAc,MAAM,YAAY,iBAClCA,EAAE,cAAc,MAAM,cAAYxB,IAAAT,EAAM,YAAN,gBAAAS,EAAe,UAAS;AAAA,wBAC5D;AAAA,wBAGD,UAAAwD;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBACH;AAAA,gBAAA;AAAA,gBAKHL,KACC/B,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,SAASgC;AAAA,oBACT,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,YAAY;AAAA,sBACZ,KAAK;AAAA,sBACL,SAAS;AAAA,sBACT,YAAY7D,EAAM,SAAS,SAAS,YAAY;AAAA,sBAChD,QAAQ,aAAaA,EAAM,SAAS,SAAS,YAAY,SAAS;AAAA,sBAClE,cAAc;AAAA,sBACd,QAAQ;AAAA,sBACR,OAAOA,EAAM,eAAe;AAAA,sBAC5B,UAAU;AAAA,sBACV,YAAY;AAAA,sBACZ,YAAY;AAAA,oBAAA;AAAA,oBAEd,cAAc,CAACiC,MAAM;AACnB,sBAAAA,EAAE,cAAc,MAAM,aAAajC,EAAM,SAAS,SAAS,YAAY,WACvEiC,EAAE,cAAc,MAAM,cAAcjC,EAAM,eAAe;AAAA,oBAAA;AAAA,oBAE3D,cAAc,CAACiC,MAAM;AACnB,sBAAAA,EAAE,cAAc,MAAM,aAAajC,EAAM,SAAS,SAAS,YAAY,WACvEiC,EAAE,cAAc,MAAM,cAAcjC,EAAM,SAAS,SAAS,YAAY;AAAA,oBAAA;AAAA,oBAE1E,cAAYd,IAAa,sBAAsB;AAAA,oBAE/C,UAAA;AAAA,sBAAAb,gBAAAA,EAAAA,IAAC,QAAA,EAAM,UAAAa,IAAa,iBAAiB,gBAAe;AAAA,sBACpDb,gBAAAA,EAAAA;AAAAA,wBAAC;AAAA,wBAAA;AAAA,0BACC,OAAM;AAAA,0BACN,QAAO;AAAA,0BACP,SAAQ;AAAA,0BACR,MAAK;AAAA,0BACL,QAAO;AAAA,0BACP,aAAY;AAAA,0BACZ,eAAc;AAAA,0BACd,gBAAe;AAAA,0BAEd,UAAAa;AAAA;AAAA,4BAECb,gBAAAA,EAAAA,IAAC,QAAA,EAAK,GAAE,WAAA,CAAW;AAAA;AAAA;AAAA,4BAGnBwD,gBAAAA,OAAAG,EAAAA,UAAA,EACE,UAAA;AAAA,8BAAA3D,gBAAAA,MAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,8BAC/BA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,GAAE,YAAA,CAAY;AAAA,8BACpBA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,GAAE,YAAA,CAAY;AAAA,4BAAA,EAAA,CACtB;AAAA;AAAA,wBAAA;AAAA,sBAAA;AAAA,oBAEJ;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACF,EAAA,CAEJ;AAAA,YAAA,EAAA,CACF;AAAA,UAAA;AAAA,QAAA;AAAA,SAIAa,KAAc,CAAC0E,MACf/B,gBAAAA,EAAAA,KAAC,SAAI,OAAO,EAAE,SAAS,IAAA,GAEpB,UAAA;AAAA,UAAAmC,EAAgB,IAAI,CAACI,GAAStB,MAC7BjB,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cAEC,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,iBAAiBiB,IAAQ,MAAM,IAAIoB,EAAO,aAAaA,EAAO;AAAA,gBAC9D,cAAcpB,IAAQkB,EAAgB,SAAS,IAAI,aAAaE,EAAO,MAAM,KAAK;AAAA,cAAA;AAAA,cAGpF,UAAA;AAAA,gBAAArC,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,OAAO;AAAA,sBACL,QAAQ;AAAA,sBACR,UAAU;AAAA,sBACV,YAAY;AAAA,sBACZ,OAAOqC,EAAO;AAAA,sBACd,SAAS;AAAA,sBACT,YAAY;AAAA,sBACZ,KAAK;AAAA,oBAAA;AAAA,oBAGN,UAAA;AAAA,sBAAAE,EAAQ,QAAQ/F,gBAAAA,MAAC,QAAA,EAAM,UAAA+F,EAAQ,MAAK;AAAA,sBACpCA,EAAQ;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBAAA;AAAA,gBAEX/F,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,OAAO;AAAA,sBACL,QAAQ;AAAA,sBACR,UAAU;AAAA,sBACV,OAAO6F,EAAO;AAAA,sBACd,YAAY;AAAA,oBAAA;AAAA,oBAGb,UAAAE,EAAQ;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACX;AAAA,YAAA;AAAA,YA9BKtB;AAAA,UAAA,CAgCR;AAAA,WAGC5D,KAAc,CAAC0E,MACfvF,gBAAAA,EAAAA,IAAC,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,WAAW,aAAa6F,EAAO,MAAM,IAAI,iBAAiBA,EAAO,cAC9F,UAAA7F,gBAAAA,EAAAA;AAAAA,YAACf;AAAA,YAAA;AAAA,cACC,MAAMF,EAAe;AAAA,cACrB,YAAYA,EAAe;AAAA,cAC3B,WAAWA,EAAe;AAAA,cAC1B,cAAc;AAAA,gBACZ,OAAOA,EAAe;AAAA,gBACtB,WAAW;AAAA,gBACX,UAAU8B;AAAA,gBACV,UAAU;AAAA,cAAA;AAAA,cAGZ,UAAAb,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,OAAO;AAAA,oBACL,OAAO;AAAA,oBACP,SAAS;AAAA,oBACT,YAAY6F,EAAO;AAAA,oBACnB,OAAO;AAAA,oBACP,QAAQ;AAAA,oBACR,cAAc;AAAA,oBACd,UAAU;AAAA,oBACV,YAAY;AAAA,oBACZ,QAAQ;AAAA,oBACR,YAAY;AAAA,oBACZ,WAAW;AAAA,oBACX,UAAU;AAAA,oBACV,UAAU;AAAA,kBAAA;AAAA,kBAEZ,aAAa,CAACjC,MAAM;AAClB,oBAAAA,EAAE,cAAc,MAAM,YAAY,gCAClCA,EAAE,cAAc,MAAM,YAAY;AAAA,kBAAA;AAAA,kBAEpC,YAAY,CAACA,MAAM;AACjB,oBAAAA,EAAE,cAAc,MAAM,YAAY,0BAClCA,EAAE,cAAc,MAAM,YAAY;AAAA,kBAAA;AAAA,kBAGnC,UAAAgC;AAAA,gBAAA;AAAA,cAAA;AAAA,YACH;AAAA,UAAA,GAEJ;AAAA,UAIDT,KACCnF,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,WAAW,aAAa6F,EAAO,MAAM;AAAA,gBACrC,iBAAiBA,EAAO;AAAA,cAAA;AAAA,cAG1B,UAAArC,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,OAAO;AAAA,oBACL,UAAU;AAAA,oBACV,OAAOqC,EAAO;AAAA,oBACd,WAAW;AAAA,kBAAA;AAAA,kBAEd,UAAA;AAAA,oBAAA;AAAA,oBACY7F,gBAAAA,MAAC,YAAO,OAAO,EAAE,OAAO6F,EAAO,KAAA,GAAQ,UAAA,SAAA,CAAM;AAAA,kBAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAC1D;AAAA,UAAA;AAAA,QACF,EAAA,CAEJ;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GCtZaG,IAAwE,CAAC;AAAA,EACpF,gBAAAjH;AAAA,EACA,OAAA4C;AAAA,EACA,SAAAZ,IAAU;AAAA,EACV,YAAAkF,IAAa;AAAA,EACb,WAAA5G;AAAA,EACA,OAAAC;AACF,MAAM;;AACJ,QAAMoD,IAAuB,KAAK,MAAM3D,EAAe,qBAAqB,GAAG,GAGzEyD,IAAmB1B,GAAoB/B,GAAgBgC,CAAO,GAC9D0B,IAAgBzB,GAAA,GAEhBsD,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,MACE,gJAAgJ,CAAC0E;AAAA,MACjJ,oEAAoEA;AAAA,IAAA;AAAA,IAEtE1B;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE3B,gBAAAA,EAAAA;AAAAA,IAACf;AAAA,IAAA;AAAA,MACC,MAAMF,EAAe;AAAA,MACrB,YAAYA,EAAe;AAAA,MAC3B,WAAWA,EAAe;AAAA,MAC1B,cAAc;AAAA,QACZ,OAAOA,EAAe;AAAA,QACtB,YAAYA,EAAe;AAAA,MAAA;AAAA,MAE7B,WAAWuF;AAAA,MACX,OAAO;AAAA,QACL,aAAY3C,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAGS,IAAAT,KAAA,gBAAAA,EAAO,eAAP,gBAAAS,EAAmB;AAAA,QACtB,GAAG9C;AAAA,MAAA;AAAA,MAGL,UAAAkE,gBAAAA,EAAAA;AAAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,OAAOe;AAAA,UACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,UAG1B,UAAA;AAAA,YAAA3B,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wBACZ,UAAAjB,EAAe,gBAAgBA,EAAe,aAAa,SAAS,IACnEiB,gBAAAA,MAAC,OAAA,EAAI,WAAU,oFACb,UAAAA,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,KAAKjB,EAAe,aAAa,CAAC,EAAE;AAAA,gBACpC,KAAKA,EAAe,wBAAwBA,EAAe;AAAA,gBAC3D,WAAU;AAAA,cAAA;AAAA,YAAA,GAEd,IACEA,EAAe,eACjBiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oFACb,UAAAA,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,KAAKjB,EAAe,aAAa;AAAA,gBACjC,KAAKA,EAAe,wBAAwBA,EAAe;AAAA,gBAC3D,WAAU;AAAA,cAAA;AAAA,YAAA,EACZ,CACF,IACEA,EAAe,sBAAsB,MACvCiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oCAAA,CAAoC,IAEnDA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oCAAmC,GAEtD;AAAA,YAGAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,oEACb,UAAA;AAAA,gBAAAxD,gBAAAA,MAAC,QAAG,WAAW3D;AAAA,kBACb;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA0E,IAAU,yBAAyB;AAAA,gBAAA,GAElC,UAAAhC,EAAe,wBAAwBA,EAAe,MAAA,CACzD;AAAA,gBAGCA,EAAe,sBAAsB,OACpCyE,gBAAAA,EAAAA,KAAC,UAAK,WAAWnH;AAAA,kBACf;AAAA,kBACA0C,EAAe,sBAAsB,MACjC,yEACA;AAAA,gBAAA,GAEH,UAAA;AAAA,kBAAA2D;AAAA,kBAAqB;AAAA,gBAAA,EAAA,CACxB;AAAA,cAAA,GAEJ;AAAA,cAGCuD,MAAelH,EAAe,8BAA8BA,EAAe,WAC1EiB,gBAAAA,MAAC,OAAE,WAAW3D;AAAA,gBACZ;AAAA,gBACA0E,IAAU,YAAY;AAAA,cAAA,GAErB,UAAAhC,EAAe,8BAA8BA,EAAe,OAAA,CAC/D;AAAA,cAIFiB,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,WAAW3D;AAAA,oBACT;AAAA,oBACU;AAAA,kBAAY;AAAA,kBAExB,OAAOoG;AAAA,kBAEN,UAAAD;AAAA,gBAAA;AAAA,cAAA;AAAA,cAIF,CAACzB,KAAWhC,EAAe,YAAYA,EAAe,SAAS,SAAS,KACvEyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,6BACZ,UAAA;AAAA,gBAAAzE,EAAe,SAAS,MAAM,GAAG,CAAC,EAAE,IAAI,CAACsD,GAASoC,MACjDzE,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBAEC,WAAU;AAAA,oBAET,UAAAqC;AAAA,kBAAA;AAAA,kBAHIoC;AAAA,gBAAA,CAKR;AAAA,gBACA1F,EAAe,SAAS,SAAS,KAChCyE,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA;AAAA,kBAAA;AAAA,kBACvDzE,EAAe,SAAS,SAAS;AAAA,kBAAE;AAAA,gBAAA,EAAA,CACvC;AAAA,cAAA,GAEJ;AAAA,cAID,CAACgC,KAAWhC,EAAe,cAAcA,EAAe,aAAa,KACpEiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,gCACb,UAAAwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,qIACb,UAAA;AAAA,gBAAAzE,EAAe;AAAA,gBAAW;AAAA,cAAA,EAAA,CAC7B,EAAA,CACF;AAAA,YAAA,GAEJ;AAAA,YAGAiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sEACb,UAAAA,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,SAAQ;AAAA,gBAER,UAAAA,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,eAAc;AAAA,oBACd,gBAAe;AAAA,oBACf,aAAa;AAAA,oBACb,GAAE;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACJ;AAAA,YAAA,EACF,CACF;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EAAA;AAGN,GC5KakG,KAAsE,CAAC;AAAA,EAClF,iBAAA3E;AAAA,EACA,qBAAA4E;AAAA,EACA,OAAAxE;AAAA,EACA,wBAAAyE,IAAyB;AAAA,EACzB,uBAAAC;AAAA,EACA,wBAAAC;AAAA,EACA,WAAAjH;AACF,MAAM;AACJ,QAAMkH,IAAqBhF,EACxB,KAAK,CAACiF,GAAGC,MAAMA,EAAE,qBAAqBD,EAAE,kBAAkB,EAC1D,MAAM,GAAGJ,CAAsB,GAE5B9B,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iBACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,gGACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oDAAmD,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC1G,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gDAAA,CAAgD,EAAA,CACvH,GACF,GACF;AAAA,UACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,iEAAgE,UAAA,wBAE9E;AAAA,YACAA,gBAAAA,EAAAA,IAAC,KAAA,EAAE,WAAU,uDAAsD,UAAA,6CAAA,CAEnE;AAAA,UAAA,EAAA,CACF;AAAA,QAAA,GACF;AAAA,QAGAA,gBAAAA,MAAC,OAAA,EAAI,WAAU,QACb,gCAAC,OAAA,EAAI,WAAU,0FACb,UAAAA,gBAAAA,EAAAA,IAAC,KAAA,EAAE,WAAU,oDACV,UAAAmG,EAAA,CACH,GACF,GACF;AAAA,QAGCI,EAAmB,SAAS,KAC3B/C,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,QACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,sCAAqC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC5F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,8BAA6B,EAAA,CACpG;AAAA,YACAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,0CAAyC,UAAA,sBAAA,CAEvD;AAAA,UAAA,GACF;AAAA,UAEAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,aACZ,UAAAuG,EAAmB,IAAI,CAACxH,GAAgB0F,MACvCjB,gBAAAA,EAAAA,KAAC,OAAA,EAAwC,WAAU,YAEjD,UAAA;AAAA,YAAAxD,gBAAAA,MAAC,OAAA,EAAI,WAAU,+BACb,UAAAA,gBAAAA,MAAC,SAAI,WAAW3D;AAAA,cACd;AAAA,cACAoI,MAAU,IAAI,sDACdA,MAAU,IAAI,4DACd;AAAA,YAAA,GAEC,UAAAA,IAAQ,EAAA,CACX,EAAA,CACF;AAAA,YAEAzE,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA;AAAAA,cAACgG;AAAA,cAAA;AAAA,gBACC,gBAAAjH;AAAA,gBACA,OAAA4C;AAAA,gBACA,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,SAAS0E;AAAA,cAAA;AAAA,YAAA,EACX,CACF;AAAA,UAAA,EAAA,GArBQtH,EAAe,SAAS0F,CAsBlC,CACD,EAAA,CACH;AAAA,QAAA,GACF;AAAA,QAIDlD,EAAgB,SAAS6E,KACxBpG,gBAAAA,EAAAA,IAAC,SAAI,WAAU,QACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,2FACb,UAAAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,sCAAqC,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC5F,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6DAA4D,EAAA,CACnI;AAAA,UACAwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,wDACb,UAAA;AAAA,YAAAjC,EAAgB,SAAS6E;AAAA,YAAuB;AAAA,YAA2B7E,EAAgB,SAAS6E,IAAyB,IAAI,MAAM;AAAA,YAAG;AAAA,UAAA,EAAA,CAC7I;AAAA,QAAA,EAAA,CACF,GACF,GACF;AAAA,QAIF5C,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,mCACZ,UAAA;AAAA,UAAA8C,KACC9C,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS8C;AAAA,cACT,WAAU;AAAA,cAEV,UAAA;AAAA,gBAAAtG,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,iKAAgK,EAAA,CACvO;AAAA,gBAAM;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,UAKVwD,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS,MAAM;AACb,gBAAI+C,EAAmB,SAAS,MAC9BF,KAAA,QAAAA,EAAwBE,EAAmB,CAAC,EAAE,OAAOA,EAAmB,CAAC,EAAE;AAAA,cAC7E;AAAA,cAEF,WAAU;AAAA,cAEV,UAAA;AAAA,gBAAAvG,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gFAA+E,EAAA,CACtJ;AAAA,gBAAM;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAER,GACF;AAAA,QAGAA,gBAAAA,EAAAA,IAAC,SAAI,WAAU,+EACb,gCAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA,oBAAA,CAE3D,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGN,GC1Ja0G,KAAkE,CAAC;AAAA,EAC9E,gBAAA3H;AAAA,EACA,gBAAA4H;AAAA,EACA,eAAAC,IAAgB;AAAA,EAChB,OAAAjF;AAAA,EACA,aAAAkF,IAAc;AAAA,EACd,SAAAC;AAAA,EACA,WAAAzH;AAAA,EACA,OAAAC;AACF,MAAM;AACJ,QAAM,CAACyH,GAAWC,CAAY,IAAI7J,EAAS,EAAK,GAE1C8J,IAAmB,MAAM;AAC7B,IAAAD,EAAa,EAAI,GACjBF,KAAA,QAAAA,EAAU/H;AAAA,EAAc,GAGpBmI,IAAmB,MAAM;AAC7B,IAAAF,EAAa,EAAK;AAAA,EAAA,GAMdG,IAAqB,MAAM;AAC/B,YAAQP,GAAA;AAAA,MACN,KAAK;AACH,eAAO,IAAID,CAAc;AAAA,MAC3B,KAAK;AACH,eAAOA,EAAe,SAAA;AAAA,MACxB,KAAK;AAAA,MACL;AACE,eAAOA,EAAe,SAAA;AAAA,IAAS;AAAA,EACnC,GAGIS,IAAkB/K;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,MAEE,wJAAwJuK,MAAkB;AAAA;AAAA,MAG1K,gCAAgCA,MAAkB;AAAA;AAAA,MAGlD,uCAAuCA,MAAkB;AAAA,IAAA;AAAA,IAE3DvH;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,yBACd,UAAA;AAAA,IAAAxD,gBAAAA,EAAAA;AAAAA,MAACf;AAAA,MAAA;AAAA,QACC,MAAMF,EAAe;AAAA,QACrB,YAAYA,EAAe;AAAA,QAC3B,WAAWA,EAAe;AAAA,QAC1B,cAAc;AAAA,UACZ,OAAOA,EAAe;AAAA,UACtB,YAAYA,EAAe;AAAA,UAC3B,gBAAA4H;AAAA,UACA,eAAAC;AAAA,QAAA;AAAA,QAEF,WAAWQ;AAAA,QACX,OAAA9H;AAAA,QAEA,UAAAU,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,OAAOuE;AAAA,YACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,YAC1B,cAAcsF;AAAA,YACd,cAAcC;AAAA,YAEb,UAAAC,EAAA;AAAA,UAAmB;AAAA,QAAA;AAAA,MACtB;AAAA,IAAA;AAAA,IAIDN,KAAeE,KACd/G,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sEACb,UAAAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,8GACb,UAAA;AAAA,MAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sBAAsB,UAAAjB,EAAe,OAAM;AAAA,MACzDA,EAAe,UACdiB,gBAAAA,EAAAA,IAAC,SAAI,WAAU,4CACZ,YAAe,OAAO,SAAS,MAC5B,GAAGjB,EAAe,OAAO,UAAU,GAAG,GAAG,CAAC,QAC1CA,EAAe,QAErB;AAAA,MAEDA,EAAe,sBAAsB,OACpCyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,mDACZ,UAAA;AAAA,QAAA,KAAK,MAAMzE,EAAe,qBAAqB,GAAG;AAAA,QAAE;AAAA,MAAA,GACvD;AAAA,MAEFiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wDAAuD,UAAA,+BAEtE;AAAA,MAEAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6JAAA,CAA6J;AAAA,IAAA,EAAA,CAC9K,EAAA,CACF;AAAA,EAAA,GAEJ;AAEJ,GC/GaqH,KAAwD,CAAC;AAAA,EACpE,iBAAA9F;AAAA,EACA,kBAAA+F;AAAA,EACA,OAAA3F;AAAA,EACA,kBAAA4F,IAAmB;AAAA,EACnB,eAAAX,IAAgB;AAAA,EAChB,iBAAAY;AAAA,EACA,WAAAnI;AAAA,EACA,OAAAC;AACF,MAAM;;AACJ,QAAM,CAACmI,GAAuBC,CAAwB,IAAIvK,EAAsC,IAAI,GAG9FwK,IAAmBpK,GAAQ,MAAM;AACrC,QAAI,CAAC+J,KAAoB/F,EAAgB,WAAW;AAClD,aAAO,EAAE,MAAM+F,GAAkB,aAAa,oBAAI,MAAI;AAGxD,QAAIM,IAAgBN;AACpB,UAAMO,wBAAkB,IAAA;AAOxB,WAJ8B,CAAC,GAAGtG,CAAe,EAC9C,KAAK,CAACiF,GAAGC,MAAMA,EAAE,qBAAqBD,EAAE,kBAAkB,EAGvC,QAAQ,CAACzH,GAAgB0F,MAAU;AACvD,YAAMkC,IAAiBlC,IAAQ,GACzBqD,IAAQ/I,EAAe;AAG7B,MAAA8I,EAAY,IAAIlB,GAAgB5H,CAAc;AAG9C,YAAMgJ,IAAa,IAAI,OAAO,MAAMD,EAAM,QAAQ,uBAAuB,MAAM,CAAC,OAAO,IAAI;AAG3F,UAAIC,EAAW,KAAKH,CAAa;AAC/B,QAAAA,IAAgBA,EAAc,QAAQG,GAAY,CAACC,MAC1C,GAAGA,CAAK,cAAcrB,CAAc,IAC5C;AAAA,WACI;AAGL,cAAMsB,IAAWlJ,EAAe,YAAY,CAAA;AAC5C,YAAImJ,IAAW;AAEf,mBAAW7F,KAAW4F,GAAU;AAC9B,gBAAME,IAAe,IAAI,OAAO,MAAM9F,EAAQ,QAAQ,uBAAuB,MAAM,CAAC,OAAO,IAAI;AAC/F,cAAI8F,EAAa,KAAKP,CAAa,KAAK,CAACM,GAAU;AACjD,YAAAN,IAAgBA,EAAc,QAAQO,GAAc,CAACH,OACnDE,IAAW,IACJ,GAAGF,CAAK,cAAcrB,CAAc,KAC5C;AACD;AAAA,UAAA;AAAA,QACF;AAIF,QAAKuB,MACHN,KAAiB,cAAcjB,CAAc;AAAA,MAC/C;AAAA,IACF,CACD,GAEM,EAAE,MAAMiB,GAAe,aAAAC,EAAA;AAAA,EAAY,GACzC,CAACP,GAAkB/F,CAAe,CAAC,GAGhC6G,IAA0B,MAAM;AACpC,UAAM,EAAE,MAAAC,GAAM,aAAAR,EAAA,IAAgBF;AAG9B,WAFcU,EAAK,MAAM,wBAAwB,EAEpC,IAAI,CAACC,GAAM7D,MAAU;AAChC,YAAM8D,IAAgBD,EAAK,MAAM,wBAAwB;AAEzD,UAAIC,GAAe;AACjB,cAAM5B,IAAiB,SAAS4B,EAAc,CAAC,CAAC,GAC1CxJ,IAAiB8I,EAAY,IAAIlB,CAAc;AAErD,YAAI5H;AACF,iBACEiB,gBAAAA,EAAAA;AAAAA,YAAC0G;AAAA,YAAA;AAAA,cAEC,gBAAA3H;AAAA,cACA,gBAAA4H;AAAA,cACA,eAAAC;AAAA,cACA,OAAAjF;AAAA,cACA,aAAa;AAAA,cACb,SAAS,CAACH,MAAQ;AAChB,gBAAAkG,EAAyBlG,CAAG,GAC5BgG,KAAA,QAAAA,EAAkBhG;AAAA,cAAG;AAAA,YACvB;AAAA,YATK,YAAYmF,CAAc,IAAIlC,CAAK;AAAA,UAAA;AAAA,MAY9C;AAGF,aAAOzE,gBAAAA,EAAAA,IAAC,QAAA,EAAkB,UAAAsI,EAAA,GAAR7D,CAAa;AAAA,IAAA,CAChC;AAAA,EAAA,GAGGH,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAO;AAAA,QACL,GAAGC;AAAA,QACH,aAAY5C,KAAA,gBAAAA,EAAO,eAAc;AAAA,QACjC,IAAGS,IAAAT,KAAA,gBAAAA,EAAO,eAAP,gBAAAS,EAAmB;AAAA,QACtB,GAAG9C;AAAA,MAAA;AAAA,MAEL,qBAAmBqC,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA3B,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yEACZ,UAAAoI,EAAA,GACH;AAAA,QAGCb,KAAoBhG,EAAgB,SAAS,KAC5CvB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wBACb,UAAAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,uDACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,MAAA,EAAG,WAAU,uFACZ,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,sIAAqI,EAAA,CAC5M;AAAA,YAAM;AAAA,UAAA,GAER;AAAA,gCAEC,OAAA,EAAI,WAAU,aACZ,UAAAuB,EACE,KAAK,CAACiF,GAAGC,MAAMA,EAAE,qBAAqBD,EAAE,kBAAkB,EAC1D,IAAI,CAACzH,GAAgB0F,MACpBjB,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cAEC,WAAWnH;AAAA,gBACT;AAAA,gBACA;AAAA,kBACE,+EACEoL,KAAA,gBAAAA,EAAuB,WAAU1I,EAAe;AAAA,kBAClD,gDACE0I,KAAA,gBAAAA,EAAuB,WAAU1I,EAAe;AAAA,gBAAA;AAAA,cACpD;AAAA,cAIF,UAAA;AAAA,gBAAAiB,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sBACb,UAAAA,gBAAAA,EAAAA;AAAAA,kBAAC0G;AAAA,kBAAA;AAAA,oBACC,gBAAA3H;AAAA,oBACA,gBAAgB0F,IAAQ;AAAA,oBACxB,eAAAmC;AAAA,oBACA,OAAAjF;AAAA,oBACA,aAAa;AAAA,kBAAA;AAAA,gBAAA,GAEjB;AAAA,gBAGA3B,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,kBACb,UAAAA,gBAAAA,EAAAA;AAAAA,kBAACgG;AAAA,kBAAA;AAAA,oBACC,gBAAAjH;AAAA,oBACA,OAAA4C;AAAA,oBACA,SAAS;AAAA,oBACT,YAAY;AAAA,kBAAA;AAAA,gBAAA,EACd,CACF;AAAA,cAAA;AAAA,YAAA;AAAA,YA9BK5C,EAAe,SAAS0F;AAAA,UAAA,CAgChC,EAAA,CACL;AAAA,QAAA,EAAA,CACF,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GCrLa+D,KAAoE,CAAC;AAAA,EAChF,iBAAAjH;AAAA,EACA,QAAAtJ;AAAA,EACA,OAAA0J;AAAA,EACA,qBAAAwE;AAAA,EACA,WAAAsC;AAAA,EACA,uBAAApC;AAAA,EACA,WAAAqC;AAAA,EACA,WAAArJ;AACF,MAAM;AACJ,QAAM,CAACsJ,GAAWC,CAAY,IAAIzL,EAASlF,EAAO,aAAa,EAAK,GAC9D,CAAC4Q,GAAaC,CAAc,IAAI3L,EAAS,EAAK;AAcpD,MAZAuC,EAAU,MAAM;AACd,QAAIzH,EAAO,WAAWA,EAAO,UAAU,GAAG;AACxC,YAAM8Q,IAAQ,WAAW,MAAM;AAC7B,QAAAH,EAAa,EAAI,GACjBE,EAAe,EAAI;AAAA,MAAA,GAClB7Q,EAAO,OAAO;AACjB,aAAO,MAAM,aAAa8Q,CAAK;AAAA,IAAA;AAE/B,MAAAD,EAAe,EAAI;AAAA,EACrB,GACC,CAAC7Q,EAAO,OAAO,CAAC,GAEf,CAAC0Q,KAAapH,EAAgB,WAAW;AAC3C,WAAO;AAGT,QAAMyH,IAAqB/Q,EAAO,sBAAsB,GAClDgR,IAAyB1H,EAAgB,MAAM,GAAGyH,CAAkB,GAEpEE,IAA4B,CAACvK,GAAcO,MAAuB;AACtE,IAAAmH,KAAA,QAAAA,EAAwB1H,GAAMO;AAAA,EAAU,GAGpCiK,IAAgB,MAAM;AAC1B,IAAAP,EAAa,EAAK,GAClBF,KAAA,QAAAA;AAAA,EAAY,GAIRU,IAAgB,MAAM;AAC1B,YAAQnR,EAAO,aAAA;AAAA,MACb,KAAK;AACH,eAAOkO,IACLnG,gBAAAA,EAAAA;AAAAA,UAACkG;AAAA,UAAA;AAAA,YACC,iBAAiB+C;AAAA,YACjB,qBAAA9C;AAAA,YACA,OAAAxE;AAAA,YACA,wBAAwBqH;AAAA,YACxB,uBAAuBE;AAAA,YACvB,wBAAwBR;AAAA,UAAA;AAAA,QAAA,IAExB;AAAA,MAEN,KAAK;AACH,eACE1I,gBAAAA,MAAC,SAAI,WAAU,aACZ,YAAuB,IAAI,CAACjB,GAAgB0F,MAC3CzE,gBAAAA,EAAAA;AAAAA,UAACgG;AAAA,UAAA;AAAA,YAEC,gBAAAjH;AAAA,YACA,OAAA4C;AAAA,YACA,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAASuH;AAAA,UAAA;AAAA,UALJnK,EAAe,SAAS0F;AAAA,QAAA,CAOhC,GACH;AAAA,MAGJ,KAAK;AACH,eAAOwE,EAAuB,SAAS,IACrCzF,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,uBACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,mCAAA,CAAmC;AAAA,YAClDwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,wDACb,UAAA;AAAA,cAAAyF,EAAuB;AAAA,cAAO;AAAA,cAAmBA,EAAuB,SAAS,IAAI,OAAO;AAAA,cAAG;AAAA,YAAA,EAAA,CAClG;AAAA,UAAA,GACF;AAAA,UACAjJ,gBAAAA,EAAAA;AAAAA,YAACgG;AAAA,YAAA;AAAA,cACC,gBAAgBiD,EAAuB,CAAC;AAAA,cACxC,OAAAtH;AAAA,cACA,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,SAASuH;AAAA,YAAA;AAAA,UAAA;AAAA,UAEVD,EAAuB,SAAS,KAC/BzF,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,iDAAgD,UAAA;AAAA,YAAA;AAAA,YAC3DyF,EAAuB,SAAS;AAAA,YAAE;AAAA,YAAqBA,EAAuB,SAAS,IAAI,MAAM;AAAA,UAAA,EAAA,CACrG;AAAA,QAAA,EAAA,CAEJ,IACE;AAAA,MAEN,KAAK;AACH,eAAO9C,IACLnG,gBAAAA,EAAAA;AAAAA,UAACqH;AAAA,UAAA;AAAA,YACC,iBAAiB4B;AAAA,YACjB,kBAAkB9C;AAAA,YAClB,OAAAxE;AAAA,YACA,kBAAkB;AAAA,YAClB,eAAc;AAAA,YACd,uBAAuBuH;AAAA,UAAA;AAAA,QAAA,IAEvB;AAAA,MAEN,KAAK;AACH,eACE1F,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,yHACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,yCACb,UAAA;AAAA,YAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,mCAAA,CAAmC;AAAA,cAClDA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,0DAAyD,UAAA,sBAAA,CAEzE;AAAA,YAAA,GACF;AAAA,YACC0I,KACC1I,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAASmJ;AAAA,gBACT,WAAU;AAAA,gBACV,cAAW;AAAA,gBAEX,UAAAnJ,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,EAAA,CAC9F;AAAA,cAAA;AAAA,YAAA;AAAA,UACF,GAEJ;AAAA,UACAA,gBAAAA,EAAAA,IAAC,SAAI,WAAU,aACZ,YAAuB,IAAI,CAACjB,GAAgB0F,MAC3CzE,gBAAAA,EAAAA;AAAAA,YAACgG;AAAA,YAAA;AAAA,cAEC,gBAAAjH;AAAA,cACA,OAAA4C;AAAA,cACA,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,SAASuH;AAAA,YAAA;AAAA,YALJnK,EAAe,SAAS0F;AAAA,UAAA,CAOhC,EAAA,CACH;AAAA,QAAA,GACF;AAAA,MAGJ;AACE,eACEzE,gBAAAA,MAAC,SAAI,WAAU,aACZ,YAAuB,IAAI,CAACjB,GAAgB0F,MAC3CzE,gBAAAA,EAAAA;AAAAA,UAAC0B;AAAA,UAAA;AAAA,YAEC,gBAAA3C;AAAA,YACA,OAAA4C;AAAA,YACA,gBAAgB;AAAA,YAChB,YAAY;AAAA,YACZ,SAASuH;AAAA,UAAA;AAAA,UALJnK,EAAe,SAAS0F;AAAA,QAAA,CAOhC,GACH;AAAA,IAAA;AAAA,EAEN,GAGIH,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,MACE,2BAA2B,CAACwM;AAAA,MAC5B,6BAA6BA;AAAA,MAC7B,wCAAwC5Q,EAAO,gBAAgB;AAAA,MAC/D,QAAQA,EAAO,gBAAgB;AAAA,MAC/B,4DAA4DA,EAAO,gBAAgB;AAAA,IAAA;AAAA,IAErFoH;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAC1B,uBAAqB1J,EAAO;AAAA,MAC5B,mBAAiBwQ;AAAA,MAEhB,UAAA;AAAA,QAAAW,EAAA;AAAA,QAGAnR,EAAO,kBAAkB,MACxB+H,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yBACb,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA,oBAAA,CAE3D,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GC3MaqJ,KAAsD,CAAC;AAAA,EAClE,SAAA3L;AAAA,EACA,OAAAiE;AAAA,EACA,uBAAA0E;AAAA,EACA,WAAAhH;AACF,MAAM;AACJ,QAAMiK,IAAS5L,EAAQ,SAAS,QAC1B6L,IAAc7L,EAAQ,SAAS,aAE/B8L,IAAiBnN;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,MACE,oBAAoBiN;AAAA,IAAA;AAAA,IAEtBjK;AAAA,EAAA,GAGIoK,IAAgBpN;AAAA,IACpB;AAAA,IACA;AAAA,MACE,2DAA2DiN;AAAA,MAC3D,kEAAkEC;AAAA,MAClE,yEAAyE7L,EAAQ,SAAS;AAAA,IAAA;AAAA,EAC5F,GAGI6G,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD,QAErB+H,IAAa,CAACC,MACXA,EAAU,mBAAmB,IAAI,EAAE,MAAM,WAAW,QAAQ,WAAW;AAGhF,SACEnG,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWgG;AAAA,MACX,OAAOjF;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAGzB,UAAA;AAAA,QAAA,CAAC2H,KACAtJ,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,qHACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sBAAqB,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC5E,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6BAAA,CAA6B,EAAA,CACpG,GACF;AAAA,QAGDsJ,KACCtJ,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,qGACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,sEAAA,CAAsE,EAAA,CAC7I,GACF;AAAA,+BAID,OAAA,EAAI,WAAW,iBAAiBsJ,IAAS,cAAc,aAAa,WAEnE,UAAA;AAAA,UAAAtJ,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAWyJ,GACd,UAAAzJ,gBAAAA,EAAAA,IAAC,SAAI,WAAU,mCACZ,UAAAtC,EAAQ,QAAA,CACX,EAAA,CACF;AAAA,UAGAsC,gBAAAA,MAAC,SAAI,WAAW3D;AAAA,YACd;AAAA,YACA,EAAE,cAAciN,EAAA;AAAA,UAAO,GAEtB,UAAAI,EAAWhM,EAAQ,SAAS,EAAA,CAC/B;AAAA,UAGCA,EAAQ,mBAAmBA,EAAQ,gBAAgB,SAAS,KAC3D8F,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,wBAEb,UAAA;AAAA,YAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,8BAA6B,EAAA,CACpG;AAAA,cACAwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,wDACb,UAAA;AAAA,gBAAA9F,EAAQ,gBAAgB;AAAA,gBAAO;AAAA,gBAAgBA,EAAQ,gBAAgB,SAAS,IAAI,MAAM;AAAA,gBAAG;AAAA,cAAA,EAAA,CAChG;AAAA,YAAA,GACF;AAAA,YAGAsC,gBAAAA,EAAAA;AAAAA,cAACwI;AAAA,cAAA;AAAA,gBACC,iBAAiB9K,EAAQ;AAAA,gBACzB,QAAQ;AAAA,kBACN,aAAa;AAAA,kBACb,SAAS;AAAA,kBACT,oBAAoB;AAAA,kBACpB,eAAe;AAAA,kBACf,UAAU;AAAA,kBACV,SAAS;AAAA,gBAAA;AAAA,gBAEX,OAAAiE;AAAA,gBACA,uBAAA0E;AAAA,gBACA,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UACZ,EAAA,CACF;AAAA,QAAA,EAAA,CAEJ;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGN,GC7GauD,KAAkD,CAAC;AAAA,EAC9D,aAAAC,IAAc;AAAA,EACd,UAAAC,IAAW;AAAA,EACX,aAAAC,IAAc,CAAA;AAAA,EACd,OAAApI;AAAA,EACA,eAAAqI;AAAA,EACA,WAAA3K;AACF,MAAM;AACJ,QAAM,CAAC3B,GAASuM,CAAU,IAAI9M,EAAS,EAAE,GACnC,CAAC+M,GAAiBC,CAAkB,IAAIhN,EAAS,EAAK,GACtD,CAACiN,GAAqBC,CAAsB,IAAIlN,EAAmB,CAAA,CAAE,GACrEmN,IAAW9K,GAA4B,IAAI,GAE3C+K,IAAoB,CAAC3G,MAA8C;AACvE,UAAMvK,IAAQuK,EAAE,OAAO;AAIvB,QAHAqG,EAAW5Q,CAAK,GAGZA,EAAM,KAAA,KAAU0Q,EAAY,SAAS,GAAG;AAC1C,YAAMS,IAAWT,EAAY;AAAA,QAAO,OAClCU,EAAW,YAAA,EAAc,SAASpR,EAAM,aAAa;AAAA,MAAA;AAEvD,MAAAgR,EAAuBG,CAAQ,GAC/BL,EAAmBK,EAAS,SAAS,CAAC;AAAA,IAAA;AAEtC,MAAAL,EAAmB,EAAK;AAI1B,IAAIG,EAAS,YACXA,EAAS,QAAQ,MAAM,SAAS,QAChCA,EAAS,QAAQ,MAAM,SAAS,GAAG,KAAK,IAAIA,EAAS,QAAQ,cAAc,GAAG,CAAC;AAAA,EACjF,GAGII,IAAgB,CAAC9G,MAA0C;AAC/D,IAAIA,EAAE,QAAQ,WAAW,CAACA,EAAE,aAC1BA,EAAE,eAAA,GACF+G,EAAA;AAAA,EACF,GAGIA,IAAa,MAAM;AACvB,UAAMC,IAAiBlN,EAAQ,KAAA;AAC/B,IAAIkN,KAAkB,CAACd,KAAYE,MACjCA,EAAcY,CAAc,GAC5BX,EAAW,EAAE,GACbE,EAAmB,EAAK,GAGpBG,EAAS,YACXA,EAAS,QAAQ,MAAM,SAAS;AAAA,EAEpC,GAGIO,IAAwB,CAACJ,MAAuB;AACpD,IAAAR,EAAWQ,CAAU,GACrBN,EAAmB,EAAK,GACpBG,EAAS,WACXA,EAAS,QAAQ,MAAA;AAAA,EACnB,GAGIhG,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIyL,IAAezO;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,iCAAiCyN;AAAA,IAAA;AAAA,EACnC,GAGIiB,IAAoB1O;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,MACE,4CAA4CqB,EAAQ,KAAA,KAAU,CAACoM;AAAA,MAC/D,qFAAqF,CAACpM,EAAQ,UAAUoM;AAAA,IAAA;AAAA,EAC1G,GAGIvF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAGzB,UAAA;AAAA,QAAAuI,KAAmBE,EAAoB,SAAS,KAC/CpK,gBAAAA,EAAAA,IAAC,SAAI,WAAU,uKACZ,UAAAoK,EAAoB,MAAM,GAAG,CAAC,EAAE,IAAI,CAACK,GAAYhG,MAChDzE,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YAEC,SAAS,MAAM6K,EAAsBJ,CAAU;AAAA,YAC/C,WAAU;AAAA,YAET,UAAAA;AAAA,UAAA;AAAA,UAJIhG;AAAA,QAAA,CAMR,GACH;AAAA,QAIFjB,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,YACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,KAAKsK;AAAA,cACL,OAAO5M;AAAA,cACP,UAAU6M;AAAA,cACV,WAAWG;AAAA,cACX,aAAAb;AAAA,cACA,UAAAC;AAAA,cACA,MAAM;AAAA,cACN,WAAWgB;AAAA,cACX,OAAO,EAAE,WAAW,QAAQ,WAAW,QAAA;AAAA,YAAQ;AAAA,UAAA;AAAA,UAIjD9K,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS2K;AAAA,cACT,UAAU,CAACjN,EAAQ,KAAA,KAAUoM;AAAA,cAC7B,WAAWiB;AAAA,cACX,cAAW;AAAA,cAEX,UAAA/K,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,oCAAmC,EAAA,CAC1G;AAAA,YAAA;AAAA,UAAA;AAAA,QACF,GACF;AAAA,QAGAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,mFACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,UAAK,UAAA,gDAAA,CAA6C;AAAA,UACnDwD,gBAAAA,OAAC,UAAK,WAAWnH;AAAA,YACf;AAAA,YACA,EAAE,aAAaqB,EAAQ,SAAS,IAAA;AAAA,UAAI,GAEnC,UAAA;AAAA,YAAAA,EAAQ;AAAA,YAAO;AAAA,UAAA,EAAA,CAClB;AAAA,QAAA,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGN,GCzJasN,KAA0D,CAAC;AAAA,EACtE,UAAAC;AAAA,EACA,QAAAhT;AAAA,EACA,OAAA0J;AAAA,EACA,WAAAuJ,IAAY;AAAA,EACZ,eAAAlB;AAAA,EACA,uBAAA3D;AAAA,EACA,WAAAhH;AACF,MAAM;AACJ,QAAM8L,IAAiB3L,GAAuB,IAAI,GAC5C4L,IAAuB5L,GAAuB,IAAI;AAGxD,EAAAE,EAAU,MAAM;AACd,IAAIyL,EAAe,WACjBA,EAAe,QAAQ,eAAe,EAAE,UAAU,UAAU;AAAA,EAC9D,GACC,CAACF,CAAQ,CAAC;AAEb,QAAM3G,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD,QAGrB0J,IAAkBpT,EAAO,cAC3BgT,EAAS,MAAM,CAAChT,EAAO,WAAW,IAClCgT;AAEJ,SACEzH,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA3B,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,KAAKoL;AAAA,YACL,WAAU;AAAA,YAET,YAAgB,WAAW,IAC1B5H,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gEACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oJACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6BAAA,CAA6B,EAAA,CACpG,EAAA,CACF;AAAA,cACAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,+DAA8D,UAAA,wBAE5E;AAAA,cACAA,gBAAAA,EAAAA,IAAC,KAAA,EAAE,WAAU,qDAAoD,UAAA,8GAAA,CAEjE;AAAA,YAAA,EAAA,CACF,IAEAwD,gBAAAA,EAAAA,KAAAG,EAAAA,UAAA,EACG,UAAA;AAAA,cAAA0H,EAAgB,IAAI,CAAC3N,MACpBsC,gBAAAA,EAAAA;AAAAA,gBAACqJ;AAAA,gBAAA;AAAA,kBAEC,SAAA3L;AAAA,kBACA,OAAAiE;AAAA,kBACA,uBAAA0E;AAAA,gBAAA;AAAA,gBAHK3I,EAAQ;AAAA,cAAA,CAKhB;AAAA,cAGAwN,KAAajT,EAAO,0BAA0B,MAC7CuL,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0BACb,UAAA;AAAA,gBAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,qHACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sBAAqB,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAC5E,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6BAAA,CAA6B,EAAA,CACpG,EAAA,CACF;AAAA,sCACC,OAAA,EAAI,WAAU,+DACb,UAAAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,kBAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,mEAAA,CAAmE;AAAA,kBAClFA,gBAAAA,MAAC,SAAI,WAAU,oEAAmE,OAAO,EAAE,gBAAgB,UAAU;AAAA,kBACrHA,gBAAAA,MAAC,SAAI,WAAU,oEAAmE,OAAO,EAAE,gBAAgB,SAAO,CAAG;AAAA,gBAAA,EAAA,CACvH,EAAA,CACF;AAAA,cAAA,GACF;AAAA,cAGFA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,KAAKmL,EAAA,CAAgB;AAAA,YAAA,EAAA,CAC5B;AAAA,UAAA;AAAA,QAAA;AAAA,QAKHlT,EAAO,qBAAqBA,EAAO,eAAeA,EAAO,YAAY,SAAS,KAAKgT,EAAS,WAAW,KACtGzH,gBAAAA,OAAC,OAAA,EAAI,WAAU,aACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iDAAgD,UAAA,sBAAkB;AAAA,UACjFA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wBACZ,UAAA/H,EAAO,YAAY,MAAM,GAAG,CAAC,EAAE,IAAI,CAACwS,GAAYhG,MAC/CzE,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cAEC,SAAS,MAAMgK,KAAA,gBAAAA,EAAgBS;AAAA,cAC/B,WAAU;AAAA,cAET,UAAAA;AAAA,YAAA;AAAA,YAJIhG;AAAA,UAAA,CAMR,EAAA,CACH;AAAA,QAAA,GACF;AAAA,QAIDxM,EAAO,mBAAmB,MAAS+R,KAClChK,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sDACb,UAAAA,gBAAAA,EAAAA;AAAAA,UAAC4J;AAAA,UAAA;AAAA,YACC,aAAa3R,EAAO,eAAe;AAAA,YACnC,UAAUiT;AAAA,YACV,aAAajT,EAAO;AAAA,YACpB,OAAA0J;AAAA,YACA,eAAAqI;AAAA,UAAA;AAAA,QAAA,EACF,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GC9HasB,KAAwD,CAAC;AAAA,EACpE,QAAArT;AAAA,EACA,OAAA0J;AAAA,EACA,OAAAmG,IAAQ;AAAA,EACR,UAAAyD,IAAW;AAAA,EACX,QAAQC;AAAA,EACR,UAAAC;AAAA,EACA,eAAAzB;AAAA,EACA,uBAAA3D;AAAA,EACA,qBAAAqF;AAAA,EACA,2BAAAC;AAAA,EACA,gBAAAC,IAAiB;AAAA,EACjB,yBAAAC,IAA0B;AAAA,EAC1B,6BAAAC;AAAA,EACA,WAAAzM;AACF,MAAM;AACJ,QAAM,CAAC0M,GAAgBC,CAAiB,IAAI7O,EAASlF,EAAO,YAAY,EAAK,GACvE,CAACgT,GAAUgB,CAAW,IAAI9O,EAAwB,CAAA,CAAE,GACpD,CAAC+N,GAAWgB,CAAY,IAAI/O,EAAS,EAAK,GAC1C,CAACgP,GAAeC,CAAgB,IAAIjP,EAAS,EAAK,GAElDkP,IAASb,MAAqB,SAAYA,IAAmBO;AAGnE,EAAArM,EAAU,MAAM;AACd,QAAIzH,EAAO,sBAAsBA,EAAO,kBAAkBgT,EAAS,WAAW,GAAG;AAC/E,YAAMqB,IAA8B;AAAA,QAClC,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,SAASrU,EAAO;AAAA,QAChB,+BAAe,KAAA;AAAA,MAAK;AAEtB,MAAAgU,EAAY,CAACK,CAAc,CAAC;AAAA,IAAA;AAAA,EAC9B,GACC,CAACrU,EAAO,oBAAoBA,EAAO,gBAAgBgT,EAAS,MAAM,CAAC,GAGtEvL,EAAU,MAAM;AACd,QAAIgM,KAAuBA,EAAoB,SAAS,KAAKG,GAAyB;AACpF,YAAMU,IAA2B;AAAA,QAC/B,IAAI,QAAQ,KAAK,IAAA,CAAK;AAAA,QACtB,MAAM;AAAA,QACN,SAASZ,IACL,aAAaA,CAAyB,+CACtC;AAAA,QACJ,+BAAe,KAAA;AAAA,QACf,iBAAiBD;AAAA,MAAA;AAInB,MAAIF,MAAqB,UACvBQ,EAAkB,EAAI,GAIxBC,EAAY,CAAAO,MAEaA,EAAK,KAAK,CAAAC,MAAOA,EAAI,GAAG,WAAW,OAAO,CAAC,IAEzDD,EAAK;AAAA,QAAI,OACdC,EAAI,GAAG,WAAW,OAAO,IAAIF,IAAcE;AAAA,MAAA,IAGxC,CAAC,GAAGD,GAAMD,CAAW,CAC7B;AAAA,IAAA;AAAA,EACH,GACC,CAACb,GAAqBG,GAAyBF,GAA2BH,CAAgB,CAAC;AAE9F,QAAMkB,IAAe,MAAM;AACzB,IAAIjB,IACFA,EAAA,IAEAO,EAAkB,CAACD,CAAc,GAEnCK,EAAiB,EAAI;AAAA,EAAA,GAGjBO,IAAoB,OAAOC,MAA2B;AAC1D,QAAI,CAAC5C,EAAe;AAGpB,UAAM6C,IAA2B;AAAA,MAC/B,IAAI,QAAQ,KAAK,IAAA,CAAK;AAAA,MACtB,MAAM;AAAA,MACN,SAASD;AAAA,MACT,+BAAe,KAAA;AAAA,IAAK;AAGtB,IAAAX,EAAY,CAAAO,MAAQ,CAAC,GAAGA,GAAMK,CAAW,CAAC,GAC1CX,EAAa,EAAI;AAEjB,QAAI;AAEF,YAAMlC,EAAc4C,CAAc;AAAA,IAAA,SAE3BxP,GAAO;AACd,cAAQ,MAAM,0BAA0BA,CAAK;AAC7C,YAAM0P,IAA4B;AAAA,QAChC,IAAI,SAAS,KAAK,IAAA,CAAK;AAAA,QACvB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,+BAAe,KAAA;AAAA,MAAK;AAEtB,MAAAb,EAAY,CAAAO,MAAQ,CAAC,GAAGA,GAAMM,CAAY,CAAC;AAAA,IAAA,UAC7C;AACE,MAAAZ,EAAa,EAAK;AAAA,IAAA;AAAA,EACpB,GAIIa,IAAoB,MAAM;AAC9B,YAAQ9U,EAAO,MAAA;AAAA,MACb,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB;AAAS,eAAO;AAAA,IAAA;AAAA,EAClB,GAcIqM,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,KAZyB,MAAM;AAC/B,cAAQpE,EAAO,UAAA;AAAA,QACb,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAe,iBAAO;AAAA,QAC3B,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAY,iBAAO;AAAA,QACxB;AAAS,iBAAO;AAAA,MAAA;AAAA,IAClB,GAMA;AAAA,IACAoH;AAAA,EAAA,GAGI2N,IAAc3Q;AAAA,IAClB;AAAA,IACA0Q,EAAA;AAAA,IACA;AAAA,MACE,0CAA0C,CAACV;AAAA,MAC3C,yBAAyBA;AAAA,IAAA;AAAA,EAC3B,GAGI9H,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWc;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA3B,gBAAAA,MAAC,OAAA,EAAI,WAAWgN,GACb,UAAAX,KACC7I,gBAAAA,EAAAA,KAAAG,YAAA,EAEE,UAAA;AAAA,UAAAH,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,iGACb,UAAA;AAAA,YAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,cAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,gFACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6BAAA,CAA6B,EAAA,CACpG,EAAA,CACF;AAAA,qCACC,OAAA,EACC,UAAA;AAAA,gBAAAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,yBAAyB,UAAA8H,GAAM;AAAA,gBAC7C9H,gBAAAA,EAAAA,IAAC,KAAA,EAAE,WAAU,yBAAyB,UAAAuL,EAAA,CAAS;AAAA,cAAA,EAAA,CACjD;AAAA,YAAA,GACF;AAAA,YACA/H,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BAEZ,UAAA;AAAA,cAAAkI,KAAuBA,EAAoB,SAAS,KAAKI,KACxD9L,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,SAAS,MAAM;AACb,oBAAA8L,EAAA,GACAG,EAAY,CAAAO,MAAQA,EAAK,OAAO,CAAAC,MAAO,CAACA,EAAI,GAAG,WAAW,OAAO,CAAC,CAAC;AAAA,kBAAA;AAAA,kBAErE,WAAU;AAAA,kBACV,cAAW;AAAA,kBACX,OAAM;AAAA,kBAEN,UAAAzM,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,uLAAsL,EAAA,CAC7P;AAAA,gBAAA;AAAA,cAAA;AAAA,cAIJA,gBAAAA,EAAAA;AAAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,SAAS0M;AAAA,kBACT,WAAU;AAAA,kBACV,cAAW;AAAA,kBAEX,UAAA1M,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,EAAA,CAC9F;AAAA,gBAAA;AAAA,cAAA;AAAA,YACF,EAAA,CACF;AAAA,UAAA,GACF;AAAA,UAGAA,gBAAAA,EAAAA;AAAAA,YAACgL;AAAA,YAAA;AAAA,cACC,UAAAC;AAAA,cACA,QAAQ;AAAA,gBACN,GAAGhT;AAAA,gBACH,gBAAA2T;AAAA,cAAA;AAAA,cAEF,OAAAjK;AAAA,cACA,WAAAuJ;AAAA,cACA,eAAeU,IAAiBe,IAAoB,MAAM;AAAA,cAAA;AAAA,cAC1D,uBAAAtG;AAAA,cACA,WAAU;AAAA,YAAA;AAAA,UAAA;AAAA,QACZ,EAAA,CACF,EAAA,CAEJ;AAAA,QAGC,CAACgG,KACA7I,gBAAAA,EAAAA;AAAAA,UAAC;AAAA,UAAA;AAAA,YACC,SAASkJ;AAAA,YACT,WAAWrQ;AAAA,cACT;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,YAEF,cAAW;AAAA,YAEX,UAAA;AAAA,cAAA2D,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,iKAAgK,EAAA,CACvO;AAAA,cAGC,CAACmM,KACAnM,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,yEAAA,CAAyE;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA,QAM7FqM,KACCrM,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,6HAA4H,UAAA,oBAAA,CAE3I;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GC5PaiN,KAA0D,CAAC;AAAA,EACtE,OAAAnF;AAAA,EACA,OAAAnG;AAAA,EACA,aAAA4D,IAAc;AAAA,EACd,aAAA2H,IAAc;AAAA,EACd,UAAAzB;AAAA,EACA,UAAA0B;AAAA,EACA,YAAAC,IAAa;AAAA,EACb,WAAA/N;AACF,MAAM;AACJ,QAAM,CAACgO,GAAaC,CAAc,IAAInQ,EAAS,EAAE,GAC3C,CAACoQ,GAAiBC,CAAkB,IAAIrQ,EAAS,EAAK,GACtD,CAACsQ,GAAUC,CAAW,IAAIvQ,EAAS,EAAK;AAG9C,EAAAuC,EAAU,MAAM;AACd,UAAMiO,IAAc,MAAM;AACxB,MAAAD,EAAY,OAAO,aAAa,GAAG;AAAA,IAAA;AAGrC,WAAAC,EAAA,GACA,OAAO,iBAAiB,UAAUA,CAAW,GACtC,MAAM,OAAO,oBAAoB,UAAUA,CAAW;AAAA,EAAA,GAC5D,EAAE;AAEL,QAAMC,IAAqB,CAAChK,MAA2C;AACrE,UAAMvK,IAAQuK,EAAE,OAAO;AACvB,IAAA0J,EAAejU,CAAK,GACpB8T,KAAA,QAAAA,EAAW9T;AAAA,EAAK,GAGZwU,IAAoB,MAAM;AAC9B,IAAAP,EAAe,EAAE,GACjBH,KAAA,QAAAA,EAAW;AAAA,EAAE,GAGTW,IAAgBzR;AAAA,IACpB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE6B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWsK;AAAA,MACX,OAAOvJ;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,0CACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,mEACX,UAAA8H,GACH;AAAA,UAEAtE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BAEZ,UAAA;AAAA,YAAAiK,KAAYhC,KACXzL,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAASyL;AAAA,gBACT,WAAU;AAAA,gBACV,OAAM;AAAA,gBAEN,UAAAzL,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,MAAK;AAAA,oBACL,QAAO;AAAA,oBACP,SAAQ;AAAA,oBAER,UAAAA,gBAAAA,EAAAA,IAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,uBAAA,CAAuB;AAAA,kBAAA;AAAA,gBAAA;AAAA,cAC9F;AAAA,YAAA;AAAA,YAKHuF,KACCvF,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAASyL;AAAA,gBACT,WAAU;AAAA,gBACV,OAAOyB,IAAc,mBAAmB;AAAA,gBAExC,UAAAlN,gBAAAA,EAAAA;AAAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAW3D;AAAA,sBACT;AAAA,sBACA,EAAE,cAAc6Q,EAAA;AAAA,oBAAY;AAAA,oBAE9B,MAAK;AAAA,oBACL,QAAO;AAAA,oBACP,SAAQ;AAAA,oBAER,UAAAlN,gBAAAA,EAAAA,IAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,kBAAA,CAAkB;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACzF;AAAA,YAAA;AAAA,UACF,EAAA,CAEJ;AAAA,QAAA,GACF;AAAA,QAGCoN,KAAc,CAACF,KACd1J,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,YACb,UAAA;AAAA,UAAAA,gBAAAA,OAAC,SAAI,WAAWnH;AAAA,YACd;AAAA,YACA;AAAA,cACE,2CAA2CkR;AAAA,YAAA;AAAA,UAC7C,GAGA,UAAA;AAAA,YAAAvN,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,uCACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,8CAAA,CAA8C,EAAA,CACrH,EAAA,CACF;AAAA,YAGAA,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,OAAOqN;AAAA,gBACP,UAAUO;AAAA,gBACV,SAAS,MAAMJ,EAAmB,EAAI;AAAA,gBACtC,QAAQ,MAAMA,EAAmB,EAAK;AAAA,gBACtC,aAAY;AAAA,gBACZ,WAAWnR;AAAA,kBACT;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBAAA;AAAA,cACF;AAAA,YAAA;AAAA,YAIDgR,KACCrN,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAS6N;AAAA,gBACT,WAAU;AAAA,gBACV,OAAM;AAAA,gBAEN,UAAA7N,gBAAAA,EAAAA,IAAC,SAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,EAAA,CAC9F;AAAA,cAAA;AAAA,YAAA;AAAA,UACF,GAEJ;AAAA,UAGCqN,KACCrN,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iDAAgD,UAAA,+CAAA,CAE/D;AAAA,QAAA,GAEJ;AAAA,QAID,CAACkN,KACA1J,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,yEACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,oCAAA,CAAoC;AAAA,YACnDA,gBAAAA,EAAAA,IAAC,UAAK,UAAA,uBAAA,CAAoB;AAAA,UAAA,GAC5B;AAAA,UACAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,8BAA6B,EAAA,CACpG;AAAA,YACAA,gBAAAA,EAAAA,IAAC,UAAK,UAAA,aAAA,CAAU;AAAA,UAAA,EAAA,CAClB;AAAA,QAAA,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR,GC1Ka+N,KAA4D,CAAC;AAAA,EACxE,iBAAAxM;AAAA,EACA,aAAAyM;AAAA,EACA,OAAArM;AAAA,EACA,oBAAAqH;AAAA,EACA,uBAAA3C;AAAA,EACA,WAAAhH;AACF,MAAM;AACJ,QAAM,CAAC4O,GAAaC,CAAc,IAAI/Q,EAAS,EAAK,GAC9C,CAACgR,GAAWC,CAAY,IAAIjR,EAAmC,KAAK,GAEpE8L,IAAyBD,IAC3BzH,EAAgB,MAAM,GAAGyH,CAAkB,IAC3CzH,GAeE8M,KAbwB,MAAM;AAClC,YAAQF,GAAA;AAAA,MACN,KAAK;AACH,eAAOlF,EACJ,OAAO,CAAAzH,MAAOA,EAAI,sBAAsB,GAAG,EAC3C,MAAM,GAAG,CAAC;AAAA,MACf,KAAK;AACH,eAAOyH,EAAuB,MAAM,GAAG,CAAC;AAAA,MAC1C;AACE,eAAOA;AAAA,IAAA;AAAA,EACX,GAGyB,GAErBqF,IAAiBjS;AAAA,IACrB;AAAA,IACA;AAAA,IACAgD;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD,QAErB4M,IAAwB,MAAM;AAClC,QAAIF,EAAmB,WAAW;AAChC,aACE7K,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,oEACb,UAAA;AAAA,QAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,8FACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,8CAAA,CAA8C,EAAA,CACrH,EAAA,CACF;AAAA,QACAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,6DAA4D,UAAA,4BAE1E;AAAA,QACAA,gBAAAA,EAAAA,IAAC,KAAA,EAAE,WAAU,4CAA2C,UAAA,uCAAA,CAExD;AAAA,MAAA,GACF;AAIJ,YAAQgO,GAAA;AAAA,MACN,KAAK;AACH,eACEhO,gBAAAA,MAAC,SAAI,WAAU,aACZ,YAAmB,IAAI,CAACjB,GAAgB0F,MACvCzE,gBAAAA,EAAAA;AAAAA,UAACgG;AAAA,UAAA;AAAA,YAEC,gBAAAjH;AAAA,YACA,OAAA4C;AAAA,YACA,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS0E;AAAA,UAAA;AAAA,UALJtH,EAAe,SAAS0F;AAAA,QAAA,CAOhC,GACH;AAAA,MAGJ,KAAK;AACH,eACEzE,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,aACZ,UAAAqO,EAAmB,IAAI,CAACtP,GAAgB0F,MACvCjB,gBAAAA,OAAC,OAAA,EAAwC,WAAU,qGACjD,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iDAAA,CAAiD;AAAA,UAChEwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,iEACZ,UAAAjB,EAAe,OAClB;AAAA,YACAyE,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,4CACZ,UAAA;AAAA,cAAA,KAAK,MAAMzE,EAAe,qBAAqB,GAAG;AAAA,cAAE;AAAA,YAAA,EAAA,CACvD;AAAA,UAAA,EAAA,CACF;AAAA,QAAA,EAAA,GATQA,EAAe,SAAS0F,CAUlC,CACD,EAAA,CACH;AAAA,MAGJ,KAAK;AACH,qCACG,OAAA,EAAI,WAAU,aACZ,UAAA4J,EAAmB,MAAM,GAAG,CAAC,EAAE,IAAI,CAACtP,GAAgB0F,MACnDjB,gBAAAA,EAAAA,KAAC,OAAA,EAAwC,WAAU,YACjD,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA;AAAAA,YAACgG;AAAA,YAAA;AAAA,cACC,gBAAAjH;AAAA,cACA,OAAA4C;AAAA,cACA,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,SAAS0E;AAAA,YAAA;AAAA,UAAA;AAAA,gCAEV,UAAA,EAAO,WAAU,uGAChB,UAAArG,gBAAAA,EAAAA,IAAC,SAAI,WAAU,2BAA0B,MAAK,gBAAe,SAAQ,aACnE,UAAAA,gBAAAA,EAAAA,IAAC,UAAK,GAAE,gGAA+F,GACzG,EAAA,CACF;AAAA,QAAA,EAAA,GAZQjB,EAAe,SAAS0F,CAalC,CACD,EAAA,CACH;AAAA,MAGJ,KAAK;AACH,eACEjB,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,aAEZ,UAAA;AAAA,UAAA6K,EAAmB,CAAC,KACnB7K,gBAAAA,EAAAA,KAAC,OAAA,EACC,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,qFAAoF,UAAA,YAElG;AAAA,YACAA,gBAAAA,EAAAA;AAAAA,cAAC0B;AAAA,cAAA;AAAA,gBACC,gBAAgB2M,EAAmB,CAAC;AAAA,gBACpC,OAAA1M;AAAA,gBACA,gBAAgB;AAAA,gBAChB,YAAY;AAAA,gBACZ,SAAS0E;AAAA,gBACT,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UACZ,GACF;AAAA,UAIDgI,EAAmB,MAAM,CAAC,EAAE,SAAS,4BACnC,OAAA,EACC,UAAA;AAAA,YAAArO,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,qFAAoF,UAAA,gBAElG;AAAA,YACAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,aACZ,UAAAqO,EAAmB,MAAM,GAAG,CAAC,EAAE,IAAI,CAACtP,GAAgB0F,MACnDzE,gBAAAA,EAAAA;AAAAA,cAACgG;AAAA,cAAA;AAAA,gBAEC,gBAAAjH;AAAA,gBACA,OAAA4C;AAAA,gBACA,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,SAAS0E;AAAA,cAAA;AAAA,cALJtH,EAAe,SAAS0F;AAAA,YAAA,CAOhC,EAAA,CACH;AAAA,UAAA,EAAA,CACF;AAAA,QAAA,GAEJ;AAAA,MAGJ;AACE,eACEzE,gBAAAA,MAAC,SAAI,WAAU,aACZ,YAAmB,IAAI,CAACjB,GAAgB0F,MACvCzE,gBAAAA,EAAAA;AAAAA,UAACgG;AAAA,UAAA;AAAA,YAEC,gBAAAjH;AAAA,YACA,OAAA4C;AAAA,YACA,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS0E;AAAA,UAAA;AAAA,UALJtH,EAAe,SAAS0F;AAAA,QAAA,CAOhC,GACH;AAAA,IAAA;AAAA,EAEN;AAGF,SACEjB,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW8K;AAAA,MACX,OAAO/J;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAG1B,UAAA;AAAA,QAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,kFACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS,MAAM4K,EAAa,KAAK;AAAA,cACjC,WAAW/R;AAAA,gBACT;AAAA,gBACA8R,MAAc,QACV,qFACA;AAAA,cAAA;AAAA,cAEP,UAAA;AAAA,gBAAA;AAAA,gBACO5M,EAAgB;AAAA,gBAAO;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,UAE/BvB,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS,MAAMoO,EAAa,KAAK;AAAA,cACjC,WAAW/R;AAAA,gBACT;AAAA,gBACA8R,MAAc,QACV,qFACA;AAAA,cAAA;AAAA,cAEP,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,UAGDnO,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS,MAAMoO,EAAa,QAAQ;AAAA,cACpC,WAAW/R;AAAA,gBACT;AAAA,gBACA8R,MAAc,WACV,qFACA;AAAA,cAAA;AAAA,cAEP,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,GACF;AAAA,QAGAnO,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sCAAqC,OAAO;AAAA,UACzD,yBAAyB;AAAA;AAAA,UACzB,oBAAoB;AAAA;AAAA,QAAA,GAEnB,eACH;AAAA,8BAGC,OAAA,EAAI,WAAU,mFACb,UAAAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,6CACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,oCACb,UAAA;AAAA,YAAA6K,EAAmB;AAAA,YAAO;AAAA,YAAgBA,EAAmB,WAAW,IAAI,MAAM;AAAA,UAAA,GACrF;AAAA,UACArO,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS,MAAMkO,EAAe,CAACD,CAAW;AAAA,cAC1C,WAAU;AAAA,cACX,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,EAAA,CACF,EAAA,CACF;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGN,GCrPaO,KAA8C,CAAC;AAAA,EAC1D,iBAAAjN;AAAA,EACA,QAAAtJ;AAAA,EACA,OAAA0J;AAAA,EACA,OAAAmG,IAAQ;AAAA,EACR,QAAAuE,IAAS;AAAA,EACT,UAAAZ;AAAA,EACA,uBAAApF;AAAA,EACA,UAAA8G;AAAA;AAAA,EAEA,WAAA9N;AAAA,EACA,eAAAoP,IAAgB;AAAA;AAClB,MAAM;AACJ,QAAM,CAACvB,GAAawB,CAAc,IAAIvR,EAASlF,EAAO,oBAAoB,EAAK,GACzE,CAACoV,GAAaC,CAAc,IAAInQ,EAAS,EAAE,GAC3C,CAACwR,CAAO,IAAIxR,EAAyB,EAAE,GACvC,CAACsQ,GAAUC,CAAW,IAAIvQ,EAAS,EAAK;AAG9C,EAAAuC,EAAU,MAAM;AACd,UAAMiO,IAAc,MAAM;AACxB,MAAAD,EAAY,OAAO,aAAa,GAAG;AAAA,IAAA;AAGrC,WAAAC,EAAA,GACA,OAAO,iBAAiB,UAAUA,CAAW,GACtC,MAAM,OAAO,oBAAoB,UAAUA,CAAW;AAAA,EAAA,GAC5D,EAAE,GAGLjO,EAAU,MAAM;AACd,QAAI+N,KAAYpB,KAAU,CAACa,KAAe,CAACuB,GAAe;AACxD,YAAMG,IAAgB,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC7D,sBAAS,KAAK,MAAM,WAAW,UAC/B,SAAS,KAAK,MAAM,WAAW,SAC/B,SAAS,KAAK,MAAM,QAAQ,QAErB,MAAM;AACX,iBAAS,KAAK,MAAM,WAAWA,GAC/B,SAAS,KAAK,MAAM,WAAW,IAC/B,SAAS,KAAK,MAAM,QAAQ;AAAA,MAAA;AAAA,IAC9B;AAAA,EACF,GACC,CAACnB,GAAUpB,GAAQa,GAAauB,CAAa,CAAC,GAGjD/O,EAAU,MAAM;AACd,QAAIzH,EAAO,eAAeA,EAAO,iBAAiB;AAChD,YAAM4W,IAAW,YAAY,MAAM;AAEjC,gBAAQ,IAAI,oCAAoC;AAAA,MAAA,GAC/C5W,EAAO,eAAe;AAEzB,aAAO,MAAM,cAAc4W,CAAQ;AAAA,IAAA;AAAA,EACrC,GACC,CAAC5W,EAAO,aAAaA,EAAO,eAAe,CAAC;AAG/C,QAAM6W,IAA0BvR,GAAQ,MAAM;AAC5C,QAAIiN,IAAW,CAAC,GAAGjJ,CAAe;AAGlC,QAAI8L,EAAY,QAAQ;AACtB,YAAM0B,IAAQ1B,EAAY,YAAA;AAC1B,MAAA7C,IAAWA,EAAS;AAAA,QAAO,CAAAhJ,MAAA;;AACzB,iBAAAA,EAAI,MAAM,YAAA,EAAc,SAASuN,CAAK,KACtCvN,EAAI,OAAO,YAAA,EAAc,SAASuN,CAAK,OACvC3M,IAAAZ,EAAI,aAAJ,gBAAAY,EAAc,KAAK,CAAAC,MAAWA,EAAQ,YAAA,EAAc,SAAS0M,CAAK;AAAA;AAAA,MAAC;AAAA,IACrE;AAIF,WAAIJ,EAAQ,cAAcA,EAAQ,WAAW,SAAS,MACpDnE,IAAWA,EAAS;AAAA,MAAO,CAAAhJ,MAAA;;AACzB,gBAAAY,IAAAZ,EAAI,eAAJ,gBAAAY,EAAgB,KAAK;;AAAO,kBAAAA,IAAAuM,EAAQ,eAAR,gBAAAvM,EAAoB,SAAS4M;AAAA;AAAA;AAAA,IAAI,IAK7DL,EAAQ,aAORA,EAAQ,aACVnE,IAAWA,EAAS,OAAO,CAAAhJ,MAAOA,EAAI,cAAcA,EAAI,aAAa,CAAC,IAIpEmN,EAAQ,kBAAkB,WAC5BnE,IAAWA,EAAS,OAAO,CAAAhJ,MAAOA,EAAI,sBAAsBmN,EAAQ,aAAc,IAIpFnE,EAAS,KAAK,CAAChE,GAAGC,MAAMA,EAAE,qBAAqBD,EAAE,kBAAkB,GAG/DvO,EAAO,uBACTuS,IAAWA,EAAS,MAAM,GAAGvS,EAAO,kBAAkB,IAGjDuS;AAAA,EAAA,GACN,CAACjJ,GAAiB8L,GAAasB,GAAS1W,EAAO,kBAAkB,CAAC,GAE/DyU,IAAe,MAAM;AACzB,IAAIzU,EAAO,gBACTyW,EAAe,CAACxB,CAAW,GAC3BzB,KAAA,QAAAA;AAAA,EACF,GAGIwD,IAAe,CAACF,MAAkB;AACtC,IAAAzB,EAAeyB,CAAK,GACpB5B,KAAA,QAAAA,EAAW4B;AAAA,EAAK,GAsBZG,IAAiB7S;AAAA,IACrB;AAAA,IACA;AAAA,KAfsB,MAAM;AAC5B,UAAI6Q,EAAa,QAAO;AAGxB,cAAQjV,EAAO,MAAA;AAAA,QACb,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,QAClB;AAAS,iBAAO;AAAA,MAAA;AAAA,IAClB,GAMA;AAAA,IACA;AAAA,MACE,YAAYA,EAAO,aAAa;AAAA,MAChC,YAAYA,EAAO,aAAa;AAAA;AAAA;AAAA,MAGhC,iCAAiC,CAACwW;AAAA,MAClC,mBAAmBA;AAAA,MACnB,UAAUxW,EAAO,aAAa,UAAU,CAACwW;AAAA,MACzC,WAAWxW,EAAO,aAAa,WAAW,CAACwW;AAAA;AAAA,MAE3C,+BAA+BxW,EAAO,aAAa,UAAU,CAACoU,KAAU,CAACoC;AAAA,MACzE,8BAA8BxW,EAAO,aAAa,WAAW,CAACoU,KAAU,CAACoC;AAAA;AAAA,MAEzE,WAAW;AAAA;AAAA,MACX,mBAAmB,CAACA;AAAA;AAAA,IAAA;AAAA,IAEtBpP;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SAAI,CAAC0K,KAAU,CAACpU,EAAO,cACd,OAIPuL,gBAAAA,EAAAA,KAAAG,YAAA,EAEG,UAAA;AAAA,IAAA0I,KAAU,CAACa,KACVlN,gBAAAA,EAAAA;AAAAA,MAAC;AAAA,MAAA;AAAA,QACC,WAAW3D;AAAA,UACT;AAAA,UACAoS,IAAgB,qBAAqB;AAAA,QAAA;AAAA,QAEvC,SAAS,MAAMhD,KAAA,gBAAAA;AAAA,QACf,OAAO;AAAA;AAAA,UAEL,UAAUgD,IAAgB,aAAa;AAAA,UACvC,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,aAAa;AAAA;AAAA,QAAA;AAAA,MACf;AAAA,IAAA;AAAA,IAKJjL,gBAAAA,EAAAA;AAAAA,MAAC;AAAA,MAAA;AAAA,QACC,WAAW0L;AAAA,QACX,OAAO3K;AAAA,QACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,QAC1B,yBAAuB1J,EAAO;AAAA,QAC9B,qBAAmBA,EAAO;AAAA,QAC1B,oBAAkBwV,KAAYpB,KAAU,CAACa,IAAc,SAAS;AAAA,QAChE,uBAAqBuB,IAAgB,SAAS;AAAA,QAG7C,UAAA;AAAA,UAAAxW,EAAO,eAAe,MACrB+H,gBAAAA,EAAAA;AAAAA,YAACiN;AAAA,YAAA;AAAA,cACC,OAAAnF;AAAA,cACA,OAAAnG;AAAA,cACA,aAAa1J,EAAO;AAAA,cACpB,aAAAiV;AAAA,cACA,UAAUR;AAAA,cACV,UAAUzU,EAAO,aAAagX,IAAe;AAAA,cAC7C,YAAYhX,EAAO,cAAc,CAACiV;AAAA,YAAA;AAAA,UAAA;AAAA,UAKrC,CAACA,KACAlN,gBAAAA,EAAAA;AAAAA,YAAC+N;AAAA,YAAA;AAAA,cACC,iBAAiBe;AAAA,cACjB,aAAa7W,EAAO;AAAA,cACpB,OAAA0J;AAAA,cACA,oBAAoB1J,EAAO;AAAA,cAC3B,uBAAAoO;AAAA,cACA,WAAU;AAAA,YAAA;AAAA,UAAA;AAAA,UAKb6G,KAAejV,EAAO,eACrBuL,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,wDACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA;AAAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAS0M;AAAA,gBACT,WAAU;AAAA,gBACV,OAAM;AAAA,gBAEN,UAAA1M,gBAAAA,EAAAA,IAAC,SAAI,WAAU,4CAA2C,MAAK,QAAO,QAAO,gBAAe,SAAQ,aAClG,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,gBAAe,EAAA,CACtF;AAAA,cAAA;AAAA,YAAA;AAAA,YAEFA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wFACZ,YAAwB,OAAA,CAC3B;AAAA,UAAA,GACF;AAAA,UAID,CAACkN,KACAlN,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,sDACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,wDAAuD,UAAA,oBAAA,CAEtE,EAAA,CACF;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAEJ,GACF;AAEJ,GCjPamP,KAAgF,CAAC;AAAA,EAC5F,iBAAA5N;AAAA,EACA,SAAA6N;AAAA,EACA,OAAAzN;AAAA,EACA,OAAAmG,IAAQ;AAAA,EACR,UAAAuH,IAAW;AAAA,EACX,MAAAvK,IAAO;AAAA,EACP,UAAAwK,IAAW;AAAA,EACX,WAAAC,IAAY;AAAA,EACZ,uBAAAlJ;AAAA,EACA,WAAAqC;AAAA,EACA,WAAArJ;AACF,MAAM;AACJ,QAAM,CAACsJ,GAAWC,CAAY,IAAIzL,EAAS,EAAK,GAC1C,CAAC0L,GAAaC,CAAc,IAAI3L,EAAS,EAAK;AAGpD,EAAAuC,EAAU,MAAM;AACd,QAAI4P,KAAY/N,EAAgB,SAAS,GAAG;AAC1C,YAAMwH,IAAQ,WAAW,MAAM;AAC7B,QAAAH,EAAa,EAAI,GACjBE,EAAe,EAAI;AAAA,MAAA,GAClByG,CAAS;AAEZ,aAAO,MAAM,aAAaxG,CAAK;AAAA,IAAA;AAAA,EACjC,GACC,CAACuG,GAAU/N,EAAgB,QAAQgO,CAAS,CAAC;AAEhD,QAAMpG,IAAgB,MAAM;AAC1B,IAAAP,EAAa,EAAK,GAClBF,KAAA,QAAAA;AAAA,EAAY,GAIR8G,IAAsB,MAAM;AAChC,YAAQ1K,GAAA;AAAA,MACN,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB;AAAS,eAAO;AAAA,IAAA;AAAA,EAClB,GAII2K,IAAqB,MAAM;AAC/B,YAAQJ,GAAA;AAAA,MACN,KAAK;AAAgB,eAAO;AAAA,MAC5B,KAAK;AAAe,eAAO;AAAA,MAC3B,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAY,eAAO;AAAA,MACxB;AAAS,eAAO;AAAA,IAAA;AAAA,EAClB;AAGF,MAAI,CAAC1G,KAAapH,EAAgB,WAAW;AAC3C,WAAO;AAGT,QAAM+C,IAAmBjI;AAAA,IACvB;AAAA,IACA;AAAA,IACAoT,EAAA;AAAA,IACAD,EAAA;AAAA,IACA;AAAA,MACE,oCAAoC,CAAC3G;AAAA,MACrC,uCAAuCA;AAAA,IAAA;AAAA,IAEzCxJ;AAAA,EAAA,GAGIkF,IAAiB5C,KAAA,QAAAA,EAAO,cAAc;AAAA,IAC1C,oBAAoBA,EAAM;AAAA,EAAA,IACD;AAE3B,SACE3B,gBAAAA,EAAAA;AAAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWsE;AAAA,MACX,OAAOC;AAAA,MACP,qBAAmB5C,KAAA,gBAAAA,EAAO;AAAA,MAE1B,UAAA6B,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,iHAEb,UAAA;AAAA,QAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,iGACb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,gFACb,UAAAA,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,UAAAA,gBAAAA,EAAAA,IAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,6BAAA,CAA6B,EAAA,CACpG,EAAA,CACF;AAAA,mCACC,OAAA,EACC,UAAA;AAAA,cAAAA,gBAAAA,EAAAA,IAAC,MAAA,EAAG,WAAU,yBAAyB,UAAA8H,GAAM;AAAA,cAC5CsH,KACC5L,gBAAAA,EAAAA,KAAC,KAAA,EAAE,WAAU,2CAA0C,UAAA;AAAA,gBAAA;AAAA,gBACzC4L;AAAA,gBAAQ;AAAA,cAAA,EAAA,CACtB;AAAA,YAAA,EAAA,CAEJ;AAAA,UAAA,GACF;AAAA,UACApP,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAASmJ;AAAA,cACT,WAAU;AAAA,cACV,cAAW;AAAA,cAEX,UAAAnJ,gBAAAA,EAAAA,IAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,aACjE,gCAAC,QAAA,EAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,EAAA,CAC9F;AAAA,YAAA;AAAA,UAAA;AAAA,QACF,GACF;AAAA,QAGAwD,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,uGAEb,UAAA;AAAA,UAAAA,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,gCACb,UAAA;AAAA,YAAAxD,gBAAAA,EAAAA,IAAC,OAAA,EAAI,WAAU,kDAAA,CAAkD;AAAA,YACjEwD,gBAAAA,EAAAA,KAAC,QAAA,EAAK,WAAU,wDACb,UAAA;AAAA,cAAAjC,EAAgB;AAAA,cAAO;AAAA,cAAmBA,EAAgB,SAAS,IAAI,OAAO;AAAA,cAAG;AAAA,YAAA,EAAA,CACpF;AAAA,UAAA,GACF;AAAA,UAGAvB,gBAAAA,EAAAA;AAAAA,YAACwI;AAAA,YAAA;AAAA,cACC,iBAAAjH;AAAA,cACA,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,SAAS;AAAA,gBACT,oBAAoB;AAAA,gBACpB,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,SAAS;AAAA,cAAA;AAAA,cAEX,OAAAI;AAAA,cACA,uBAAA0E;AAAA,YAAA;AAAA,UAAA;AAAA,QACF,GACF;AAAA,8BAGC,OAAA,EAAI,WAAU,yFACb,UAAA7C,gBAAAA,EAAAA,KAAC,OAAA,EAAI,WAAU,qCACb,UAAA;AAAA,UAAAxD,gBAAAA,EAAAA,IAAC,QAAA,EAAK,WAAU,4CAA2C,UAAA,qBAE3D;AAAA,UACAA,gBAAAA,EAAAA;AAAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAASmJ;AAAA,cACT,WAAU;AAAA,cACX,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,EAAA,CACF,EAAA,CACF;AAAA,MAAA,EAAA,CACF;AAAA,IAAA;AAAA,EAAA;AAGN,GClKauG,IAAoB,CAACC,IAAoC,OAAoB;AACxF,QAAMC,IAAyB;AAAA,IAC7B,MAAM;AAAA,IACN,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,YAAY;AAAA,IACZ,UAAU;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IAAA;AAAA,IAET,cAAc;AAAA,IACd,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA;AAAA,IAET,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA;AAAA,IAET,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,IAAA;AAAA,EACb;AAGF,SAAO;AAAA,IACL,GAAGA;AAAA,IACH,GAAGD;AAAA,IACH,UAAU;AAAA,MACR,GAAGC,EAAU;AAAA,MACb,GAAGD,EAAY;AAAA,IAAA;AAAA,IAEjB,SAAS;AAAA,MACP,GAAGC,EAAU;AAAA,MACb,GAAGD,EAAY;AAAA,IAAA;AAAA,IAEjB,SAAS;AAAA,MACP,GAAGC,EAAU;AAAA,MACb,GAAGD,EAAY;AAAA,IAAA;AAAA,IAEjB,OAAO;AAAA,MACL,GAAGC,EAAU;AAAA,MACb,GAAGD,EAAY;AAAA,IAAA;AAAA,IAEjB,YAAY;AAAA,MACV,GAAGC,EAAU;AAAA,MACb,GAAGD,EAAY;AAAA,IAAA;AAAA,EACjB;AAEJ,GAKaE,KAAkB,CAACF,IAAoC,OAe3DD,EAAkB;AAAA,EACvB,GAfyC;AAAA,IACzC,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA;AAAA,EACT;AAAA,EAKA,GAAGC;AAAA,CACJ,GAMUG,KAAe;AAAA;AAAA,EAE1B,SAASJ,EAAkB;AAAA,IACzB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA;AAAA,EACT,CACD;AAAA;AAAA,EAGD,SAASA,EAAkB;AAAA,IACzB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,WAAW;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,IAAA;AAAA,EACV,CACD;AAAA;AAAA,EAGD,WAAWA,EAAkB;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,EAAA,CACb;AAAA;AAAA,EAGD,cAAcA,EAAkB;AAAA,IAC9B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,SAAS;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IAAA;AAAA,EACT,CACD;AACH,GAKaK,KAAc,IAAIC,MAAgD;AAC7E,QAAMJ,IAAYF,EAAA;AAClB,SAAOM,EAAO,OAAO,CAACC,GAAQtO,MACvBA,IACE+N,EAAkB;AAAA,IACvB,GAAGO;AAAA,IACH,GAAGtO;AAAA,EAAA,CACJ,IAJkBsO,GAKlBL,CAAS;AACd,GAKaM,KAAyB,CAACC,MAA+C;;AACpF,QAAMC,IAAgB,iBAAiBD,CAAO;AAE9C,SAAO;AAAA,IACL,gBAAc/N,IAAAgO,EAAc,iBAAiB,wBAAwB,MAAvD,gBAAAhO,EAA0D,WAAU;AAAA,IAClF,kBAAgBW,IAAAqN,EAAc,iBAAiB,0BAA0B,MAAzD,gBAAArN,EAA4D,WAAU;AAAA,IACtF,mBAAiBC,IAAAoN,EAAc,iBAAiB,2BAA2B,MAA1D,gBAAApN,EAA6D,WAAU;AAAA,IACxF,aAAWC,IAAAmN,EAAc,iBAAiB,qBAAqB,MAApD,gBAAAnN,EAAuD,WAAU;AAAA,IAC5E,gBAAcC,IAAAkN,EAAc,iBAAiB,wBAAwB,MAAvD,gBAAAlN,EAA0D,WAAU;AAAA,IAClF,cAAYC,IAAAiN,EAAc,iBAAiB,sBAAsB,MAArD,gBAAAjN,EAAwD,WAAU;AAAA,EAAA;AAElF,GC5EakN,KAAU,SAGVC,KAAiB;AAAA,EAC5B,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,OAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,EAAA;AAEjB;", "x_google_ignoreList": [0, 1, 2, 3]}