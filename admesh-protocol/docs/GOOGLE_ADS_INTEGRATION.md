# Google Ads Integration for AdMesh

This document describes the Google Ads integration for the AdMesh platform, allowing brands to automatically create and manage Google Ads campaigns from their AdMesh offers.

## Overview

The Google Ads integration enables brands to:
- Connect their Google Ads accounts to AdMesh
- Automatically create Google Ads campaigns from existing offers
- Manage campaign status (pause/resume)
- Track campaign performance metrics
- Sync budgets and targeting with AdMesh offers

## Architecture

### Components

1. **GoogleAdsService** (`api/services/google_ads_service.py`)
   - Handles Google Ads API operations
   - Manages authentication and client initialization
   - Provides methods for campaign, ad group, and keyword management

2. **Google Ads API Routes** (`api/routes/google_ads.py`)
   - REST API endpoints for Google Ads operations
   - Authentication and authorization
   - Request/response validation

3. **Dashboard UI** (`admesh-dashboard/src/app/dashboard/brand/google-ads/`)
   - Brand interface for Google Ads management
   - Account validation and setup
   - Campaign performance monitoring

4. **Offer Integration** (`api/routes/brands.py`)
   - Extended offer creation to include Google Ads settings
   - Automatic campaign creation during offer setup

## Setup and Configuration

### Prerequisites

1. **Google Ads API Access**
   - Google Ads Developer Token
   - OAuth 2.0 credentials (Client ID, Client Secret)
   - Refresh Token for authentication

2. **Environment Variables**
   ```bash
   GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token
   GOOGLE_ADS_CLIENT_ID=your_client_id
   GOOGLE_ADS_CLIENT_SECRET=your_client_secret
   GOOGLE_ADS_REFRESH_TOKEN=your_refresh_token
   GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_login_customer_id
   ```

### Installation

1. **Install Dependencies**
   ```bash
   pip install google-ads==25.0.0
   ```

2. **Update Configuration**
   The Google Ads configuration is automatically loaded from environment variables through the base configuration class.

## API Endpoints

### Account Management

#### Validate Google Ads Account
```http
POST /google-ads/validate-account
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer_id": "123-456-7890"
}
```

### Campaign Management

#### Create Campaign
```http
POST /google-ads/campaigns/create
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer_id": "123-456-7890",
  "campaign_name": "My Campaign",
  "daily_budget_micros": ********,
  "target_url": "https://example.com",
  "keywords": ["keyword1", "keyword2"],
  "ad_headlines": ["Headline 1", "Headline 2", "Headline 3"],
  "ad_descriptions": ["Description 1", "Description 2"]
}
```

#### Create Campaign from Offer
```http
POST /google-ads/campaigns/create-from-offer
Content-Type: application/json
Authorization: Bearer <token>

{
  "offer_id": "offer_uuid",
  "customer_id": "123-456-7890",
  "daily_budget_micros": ********,
  "custom_keywords": ["optional", "custom", "keywords"],
  "custom_headlines": ["Optional Custom Headline"],
  "custom_descriptions": ["Optional Custom Description"]
}
```

#### List Campaigns
```http
GET /google-ads/campaigns
Authorization: Bearer <token>
```

#### Update Campaign Status
```http
POST /google-ads/campaigns/{campaign_id}/update-status
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "ENABLED"  // or "PAUSED", "REMOVED"
}
```

### Performance Tracking

#### Get Account Performance
```http
GET /google-ads/performance/account/{customer_id}?date_range=LAST_30_DAYS
Authorization: Bearer <token>
```

#### Get Campaigns Performance
```http
GET /google-ads/performance/campaigns/{customer_id}?date_range=LAST_30_DAYS
Authorization: Bearer <token>
```

#### Get Campaign Performance
```http
GET /google-ads/campaigns/{campaign_id}/performance
Authorization: Bearer <token>
```

## Offer Integration

### Creating Offers with Google Ads

When creating an offer, you can include Google Ads settings:

```json
{
  "product_id": "product_uuid",
  "goal": "signup",
  "payout": {
    "amount": 100,
    "currency": "USD",
    "model": "CPA"
  },
  "offer_total_budget_allocated": 10000,
  "url": "https://example.com/signup",
  "google_ads": {
    "enabled": true,
    "customer_id": "123-456-7890",
    "daily_budget_micros": ********,
    "keywords": ["signup", "registration"],
    "ad_headlines": [
      "Sign Up Today",
      "Join Our Platform",
      "Get Started Now"
    ],
    "ad_descriptions": [
      "Create your account in minutes",
      "Join thousands of satisfied users"
    ]
  }
}
```

### Automatic Campaign Creation

When an offer is created with Google Ads enabled:

1. **Budget Creation**: A campaign budget is created with the specified daily amount
2. **Campaign Creation**: A search campaign is created and initially paused
3. **Ad Group Creation**: An ad group is created within the campaign
4. **Keyword Addition**: Keywords are added to the ad group
5. **Ad Creation**: A responsive search ad is created with the provided headlines and descriptions
6. **Firestore Storage**: Campaign information is stored for tracking and management

## Dashboard Usage

### Account Setup

1. Navigate to **Google Ads** in the brand dashboard
2. Enter your Google Ads Customer ID
3. Click **Validate Account** to verify access
4. Once validated, you can create campaigns

### Campaign Management

1. **View Campaigns**: See all your Google Ads campaigns created through AdMesh
2. **Pause/Resume**: Control campaign status directly from the dashboard
3. **Performance Metrics**: View impressions, clicks, spend, and conversion data
4. **Create from Offers**: Generate campaigns from existing AdMesh offers

### Performance Monitoring

The dashboard provides:
- Account-level performance overview
- Campaign-specific metrics
- Real-time data from Google Ads API
- Cost and conversion tracking

## Data Flow

### Campaign Creation Flow

1. **User Input**: Brand provides Google Ads settings in offer creation
2. **Validation**: System validates Google Ads account access
3. **API Calls**: Sequential Google Ads API calls create campaign structure
4. **Storage**: Campaign data stored in Firestore for tracking
5. **Response**: Campaign ID and status returned to user

### Performance Tracking Flow

1. **API Request**: Dashboard requests performance data
2. **Google Ads Query**: System queries Google Ads API for metrics
3. **Data Processing**: Raw metrics are processed and formatted
4. **Display**: Performance data displayed in dashboard

## Error Handling

### Common Errors

1. **Invalid Customer ID**: Account validation fails
2. **Insufficient Permissions**: API access denied
3. **Budget Constraints**: Daily budget too low
4. **Content Policy**: Ad content violates Google policies

### Error Responses

```json
{
  "success": false,
  "error": "Unable to access the specified Google Ads account",
  "details": "Check customer ID and permissions"
}
```

## Testing

### Unit Tests

Run the Google Ads integration tests:

```bash
python -m pytest tests/test_google_ads_integration.py -v
```

### Integration Tests

Run the integration test script:

```bash
python scripts/test_google_ads_integration.py
```

### Manual Testing

1. **Account Validation**: Test with valid and invalid customer IDs
2. **Campaign Creation**: Create campaigns with various configurations
3. **Performance Tracking**: Verify metrics are correctly retrieved
4. **Error Scenarios**: Test error handling and recovery

## Security Considerations

1. **API Keys**: Store Google Ads credentials securely in environment variables
2. **Access Control**: Verify brand ownership before allowing campaign operations
3. **Rate Limiting**: Respect Google Ads API rate limits
4. **Data Privacy**: Handle customer data according to privacy policies

## Limitations

1. **Campaign Types**: Currently supports Search campaigns only
2. **Ad Formats**: Limited to Responsive Search Ads
3. **Targeting**: Basic keyword targeting only
4. **Automation**: Manual campaign management required

## Future Enhancements

1. **Display Campaigns**: Support for Display and Video campaigns
2. **Advanced Targeting**: Geographic, demographic, and audience targeting
3. **Automated Bidding**: Smart bidding strategies integration
4. **Bulk Operations**: Batch campaign creation and management
5. **Advanced Analytics**: Custom reporting and insights

## Support

For issues with Google Ads integration:

1. Check the logs for detailed error messages
2. Verify Google Ads API credentials and permissions
3. Ensure customer ID has proper access rights
4. Contact support with specific error details

## References

- [Google Ads API Documentation](https://developers.google.com/google-ads/api/docs/start)
- [Google Ads Python Client Library](https://github.com/googleads/google-ads-python)
- [AdMesh API Documentation](https://docs.useadmesh.com/)
