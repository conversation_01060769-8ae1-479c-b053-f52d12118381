#!/usr/bin/env python3
"""
Integration test script for Google Ads functionality
This script tests the Google Ads integration without making actual API calls
"""

import os
import sys
import json
import asyncio
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.services.google_ads_service import GoogleAdsService
from unittest.mock import Mock, patch


class GoogleAdsIntegrationTester:
    """Test class for Google Ads integration"""
    
    def __init__(self):
        self.test_results = []
        self.passed = 0
        self.failed = 0
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        self.test_results.append({
            "test": test_name,
            "status": status,
            "message": message
        })
        
        if passed:
            self.passed += 1
            print(f"✅ {test_name}: {status}")
        else:
            self.failed += 1
            print(f"❌ {test_name}: {status} - {message}")
    
    def test_service_initialization(self):
        """Test GoogleAdsService initialization"""
        try:
            # Mock the configuration
            mock_config = Mock()
            mock_config.google_ads_config = {
                "developer_token": "test_token",
                "client_id": "test_client_id",
                "client_secret": "test_secret",
                "refresh_token": "test_refresh",
                "login_customer_id": "123456789",
                "use_proto_plus": True
            }
            
            with patch('api.services.google_ads_service.get_config', return_value=mock_config):
                service = GoogleAdsService()
                assert service.google_ads_config["developer_token"] == "test_token"
                self.log_test("Service Initialization", True)
        except Exception as e:
            self.log_test("Service Initialization", False, str(e))
    
    def test_campaign_data_structure(self):
        """Test campaign data structure validation"""
        try:
            # Test valid campaign data
            campaign_data = {
                "name": "Test Campaign",
                "budget_resource_name": "customers/123456789/campaignBudgets/987654321"
            }
            
            # Validate required fields
            assert "name" in campaign_data
            assert "budget_resource_name" in campaign_data
            assert len(campaign_data["name"]) > 0
            
            self.log_test("Campaign Data Structure", True)
        except Exception as e:
            self.log_test("Campaign Data Structure", False, str(e))
    
    def test_budget_data_structure(self):
        """Test budget data structure validation"""
        try:
            # Test valid budget data
            budget_data = {
                "name": "Test Budget",
                "amount_micros": 50000000  # $50 in micros
            }
            
            # Validate required fields
            assert "name" in budget_data
            assert "amount_micros" in budget_data
            assert budget_data["amount_micros"] > 0
            assert isinstance(budget_data["amount_micros"], int)
            
            self.log_test("Budget Data Structure", True)
        except Exception as e:
            self.log_test("Budget Data Structure", False, str(e))
    
    def test_ad_data_structure(self):
        """Test ad data structure validation"""
        try:
            # Test valid ad data
            ad_data = {
                "headlines": ["Headline 1", "Headline 2", "Headline 3"],
                "descriptions": ["Description 1", "Description 2"],
                "final_urls": ["https://example.com"]
            }
            
            # Validate required fields
            assert "headlines" in ad_data
            assert "descriptions" in ad_data
            assert "final_urls" in ad_data
            assert len(ad_data["headlines"]) >= 3
            assert len(ad_data["descriptions"]) >= 2
            assert len(ad_data["final_urls"]) >= 1
            
            self.log_test("Ad Data Structure", True)
        except Exception as e:
            self.log_test("Ad Data Structure", False, str(e))
    
    def test_keyword_data_structure(self):
        """Test keyword data structure validation"""
        try:
            # Test valid keywords
            keywords = ["test keyword", "another keyword", "third keyword"]
            
            # Validate keywords
            assert isinstance(keywords, list)
            assert len(keywords) > 0
            assert all(isinstance(keyword, str) for keyword in keywords)
            assert all(len(keyword.strip()) > 0 for keyword in keywords)
            
            self.log_test("Keyword Data Structure", True)
        except Exception as e:
            self.log_test("Keyword Data Structure", False, str(e))
    
    def test_offer_google_ads_integration(self):
        """Test offer integration with Google Ads data"""
        try:
            # Test offer data with Google Ads integration
            offer_data = {
                "product_id": "test_product",
                "goal": "signup",
                "payout": {"amount": 100, "currency": "USD", "model": "CPA"},
                "offer_total_budget_allocated": 10000,
                "url": "https://example.com",
                "google_ads": {
                    "enabled": True,
                    "customer_id": "123456789",
                    "daily_budget_micros": 50000000,
                    "keywords": ["test", "keyword"],
                    "ad_headlines": ["Headline 1", "Headline 2", "Headline 3"],
                    "ad_descriptions": ["Description 1", "Description 2"]
                }
            }
            
            # Validate Google Ads integration
            google_ads = offer_data["google_ads"]
            assert google_ads["enabled"] is True
            assert "customer_id" in google_ads
            assert "daily_budget_micros" in google_ads
            assert len(google_ads["ad_headlines"]) >= 3
            assert len(google_ads["ad_descriptions"]) >= 2
            
            self.log_test("Offer Google Ads Integration", True)
        except Exception as e:
            self.log_test("Offer Google Ads Integration", False, str(e))
    
    def test_budget_conversion(self):
        """Test budget conversion between dollars and micros"""
        try:
            # Test dollar to micros conversion
            dollars = 50.00
            micros = int(dollars * 1000000)
            assert micros == 50000000
            
            # Test micros to dollars conversion
            converted_back = micros / 1000000
            assert converted_back == 50.00
            
            # Test edge cases
            assert int(0.01 * 1000000) == 10000  # 1 cent
            assert int(1000.00 * 1000000) == 1000000000  # $1000
            
            self.log_test("Budget Conversion", True)
        except Exception as e:
            self.log_test("Budget Conversion", False, str(e))
    
    def test_performance_data_structure(self):
        """Test performance data structure"""
        try:
            # Test performance data structure
            performance_data = {
                "campaign_id": "111222333",
                "campaign_name": "Test Campaign",
                "status": "ENABLED",
                "impressions": 1000,
                "clicks": 50,
                "cost_micros": 25000000,
                "conversions": 5.0,
                "ctr": 0.05,
                "average_cpc": 500000
            }
            
            # Validate performance data
            assert "campaign_id" in performance_data
            assert "impressions" in performance_data
            assert "clicks" in performance_data
            assert "cost_micros" in performance_data
            assert isinstance(performance_data["impressions"], int)
            assert isinstance(performance_data["clicks"], int)
            assert isinstance(performance_data["cost_micros"], int)
            
            self.log_test("Performance Data Structure", True)
        except Exception as e:
            self.log_test("Performance Data Structure", False, str(e))
    
    def test_api_request_structure(self):
        """Test API request structure validation"""
        try:
            # Test campaign creation request
            campaign_request = {
                "customer_id": "123456789",
                "campaign_name": "Test Campaign",
                "daily_budget_micros": 50000000,
                "target_url": "https://example.com",
                "keywords": ["test", "keyword"],
                "ad_headlines": ["Headline 1", "Headline 2", "Headline 3"],
                "ad_descriptions": ["Description 1", "Description 2"]
            }
            
            # Validate request structure
            assert "customer_id" in campaign_request
            assert "campaign_name" in campaign_request
            assert "daily_budget_micros" in campaign_request
            assert "target_url" in campaign_request
            assert len(campaign_request["ad_headlines"]) >= 3
            assert len(campaign_request["ad_descriptions"]) >= 2
            
            # Test campaign from offer request
            offer_request = {
                "offer_id": "test_offer_id",
                "customer_id": "123456789",
                "daily_budget_micros": 50000000
            }
            
            assert "offer_id" in offer_request
            assert "customer_id" in offer_request
            assert "daily_budget_micros" in offer_request
            
            self.log_test("API Request Structure", True)
        except Exception as e:
            self.log_test("API Request Structure", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Google Ads Integration Tests...\n")
        
        self.test_service_initialization()
        self.test_campaign_data_structure()
        self.test_budget_data_structure()
        self.test_ad_data_structure()
        self.test_keyword_data_structure()
        self.test_offer_google_ads_integration()
        self.test_budget_conversion()
        self.test_performance_data_structure()
        self.test_api_request_structure()
        
        print(f"\n📊 Test Results:")
        print(f"✅ Passed: {self.passed}")
        print(f"❌ Failed: {self.failed}")
        print(f"📈 Success Rate: {(self.passed / (self.passed + self.failed)) * 100:.1f}%")
        
        if self.failed > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['message']}")
        
        return self.failed == 0


def main():
    """Main function"""
    tester = GoogleAdsIntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! Google Ads integration is ready.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    main()
