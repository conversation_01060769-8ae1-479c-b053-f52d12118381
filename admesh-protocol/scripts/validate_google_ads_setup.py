#!/usr/bin/env python3
"""
Validation script for Google Ads integration setup
This script validates the Google Ads integration without requiring the actual Google Ads library
"""

import os
import sys
import json

def validate_files_exist():
    """Validate that all required files exist"""
    required_files = [
        "api/services/google_ads_service.py",
        "api/routes/google_ads.py",
        "docs/GOOGLE_ADS_INTEGRATION.md",
        "tests/test_google_ads_integration.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✅ All required files exist")
        return True

def validate_requirements():
    """Validate that Google Ads dependency is in requirements.txt"""
    try:
        with open("requirements.txt", "r") as f:
            content = f.read()
            if "google-ads" in content:
                print("✅ Google Ads dependency found in requirements.txt")
                return True
            else:
                print("❌ Google Ads dependency not found in requirements.txt")
                return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def validate_api_routes():
    """Validate that Google Ads routes are properly structured"""
    try:
        with open("api/routes/google_ads.py", "r") as f:
            content = f.read()
            
        required_endpoints = [
            "validate-account",
            "campaigns/create",
            "campaigns/create-from-offer",
            "performance/account",
            "performance/campaigns"
        ]
        
        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing_endpoints.append(endpoint)
        
        if missing_endpoints:
            print("❌ Missing API endpoints:")
            for endpoint in missing_endpoints:
                print(f"  - {endpoint}")
            return False
        else:
            print("✅ All required API endpoints found")
            return True
            
    except FileNotFoundError:
        print("❌ Google Ads routes file not found")
        return False

def validate_service_methods():
    """Validate that Google Ads service has required methods"""
    try:
        with open("api/services/google_ads_service.py", "r") as f:
            content = f.read()
            
        required_methods = [
            "validate_customer_access",
            "create_campaign_budget",
            "create_campaign",
            "create_ad_group",
            "create_keywords",
            "create_responsive_search_ad",
            "get_campaign_performance",
            "update_campaign_status"
        ]
        
        missing_methods = []
        for method in required_methods:
            if f"def {method}" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print("❌ Missing service methods:")
            for method in missing_methods:
                print(f"  - {method}")
            return False
        else:
            print("✅ All required service methods found")
            return True
            
    except FileNotFoundError:
        print("❌ Google Ads service file not found")
        return False

def validate_main_api_integration():
    """Validate that Google Ads routes are integrated into main API"""
    try:
        with open("api/main.py", "r") as f:
            content = f.read()
            
        if "google_ads" in content and "google-ads" in content:
            print("✅ Google Ads routes integrated into main API")
            return True
        else:
            print("❌ Google Ads routes not integrated into main API")
            return False
            
    except FileNotFoundError:
        print("❌ Main API file not found")
        return False

def validate_config_integration():
    """Validate that Google Ads config is integrated"""
    try:
        with open("config/base.py", "r") as f:
            content = f.read()
            
        if "google_ads_config" in content:
            print("✅ Google Ads configuration integrated")
            return True
        else:
            print("❌ Google Ads configuration not integrated")
            return False
            
    except FileNotFoundError:
        print("❌ Base config file not found")
        return False

def validate_dashboard_integration():
    """Validate that dashboard files exist"""
    dashboard_files = [
        "../admesh-dashboard/src/app/dashboard/brand/google-ads/page.tsx",
        "../admesh-dashboard/src/components/app-sidebar.tsx"
    ]
    
    existing_files = []
    for file_path in dashboard_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
    
    if len(existing_files) > 0:
        print(f"✅ Dashboard integration found ({len(existing_files)}/{len(dashboard_files)} files)")
        return True
    else:
        print("❌ Dashboard integration not found")
        return False

def validate_offer_integration():
    """Validate that offers are integrated with Google Ads"""
    try:
        with open("api/routes/brands.py", "r") as f:
            content = f.read()
            
        if "google_ads" in content and "GoogleAdsSettings" in content:
            print("✅ Google Ads integrated with offer system")
            return True
        else:
            print("❌ Google Ads not integrated with offer system")
            return False
            
    except FileNotFoundError:
        print("❌ Brands routes file not found")
        return False

def main():
    """Main validation function"""
    print("🚀 Validating Google Ads Integration Setup...\n")
    
    validations = [
        ("File Structure", validate_files_exist),
        ("Requirements", validate_requirements),
        ("API Routes", validate_api_routes),
        ("Service Methods", validate_service_methods),
        ("Main API Integration", validate_main_api_integration),
        ("Configuration Integration", validate_config_integration),
        ("Dashboard Integration", validate_dashboard_integration),
        ("Offer Integration", validate_offer_integration)
    ]
    
    passed = 0
    total = len(validations)
    
    for name, validation_func in validations:
        print(f"\n📋 {name}:")
        if validation_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Validation Results:")
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    print(f"📈 Success Rate: {(passed / total) * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 Google Ads integration setup is complete!")
        print("\n📝 Next Steps:")
        print("1. Install dependencies: pip install google-ads==25.0.0")
        print("2. Set up Google Ads API credentials in environment variables")
        print("3. Test the integration with a real Google Ads account")
        return True
    else:
        print(f"\n💥 {total - passed} validation(s) failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
