"""
Tests for Google Ads integration
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from api.services.google_ads_service import GoogleAdsService
from api.routes.google_ads import router
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Create test app
app = FastAPI()
app.include_router(router, prefix="/google-ads")
client = TestClient(app)


class TestGoogleAdsService:
    """Test cases for GoogleAdsService"""
    
    @patch('api.services.google_ads_service.GoogleAdsClient')
    def test_service_initialization(self, mock_client):
        """Test that GoogleAdsService initializes correctly"""
        mock_config = Mock()
        mock_config.google_ads_config = {
            "developer_token": "test_token",
            "client_id": "test_client_id",
            "client_secret": "test_secret",
            "refresh_token": "test_refresh",
            "login_customer_id": "*********",
            "use_proto_plus": True
        }
        
        with patch('api.services.google_ads_service.get_config', return_value=mock_config):
            service = GoogleAdsService()
            assert service.google_ads_config["developer_token"] == "test_token"
    
    @patch('api.services.google_ads_service.GoogleAdsClient')
    def test_validate_customer_access_success(self, mock_client):
        """Test successful customer access validation"""
        mock_config = Mock()
        mock_config.google_ads_config = {
            "developer_token": "test_token",
            "client_id": "test_client_id",
            "client_secret": "test_secret",
            "refresh_token": "test_refresh",
            "login_customer_id": "*********",
            "use_proto_plus": True
        }
        
        # Mock the client and services
        mock_client_instance = Mock()
        mock_customer_service = Mock()
        mock_customer = Mock()
        
        mock_client.load_from_dict.return_value = mock_client_instance
        mock_client_instance.get_service.return_value = mock_customer_service
        mock_customer_service.customer_path.return_value = "customers/*********"
        mock_customer_service.get_customer.return_value = mock_customer
        
        with patch('api.services.google_ads_service.get_config', return_value=mock_config):
            service = GoogleAdsService()
            result = service.validate_customer_access("*********")
            assert result is True
    
    @patch('api.services.google_ads_service.GoogleAdsClient')
    def test_create_campaign_budget_success(self, mock_client):
        """Test successful campaign budget creation"""
        mock_config = Mock()
        mock_config.google_ads_config = {
            "developer_token": "test_token",
            "client_id": "test_client_id",
            "client_secret": "test_secret",
            "refresh_token": "test_refresh",
            "login_customer_id": "*********",
            "use_proto_plus": True
        }
        
        # Mock the client and services
        mock_client_instance = Mock()
        mock_budget_service = Mock()
        mock_response = Mock()
        mock_result = Mock()
        mock_result.resource_name = "customers/*********/campaignBudgets/*********"
        mock_response.results = [mock_result]
        
        mock_client.load_from_dict.return_value = mock_client_instance
        mock_client_instance.get_service.return_value = mock_budget_service
        mock_client_instance.get_type.return_value = Mock()
        mock_budget_service.mutate_campaign_budgets.return_value = mock_response
        
        with patch('api.services.google_ads_service.get_config', return_value=mock_config):
            service = GoogleAdsService()
            budget_data = {
                "name": "Test Budget",
                "amount_micros": 50000000  # $50
            }
            result = service.create_campaign_budget("*********", budget_data)
            assert result == "customers/*********/campaignBudgets/*********"
    
    @patch('api.services.google_ads_service.GoogleAdsClient')
    def test_create_campaign_success(self, mock_client):
        """Test successful campaign creation"""
        mock_config = Mock()
        mock_config.google_ads_config = {
            "developer_token": "test_token",
            "client_id": "test_client_id",
            "client_secret": "test_secret",
            "refresh_token": "test_refresh",
            "login_customer_id": "*********",
            "use_proto_plus": True
        }
        
        # Mock the client and services
        mock_client_instance = Mock()
        mock_campaign_service = Mock()
        mock_response = Mock()
        mock_result = Mock()
        mock_result.resource_name = "customers/*********/campaigns/*********"
        mock_response.results = [mock_result]
        
        mock_client.load_from_dict.return_value = mock_client_instance
        mock_client_instance.get_service.return_value = mock_campaign_service
        mock_client_instance.get_type.return_value = Mock()
        mock_client_instance.enums.CampaignStatusEnum.PAUSED = "PAUSED"
        mock_client_instance.enums.AdvertisingChannelTypeEnum.SEARCH = "SEARCH"
        mock_campaign_service.mutate_campaigns.return_value = mock_response
        
        with patch('api.services.google_ads_service.get_config', return_value=mock_config):
            service = GoogleAdsService()
            campaign_data = {
                "name": "Test Campaign",
                "budget_resource_name": "customers/*********/campaignBudgets/*********"
            }
            result = service.create_campaign("*********", campaign_data)
            assert result == "customers/*********/campaigns/*********"


class TestGoogleAdsAPI:
    """Test cases for Google Ads API routes"""
    
    @patch('api.routes.google_ads.require_role')
    @patch('api.routes.google_ads.GoogleAdsService')
    @patch('api.routes.google_ads.db')
    def test_validate_account_success(self, mock_db, mock_service_class, mock_require_role):
        """Test successful account validation"""
        # Mock authentication
        mock_user = {"uid": "test_brand_id"}
        mock_require_role.return_value = mock_user
        
        # Mock Google Ads service
        mock_service = Mock()
        mock_service.validate_customer_access.return_value = True
        mock_service_class.return_value = mock_service
        
        # Mock Firestore
        mock_brand_ref = Mock()
        mock_db.collection.return_value.document.return_value = mock_brand_ref
        
        # Make request
        response = client.post(
            "/google-ads/validate-account",
            json={"customer_id": "*********"}
        )
        
        # Assertions would go here if we had proper dependency injection
        # For now, this test structure shows how we would test the endpoint
    
    @patch('api.routes.google_ads.require_role')
    @patch('api.routes.google_ads.GoogleAdsService')
    @patch('api.routes.google_ads.db')
    def test_create_campaign_success(self, mock_db, mock_service_class, mock_require_role):
        """Test successful campaign creation"""
        # Mock authentication
        mock_user = {"uid": "test_brand_id"}
        mock_require_role.return_value = mock_user
        
        # Mock Google Ads service
        mock_service = Mock()
        mock_service.validate_customer_access.return_value = True
        mock_service.create_campaign_budget.return_value = "budget_resource_name"
        mock_service.create_campaign.return_value = "campaign_resource_name"
        mock_service.create_ad_group.return_value = "ad_group_resource_name"
        mock_service.create_keywords.return_value = ["keyword1", "keyword2"]
        mock_service.create_responsive_search_ad.return_value = "ad_resource_name"
        mock_service_class.return_value = mock_service
        
        # Mock Firestore
        mock_collection = Mock()
        mock_db.collection.return_value = mock_collection
        
        # Test data
        campaign_data = {
            "customer_id": "*********",
            "campaign_name": "Test Campaign",
            "daily_budget_micros": 50000000,
            "target_url": "https://example.com",
            "keywords": ["test", "keyword"],
            "ad_headlines": ["Headline 1", "Headline 2", "Headline 3"],
            "ad_descriptions": ["Description 1", "Description 2"]
        }
        
        # Make request
        response = client.post(
            "/google-ads/campaigns/create",
            json=campaign_data
        )
        
        # Assertions would go here if we had proper dependency injection
    
    def test_campaign_from_offer_validation(self):
        """Test validation for creating campaign from offer"""
        # Test missing offer_id
        response = client.post(
            "/google-ads/campaigns/create-from-offer",
            json={
                "customer_id": "*********",
                "daily_budget_micros": 50000000
            }
        )
        # Would assert validation error if we had proper setup


class TestGoogleAdsIntegrationWithOffers:
    """Test cases for Google Ads integration with AdMesh offers"""
    
    def test_offer_creation_with_google_ads(self):
        """Test that offers can be created with Google Ads settings"""
        offer_data = {
            "product_id": "test_product",
            "goal": "signup",
            "payout": {"amount": 100, "currency": "USD", "model": "CPA"},
            "offer_total_budget_allocated": 10000,
            "url": "https://example.com",
            "google_ads": {
                "enabled": True,
                "customer_id": "*********",
                "daily_budget_micros": 50000000,
                "keywords": ["test", "keyword"],
                "ad_headlines": ["Headline 1", "Headline 2", "Headline 3"],
                "ad_descriptions": ["Description 1", "Description 2"]
            }
        }
        
        # Test that the data structure is valid
        assert offer_data["google_ads"]["enabled"] is True
        assert offer_data["google_ads"]["customer_id"] == "*********"
        assert len(offer_data["google_ads"]["ad_headlines"]) >= 3
        assert len(offer_data["google_ads"]["ad_descriptions"]) >= 2
    
    def test_google_ads_budget_conversion(self):
        """Test that budget amounts are correctly converted to micros"""
        daily_budget_dollars = 50.00
        daily_budget_micros = int(daily_budget_dollars * 1000000)
        
        assert daily_budget_micros == 50000000
        
        # Test conversion back to dollars
        converted_back = daily_budget_micros / 1000000
        assert converted_back == 50.00


class TestGoogleAdsPerformanceTracking:
    """Test cases for Google Ads performance tracking"""
    
    @patch('api.services.google_ads_service.GoogleAdsClient')
    def test_get_campaign_performance(self, mock_client):
        """Test getting campaign performance metrics"""
        mock_config = Mock()
        mock_config.google_ads_config = {
            "developer_token": "test_token",
            "client_id": "test_client_id",
            "client_secret": "test_secret",
            "refresh_token": "test_refresh",
            "login_customer_id": "*********",
            "use_proto_plus": True
        }
        
        # Mock the client and services
        mock_client_instance = Mock()
        mock_ga_service = Mock()
        mock_response = [Mock()]
        
        # Mock performance data
        mock_row = Mock()
        mock_row.campaign.id = "*********"
        mock_row.campaign.name = "Test Campaign"
        mock_row.campaign.status.name = "ENABLED"
        mock_row.metrics.impressions = 1000
        mock_row.metrics.clicks = 50
        mock_row.metrics.cost_micros = 25000000  # $25
        mock_row.metrics.conversions = 5.0
        mock_row.metrics.ctr = 0.05
        mock_row.metrics.average_cpc = 500000  # $0.50
        
        mock_response = [mock_row]
        
        mock_client.load_from_dict.return_value = mock_client_instance
        mock_client_instance.get_service.return_value = mock_ga_service
        mock_ga_service.search.return_value = mock_response
        
        with patch('api.services.google_ads_service.get_config', return_value=mock_config):
            service = GoogleAdsService()
            result = service.get_campaign_performance("*********", "campaign_resource_name")
            
            assert result["campaign_id"] == "*********"
            assert result["impressions"] == 1000
            assert result["clicks"] == 50
            assert result["cost_micros"] == 25000000


if __name__ == "__main__":
    pytest.main([__file__])
