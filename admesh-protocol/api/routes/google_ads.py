"""
Google Ads API Routes for AdMesh
Provides endpoints for managing Google Ads campaigns, ad groups, and keywords
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from auth.deps import require_role
from api.services.google_ads_service import GoogleAdsService
from firebase.config import get_db
from google.cloud import firestore

logger = logging.getLogger(__name__)
router = APIRouter()
db = get_db()


# Pydantic Models
class GoogleAdsAccountRequest(BaseModel):
    customer_id: str = Field(..., description="Google Ads customer ID")


class CampaignCreateRequest(BaseModel):
    customer_id: str = Field(..., description="Google Ads customer ID")
    campaign_name: str = Field(..., description="Campaign name")
    daily_budget_micros: int = Field(..., description="Daily budget in micros (1 USD = 1,000,000 micros)")
    target_url: str = Field(..., description="Landing page URL")
    keywords: List[str] = Field(..., description="List of keywords to target")
    ad_headlines: List[str] = Field(..., description="Ad headlines (3-15 required)")
    ad_descriptions: List[str] = Field(..., description="Ad descriptions (2-4 required)")


class CampaignFromOfferRequest(BaseModel):
    offer_id: str = Field(..., description="AdMesh offer ID to create campaign from")
    customer_id: str = Field(..., description="Google Ads customer ID")
    daily_budget_micros: int = Field(..., description="Daily budget in micros (1 USD = 1,000,000 micros)")
    custom_keywords: Optional[List[str]] = Field(default=None, description="Custom keywords (will use offer keywords if not provided)")
    custom_headlines: Optional[List[str]] = Field(default=None, description="Custom headlines (will use offer title if not provided)")
    custom_descriptions: Optional[List[str]] = Field(default=None, description="Custom descriptions (will use offer description if not provided)")


class CampaignResponse(BaseModel):
    success: bool
    campaign_resource_name: Optional[str] = None
    campaign_id: Optional[str] = None
    message: str


class CampaignPerformanceResponse(BaseModel):
    campaign_id: Optional[str] = None
    campaign_name: Optional[str] = None
    status: Optional[str] = None
    impressions: Optional[int] = None
    clicks: Optional[int] = None
    cost_micros: Optional[int] = None
    conversions: Optional[float] = None
    ctr: Optional[float] = None
    average_cpc: Optional[int] = None


class AccountPerformanceResponse(BaseModel):
    customer_id: str
    impressions: int
    clicks: int
    cost_micros: int
    conversions: float
    ctr: float
    average_cpc: int
    conversion_rate: float


class CampaignsPerformanceResponse(BaseModel):
    success: bool
    campaigns: List[Dict[str, Any]]
    total: int


@router.post("/validate-account")
async def validate_google_ads_account(
    request: GoogleAdsAccountRequest,
    user_data = Depends(require_role("brand"))
):
    """Validate access to a Google Ads account"""
    try:
        google_ads_service = GoogleAdsService()
        is_valid = google_ads_service.validate_customer_access(request.customer_id)
        
        if is_valid:
            # Store the validated customer ID for the brand
            brand_id = user_data["uid"]
            db.collection("brands").document(brand_id).update({
                "google_ads_customer_id": request.customer_id,
                "google_ads_validated_at": firestore.SERVER_TIMESTAMP
            })
            
            return {
                "success": True,
                "message": "Google Ads account validated successfully",
                "customer_id": request.customer_id
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="Unable to access the specified Google Ads account. Please check the customer ID and ensure proper permissions."
            )
            
    except Exception as e:
        logger.error(f"Error validating Google Ads account: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate Google Ads account: {str(e)}")


@router.post("/campaigns/create", response_model=CampaignResponse)
async def create_google_ads_campaign(
    request: CampaignCreateRequest,
    user_data = Depends(require_role("brand"))
):
    """Create a new Google Ads campaign"""
    try:
        brand_id = user_data["uid"]
        google_ads_service = GoogleAdsService()
        
        # Validate account access first
        if not google_ads_service.validate_customer_access(request.customer_id):
            raise HTTPException(
                status_code=400,
                detail="Unable to access the specified Google Ads account"
            )
        
        # Create campaign budget
        budget_data = {
            "name": f"{request.campaign_name} Budget",
            "amount_micros": request.daily_budget_micros
        }
        
        budget_resource_name = google_ads_service.create_campaign_budget(
            request.customer_id, budget_data
        )
        
        if not budget_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create campaign budget")
        
        # Create campaign
        campaign_data = {
            "name": request.campaign_name,
            "budget_resource_name": budget_resource_name
        }
        
        campaign_resource_name = google_ads_service.create_campaign(
            request.customer_id, campaign_data
        )
        
        if not campaign_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create campaign")
        
        # Create ad group
        ad_group_data = {
            "name": f"{request.campaign_name} Ad Group",
            "campaign_resource_name": campaign_resource_name,
            "cpc_bid_micros": 1000000  # $1.00 default bid
        }
        
        ad_group_resource_name = google_ads_service.create_ad_group(
            request.customer_id, ad_group_data
        )
        
        if not ad_group_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create ad group")
        
        # Add keywords
        keyword_resource_names = google_ads_service.create_keywords(
            request.customer_id, ad_group_resource_name, request.keywords
        )
        
        # Create responsive search ad
        ad_data = {
            "headlines": request.ad_headlines,
            "descriptions": request.ad_descriptions,
            "final_urls": [request.target_url]
        }
        
        ad_resource_name = google_ads_service.create_responsive_search_ad(
            request.customer_id, ad_group_resource_name, ad_data
        )
        
        # Extract campaign ID from resource name
        campaign_id = campaign_resource_name.split("/")[-1]
        
        # Store campaign information in Firestore
        campaign_doc = {
            "brand_id": brand_id,
            "customer_id": request.customer_id,
            "campaign_resource_name": campaign_resource_name,
            "campaign_id": campaign_id,
            "campaign_name": request.campaign_name,
            "budget_resource_name": budget_resource_name,
            "ad_group_resource_name": ad_group_resource_name,
            "ad_resource_name": ad_resource_name,
            "keyword_resource_names": keyword_resource_names,
            "daily_budget_micros": request.daily_budget_micros,
            "target_url": request.target_url,
            "keywords": request.keywords,
            "status": "PAUSED",  # Campaigns start paused
            "created_at": firestore.SERVER_TIMESTAMP
        }
        
        db.collection("google_ads_campaigns").add(campaign_doc)
        
        return CampaignResponse(
            success=True,
            campaign_resource_name=campaign_resource_name,
            campaign_id=campaign_id,
            message="Campaign created successfully (started in paused state)"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Google Ads campaign: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create campaign: {str(e)}")


@router.get("/campaigns/{campaign_id}/performance", response_model=CampaignPerformanceResponse)
async def get_campaign_performance(
    campaign_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get performance metrics for a Google Ads campaign"""
    try:
        brand_id = user_data["uid"]
        
        # Find the campaign in Firestore
        campaigns_ref = db.collection("google_ads_campaigns")
        query = campaigns_ref.where("brand_id", "==", brand_id).where("campaign_id", "==", campaign_id)
        campaigns = list(query.stream())
        
        if not campaigns:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        campaign_data = campaigns[0].to_dict()
        customer_id = campaign_data["customer_id"]
        campaign_resource_name = campaign_data["campaign_resource_name"]
        
        # Get performance data from Google Ads
        google_ads_service = GoogleAdsService()
        performance_data = google_ads_service.get_campaign_performance(
            customer_id, campaign_resource_name
        )
        
        if performance_data:
            return CampaignPerformanceResponse(**performance_data)
        else:
            return CampaignPerformanceResponse(
                campaign_id=campaign_id,
                campaign_name=campaign_data["campaign_name"],
                status="UNKNOWN"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting campaign performance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get campaign performance: {str(e)}")


@router.get("/campaigns")
async def list_google_ads_campaigns(
    user_data = Depends(require_role("brand"))
):
    """List all Google Ads campaigns for the authenticated brand"""
    try:
        brand_id = user_data["uid"]
        
        # Get campaigns from Firestore
        campaigns_ref = db.collection("google_ads_campaigns")
        query = campaigns_ref.where("brand_id", "==", brand_id).order_by("created_at", direction=firestore.Query.DESCENDING)
        campaigns = list(query.stream())
        
        campaign_list = []
        for campaign_doc in campaigns:
            campaign_data = campaign_doc.to_dict()
            campaign_list.append({
                "id": campaign_doc.id,
                "campaign_id": campaign_data.get("campaign_id"),
                "campaign_name": campaign_data.get("campaign_name"),
                "status": campaign_data.get("status"),
                "daily_budget_micros": campaign_data.get("daily_budget_micros"),
                "target_url": campaign_data.get("target_url"),
                "keywords": campaign_data.get("keywords", []),
                "created_at": campaign_data.get("created_at")
            })
        
        return {
            "success": True,
            "campaigns": campaign_list,
            "total": len(campaign_list)
        }
        
    except Exception as e:
        logger.error(f"Error listing Google Ads campaigns: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list campaigns: {str(e)}")


@router.post("/campaigns/{campaign_id}/pause")
async def pause_campaign(
    campaign_id: str,
    user_data = Depends(require_role("brand"))
):
    """Pause a Google Ads campaign"""
    try:
        brand_id = user_data["uid"]
        
        # Find the campaign in Firestore
        campaigns_ref = db.collection("google_ads_campaigns")
        query = campaigns_ref.where("brand_id", "==", brand_id).where("campaign_id", "==", campaign_id)
        campaigns = list(query.stream())
        
        if not campaigns:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        campaign_doc = campaigns[0]
        campaign_data = campaign_doc.to_dict()
        
        # TODO: Implement actual Google Ads API call to pause campaign
        # For now, just update the status in Firestore
        campaign_doc.reference.update({
            "status": "PAUSED",
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        return {
            "success": True,
            "message": "Campaign paused successfully",
            "campaign_id": campaign_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pausing campaign: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to pause campaign: {str(e)}")


@router.post("/campaigns/{campaign_id}/resume")
async def resume_campaign(
    campaign_id: str,
    user_data = Depends(require_role("brand"))
):
    """Resume a Google Ads campaign"""
    try:
        brand_id = user_data["uid"]
        
        # Find the campaign in Firestore
        campaigns_ref = db.collection("google_ads_campaigns")
        query = campaigns_ref.where("brand_id", "==", brand_id).where("campaign_id", "==", campaign_id)
        campaigns = list(query.stream())
        
        if not campaigns:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        campaign_doc = campaigns[0]
        campaign_data = campaign_doc.to_dict()
        
        # TODO: Implement actual Google Ads API call to resume campaign
        # For now, just update the status in Firestore
        campaign_doc.reference.update({
            "status": "ENABLED",
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        return {
            "success": True,
            "message": "Campaign resumed successfully",
            "campaign_id": campaign_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resuming campaign: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to resume campaign: {str(e)}")


@router.post("/campaigns/create-from-offer", response_model=CampaignResponse)
async def create_campaign_from_offer(
    request: CampaignFromOfferRequest,
    user_data = Depends(require_role("brand"))
):
    """Create a Google Ads campaign from an existing AdMesh offer"""
    try:
        brand_id = user_data["uid"]

        # Get the offer data
        offer_ref = db.collection("offers").document(request.offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()

        # Verify ownership
        if offer_data["brand_id"] != brand_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Get the associated product data
        product_id = offer_data.get("product_id")
        if not product_id:
            raise HTTPException(status_code=400, detail="Offer has no associated product")

        product_ref = db.collection("products").document(product_id)
        product_doc = product_ref.get()

        if not product_doc.exists:
            raise HTTPException(status_code=404, detail="Associated product not found")

        product_data = product_doc.to_dict()

        # Prepare campaign data using offer and product information
        campaign_name = f"{product_data['title']} - AdMesh Campaign"
        target_url = offer_data["url"]

        # Use custom data if provided, otherwise fall back to offer/product data
        keywords = request.custom_keywords or product_data.get("keywords", [])
        headlines = request.custom_headlines or [
            offer_data.get("offer_title", product_data["title"]),
            product_data["title"],
            f"Get {product_data['title']} Now"
        ]
        descriptions = request.custom_descriptions or [
            offer_data.get("offer_description", product_data["description"]),
            product_data.get("description", "")
        ]

        # Filter out empty strings and ensure we have valid content
        headlines = [h for h in headlines if h and h.strip()][:15]  # Max 15 headlines
        descriptions = [d for d in descriptions if d and d.strip()][:4]  # Max 4 descriptions

        if len(headlines) < 3:
            raise HTTPException(
                status_code=400,
                detail="Need at least 3 headlines for responsive search ads. Please provide custom headlines."
            )

        if len(descriptions) < 2:
            raise HTTPException(
                status_code=400,
                detail="Need at least 2 descriptions for responsive search ads. Please provide custom descriptions."
            )

        # Create the campaign using the existing logic
        campaign_request = CampaignCreateRequest(
            customer_id=request.customer_id,
            campaign_name=campaign_name,
            daily_budget_micros=request.daily_budget_micros,
            target_url=target_url,
            keywords=keywords,
            ad_headlines=headlines,
            ad_descriptions=descriptions
        )

        # Use the existing campaign creation logic
        google_ads_service = GoogleAdsService()

        # Validate account access first
        if not google_ads_service.validate_customer_access(request.customer_id):
            raise HTTPException(
                status_code=400,
                detail="Unable to access the specified Google Ads account"
            )

        # Create campaign budget
        budget_data = {
            "name": f"{campaign_name} Budget",
            "amount_micros": request.daily_budget_micros
        }

        budget_resource_name = google_ads_service.create_campaign_budget(
            request.customer_id, budget_data
        )

        if not budget_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create campaign budget")

        # Create campaign
        campaign_data = {
            "name": campaign_name,
            "budget_resource_name": budget_resource_name
        }

        campaign_resource_name = google_ads_service.create_campaign(
            request.customer_id, campaign_data
        )

        if not campaign_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create campaign")

        # Create ad group
        ad_group_data = {
            "name": f"{campaign_name} Ad Group",
            "campaign_resource_name": campaign_resource_name,
            "cpc_bid_micros": 1000000  # $1.00 default bid
        }

        ad_group_resource_name = google_ads_service.create_ad_group(
            request.customer_id, ad_group_data
        )

        if not ad_group_resource_name:
            raise HTTPException(status_code=500, detail="Failed to create ad group")

        # Add keywords
        keyword_resource_names = google_ads_service.create_keywords(
            request.customer_id, ad_group_resource_name, keywords
        )

        # Create responsive search ad
        ad_data = {
            "headlines": headlines,
            "descriptions": descriptions,
            "final_urls": [target_url]
        }

        ad_resource_name = google_ads_service.create_responsive_search_ad(
            request.customer_id, ad_group_resource_name, ad_data
        )

        # Extract campaign ID from resource name
        campaign_id = campaign_resource_name.split("/")[-1]

        # Store campaign information in Firestore with offer association
        campaign_doc = {
            "brand_id": brand_id,
            "offer_id": request.offer_id,
            "product_id": product_id,
            "customer_id": request.customer_id,
            "campaign_resource_name": campaign_resource_name,
            "campaign_id": campaign_id,
            "campaign_name": campaign_name,
            "budget_resource_name": budget_resource_name,
            "ad_group_resource_name": ad_group_resource_name,
            "ad_resource_name": ad_resource_name,
            "keyword_resource_names": keyword_resource_names,
            "daily_budget_micros": request.daily_budget_micros,
            "target_url": target_url,
            "keywords": keywords,
            "status": "PAUSED",  # Campaigns start paused
            "created_from_offer": True,
            "created_at": firestore.SERVER_TIMESTAMP
        }

        db.collection("google_ads_campaigns").add(campaign_doc)

        # Update the offer to reference the Google Ads campaign
        offer_ref.update({
            "google_ads_campaign_id": campaign_id,
            "google_ads_campaign_resource_name": campaign_resource_name,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        return CampaignResponse(
            success=True,
            campaign_resource_name=campaign_resource_name,
            campaign_id=campaign_id,
            message=f"Campaign created successfully from offer {request.offer_id} (started in paused state)"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Google Ads campaign from offer: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create campaign from offer: {str(e)}")


@router.get("/performance/account/{customer_id}", response_model=AccountPerformanceResponse)
async def get_account_performance(
    customer_id: str,
    date_range: str = "LAST_30_DAYS",
    user_data = Depends(require_role("brand"))
):
    """Get Google Ads account performance metrics"""
    try:
        brand_id = user_data["uid"]

        # Verify the brand has access to this customer ID
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        if brand_data.get("google_ads_customer_id") != customer_id:
            raise HTTPException(status_code=403, detail="Access denied to this Google Ads account")

        # Get performance data
        google_ads_service = GoogleAdsService()
        performance_data = google_ads_service.get_account_performance(customer_id, date_range)

        if performance_data:
            return AccountPerformanceResponse(**performance_data)
        else:
            raise HTTPException(status_code=500, detail="Failed to retrieve account performance data")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting account performance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get account performance: {str(e)}")


@router.get("/performance/campaigns/{customer_id}", response_model=CampaignsPerformanceResponse)
async def get_campaigns_performance(
    customer_id: str,
    date_range: str = "LAST_30_DAYS",
    user_data = Depends(require_role("brand"))
):
    """Get performance metrics for all Google Ads campaigns"""
    try:
        brand_id = user_data["uid"]

        # Verify the brand has access to this customer ID
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        if brand_data.get("google_ads_customer_id") != customer_id:
            raise HTTPException(status_code=403, detail="Access denied to this Google Ads account")

        # Get campaigns performance data
        google_ads_service = GoogleAdsService()
        campaigns_data = google_ads_service.get_campaigns_performance(customer_id, date_range)

        return CampaignsPerformanceResponse(
            success=True,
            campaigns=campaigns_data,
            total=len(campaigns_data)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting campaigns performance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get campaigns performance: {str(e)}")


@router.post("/campaigns/{campaign_id}/update-status")
async def update_campaign_status(
    campaign_id: str,
    status: str,
    user_data = Depends(require_role("brand"))
):
    """Update Google Ads campaign status (ENABLED, PAUSED, REMOVED)"""
    try:
        brand_id = user_data["uid"]

        # Find the campaign in Firestore
        campaigns_ref = db.collection("google_ads_campaigns")
        query = campaigns_ref.where("brand_id", "==", brand_id).where("campaign_id", "==", campaign_id)
        campaigns = list(query.stream())

        if not campaigns:
            raise HTTPException(status_code=404, detail="Campaign not found")

        campaign_doc = campaigns[0]
        campaign_data = campaign_doc.to_dict()
        customer_id = campaign_data["customer_id"]
        campaign_resource_name = campaign_data["campaign_resource_name"]

        # Update campaign status in Google Ads
        google_ads_service = GoogleAdsService()
        success = google_ads_service.update_campaign_status(
            customer_id, campaign_resource_name, status
        )

        if success:
            # Update status in Firestore
            campaign_doc.reference.update({
                "status": status.upper(),
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            return {
                "success": True,
                "message": f"Campaign status updated to {status.upper()}",
                "campaign_id": campaign_id
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update campaign status in Google Ads")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating campaign status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update campaign status: {str(e)}")
