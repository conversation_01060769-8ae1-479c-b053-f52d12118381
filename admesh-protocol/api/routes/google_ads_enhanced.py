"""
Enhanced Google Ads API Routes for AdMesh
Provides comprehensive Google Ads management with OAuth and automation
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field
from auth.deps import require_role
from api.services.google_ads_data_sync_service import GoogleAdsDataSyncService
from api.services.google_ads_oauth_service import GoogleAdsOAuthService
from api.services.google_ads_mock_data_service import GoogleAdsMockDataService

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic Models
class SyncRequest(BaseModel):
    force_refresh: bool = Field(default=False, description="Force refresh even if recently synced")


class SyncResponse(BaseModel):
    status: str
    synced_accounts: Optional[int] = None
    message: str
    synced_at: Optional[str] = None


class AccountOverviewResponse(BaseModel):
    total_accounts: int
    accounts: List[Dict[str, Any]]
    aggregate_performance: Dict[str, Any]


class CampaignDataResponse(BaseModel):
    campaigns: List[Dict[str, Any]]
    total_campaigns: int


class RealTimeMetricsResponse(BaseModel):
    timestamp: str
    today_metrics: Dict[str, Any]
    hourly_trend: List[Dict[str, Any]]


class RecommendationsResponse(BaseModel):
    recommendations: List[Dict[str, Any]]
    total_recommendations: int
    generated_at: str


@router.post("/sync", response_model=SyncResponse)
async def sync_account_data(
    request: SyncRequest,
    background_tasks: BackgroundTasks,
    user_data = Depends(require_role("brand"))
):
    """Sync Google Ads account data"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        # Run sync in background if not forcing immediate refresh
        if not request.force_refresh:
            background_tasks.add_task(sync_service.sync_account_data, brand_id, False)
            return SyncResponse(
                status="queued",
                message="Data sync queued in background"
            )
        
        # Run immediate sync
        result = sync_service.sync_account_data(brand_id, request.force_refresh)
        
        return SyncResponse(**result)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Sync failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


@router.get("/overview", response_model=AccountOverviewResponse)
async def get_account_overview(
    user_data = Depends(require_role("brand"))
):
    """Get account overview with performance data"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        overview = sync_service.get_account_overview(brand_id)
        
        if overview.get("status") == "no_data":
            raise HTTPException(status_code=404, detail="No Google Ads data found. Please sync your account first.")
        
        return AccountOverviewResponse(**overview)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get overview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get overview: {str(e)}")


@router.get("/campaigns", response_model=CampaignDataResponse)
async def get_campaigns_data(
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    user_data = Depends(require_role("brand"))
):
    """Get campaigns data"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        campaigns = sync_service.get_campaigns_data(brand_id, customer_id)
        
        return CampaignDataResponse(
            campaigns=campaigns,
            total_campaigns=len(campaigns)
        )
        
    except Exception as e:
        logger.error(f"Failed to get campaigns: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get campaigns: {str(e)}")


@router.get("/realtime/{customer_id}", response_model=RealTimeMetricsResponse)
async def get_real_time_metrics(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get real-time performance metrics"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        metrics = sync_service.get_real_time_metrics(brand_id, customer_id)
        
        return RealTimeMetricsResponse(**metrics)
        
    except Exception as e:
        logger.error(f"Failed to get real-time metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get real-time metrics: {str(e)}")


@router.get("/recommendations/{customer_id}", response_model=RecommendationsResponse)
async def get_recommendations(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get optimization recommendations"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        recommendations = sync_service.get_recommendations(brand_id, customer_id)
        
        return RecommendationsResponse(
            recommendations=recommendations,
            total_recommendations=len(recommendations),
            generated_at=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to get recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get recommendations: {str(e)}")


@router.get("/campaign/{campaign_id}/details")
async def get_campaign_details(
    campaign_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get detailed campaign data including ad groups, keywords, and ads"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        # Get all campaigns and find the specific one
        campaigns = sync_service.get_campaigns_data(brand_id)
        
        campaign = None
        for camp in campaigns:
            if camp["campaign_id"] == campaign_id:
                campaign = camp
                break
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        return {
            "campaign": campaign,
            "ad_groups_count": len(campaign.get("ad_groups", [])),
            "total_keywords": sum(len(ag.get("keywords", [])) for ag in campaign.get("ad_groups", [])),
            "total_ads": sum(len(ag.get("ads", [])) for ag in campaign.get("ad_groups", [])),
            "total_search_terms": sum(len(ag.get("search_terms", [])) for ag in campaign.get("ad_groups", []))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get campaign details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get campaign details: {str(e)}")


@router.get("/keywords/performance")
async def get_keywords_performance(
    customer_id: Optional[str] = Query(None),
    campaign_id: Optional[str] = Query(None),
    limit: int = Query(50, description="Number of keywords to return"),
    sort_by: str = Query("cost_micros", description="Sort by metric"),
    user_data = Depends(require_role("brand"))
):
    """Get keywords performance data"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        campaigns = sync_service.get_campaigns_data(brand_id, customer_id)
        
        all_keywords = []
        for campaign in campaigns:
            if campaign_id and campaign["campaign_id"] != campaign_id:
                continue
                
            for ad_group in campaign.get("ad_groups", []):
                for keyword in ad_group.get("keywords", []):
                    keyword_data = {
                        **keyword,
                        "campaign_id": campaign["campaign_id"],
                        "campaign_name": campaign["campaign_name"],
                        "ad_group_name": ad_group["ad_group_name"]
                    }
                    all_keywords.append(keyword_data)
        
        # Sort keywords by specified metric
        if sort_by in ["cost_micros", "clicks", "impressions", "conversions"]:
            all_keywords.sort(
                key=lambda x: x.get("performance", {}).get(sort_by, 0),
                reverse=True
            )
        
        return {
            "keywords": all_keywords[:limit],
            "total_keywords": len(all_keywords),
            "sort_by": sort_by
        }
        
    except Exception as e:
        logger.error(f"Failed to get keywords performance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get keywords performance: {str(e)}")


@router.get("/search-terms/analysis")
async def get_search_terms_analysis(
    customer_id: Optional[str] = Query(None),
    min_impressions: int = Query(10, description="Minimum impressions threshold"),
    user_data = Depends(require_role("brand"))
):
    """Get search terms analysis for keyword expansion"""
    try:
        brand_id = user_data["uid"]
        sync_service = GoogleAdsDataSyncService()
        
        campaigns = sync_service.get_campaigns_data(brand_id, customer_id)
        
        all_search_terms = []
        for campaign in campaigns:
            for ad_group in campaign.get("ad_groups", []):
                for search_term in ad_group.get("search_terms", []):
                    if search_term.get("performance", {}).get("impressions", 0) >= min_impressions:
                        search_term_data = {
                            **search_term,
                            "campaign_id": campaign["campaign_id"],
                            "campaign_name": campaign["campaign_name"],
                            "ad_group_name": ad_group["ad_group_name"]
                        }
                        all_search_terms.append(search_term_data)
        
        # Analyze search terms for keyword opportunities
        keyword_opportunities = []
        negative_keyword_suggestions = []
        
        for term in all_search_terms:
            perf = term.get("performance", {})
            ctr = perf.get("ctr", 0)
            conversion_rate = perf.get("conversion_rate", 0)
            
            if not term.get("added_as_keyword", False):
                if ctr > 0.03 and conversion_rate > 0.02:  # Good performing terms
                    keyword_opportunities.append(term)
                elif ctr < 0.005 and perf.get("clicks", 0) > 5:  # Poor performing terms
                    negative_keyword_suggestions.append(term)
        
        return {
            "search_terms": all_search_terms,
            "keyword_opportunities": keyword_opportunities[:20],
            "negative_keyword_suggestions": negative_keyword_suggestions[:20],
            "total_search_terms": len(all_search_terms)
        }
        
    except Exception as e:
        logger.error(f"Failed to get search terms analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get search terms analysis: {str(e)}")


@router.get("/audience-insights/{customer_id}")
async def get_audience_insights(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get audience insights and demographics data"""
    try:
        brand_id = user_data["uid"]
        
        # Get insights from Firestore
        from firebase.config import get_db
        db = get_db()
        
        insights_doc = db.collection("google_ads_audience_insights").document(f"{brand_id}_{customer_id}").get()
        
        if not insights_doc.exists:
            raise HTTPException(status_code=404, detail="Audience insights not found. Please sync your account first.")
        
        insights_data = insights_doc.to_dict()
        
        return {
            "insights": insights_data["insights"],
            "last_updated": insights_data["synced_at"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get audience insights: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get audience insights: {str(e)}")


# Import datetime for recommendations endpoint
from datetime import datetime
from api.services.google_ads_automation_service import GoogleAdsAutomationService
from api.services.google_ads_ai_optimization_engine import GoogleAdsAIOptimizationEngine


@router.post("/automation/optimize/{customer_id}")
async def run_automated_optimization(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Run automated optimization for an account"""
    try:
        brand_id = user_data["uid"]
        automation_service = GoogleAdsAutomationService()

        result = automation_service.run_automated_optimization(brand_id, customer_id)

        return result

    except Exception as e:
        logger.error(f"Failed to run automated optimization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to run automated optimization: {str(e)}")


@router.get("/automation/history/{customer_id}")
async def get_optimization_history(
    customer_id: str,
    limit: int = Query(10, description="Number of optimization records to return"),
    user_data = Depends(require_role("brand"))
):
    """Get optimization history for an account"""
    try:
        brand_id = user_data["uid"]
        automation_service = GoogleAdsAutomationService()

        history = automation_service.get_optimization_history(brand_id, customer_id, limit)

        return {
            "optimization_history": history,
            "total_records": len(history)
        }

    except Exception as e:
        logger.error(f"Failed to get optimization history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get optimization history: {str(e)}")


@router.post("/ai/optimize/{customer_id}")
async def run_ai_optimization(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Run AI-powered optimization for an account"""
    try:
        brand_id = user_data["uid"]
        ai_engine = GoogleAdsAIOptimizationEngine()

        result = ai_engine.run_ai_optimization(brand_id, customer_id)

        return result

    except Exception as e:
        logger.error(f"Failed to run AI optimization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to run AI optimization: {str(e)}")


@router.get("/ai/insights/{customer_id}")
async def get_ai_insights(
    customer_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get AI-powered insights and recommendations"""
    try:
        brand_id = user_data["uid"]

        # Get latest AI optimization results from Firestore
        from firebase.config import get_db
        db = get_db()

        query = (db.collection("google_ads_ai_optimizations")
                .where("brand_id", "==", brand_id)
                .where("customer_id", "==", customer_id)
                .order_by("created_at", direction="DESCENDING")
                .limit(1))

        docs = list(query.stream())

        if not docs:
            # Generate fresh insights if none exist
            ai_engine = GoogleAdsAIOptimizationEngine()
            result = ai_engine.run_ai_optimization(brand_id, customer_id)
            return result

        latest_doc = docs[0]
        data = latest_doc.to_dict()

        return {
            "ai_insights": data["ai_optimization_results"],
            "generated_at": data["created_at"],
            "model_version": data.get("model_version", "AdMesh AI v2.1")
        }

    except Exception as e:
        logger.error(f"Failed to get AI insights: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI insights: {str(e)}")
