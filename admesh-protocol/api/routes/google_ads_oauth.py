"""
Google Ads OAuth API Routes for AdMesh
Provides endpoints for OAuth 2.0 flow with Google Ads
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from auth.deps import require_role
from api.services.google_ads_oauth_service import GoogleAdsOAuthService

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic Models
class OAuthInitiateResponse(BaseModel):
    auth_url: str
    state: str
    message: str


class OAuthCallbackRequest(BaseModel):
    code: str = Field(..., description="Authorization code from Google")
    state: str = Field(..., description="State parameter for security")


class OAuthCallbackResponse(BaseModel):
    success: bool
    brand_id: Optional[str] = None
    user_info: Optional[Dict[str, Any]] = None
    ads_accounts: Optional[list] = None
    message: str


class ConnectionStatusResponse(BaseModel):
    connected: bool
    user_info: Optional[Dict[str, Any]] = None
    ads_accounts: Optional[list] = None
    connected_at: Optional[str] = None
    last_sync: Optional[str] = None
    token_expires_at: Optional[str] = None
    token_valid: Optional[bool] = None
    message: Optional[str] = None
    error: Optional[str] = None


@router.post("/initiate", response_model=OAuthInitiateResponse)
async def initiate_oauth(
    user_data = Depends(require_role("brand"))
):
    """Initiate OAuth flow for Google Ads"""
    try:
        brand_id = user_data["uid"]
        oauth_service = GoogleAdsOAuthService()
        
        auth_data = oauth_service.generate_auth_url(brand_id)
        
        return OAuthInitiateResponse(
            auth_url=auth_data["auth_url"],
            state=auth_data["state"],
            message="OAuth flow initiated. Please complete authorization in the opened window."
        )
        
    except Exception as e:
        logger.error(f"Failed to initiate OAuth: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth: {str(e)}")


@router.post("/callback", response_model=OAuthCallbackResponse)
async def handle_oauth_callback(
    request: OAuthCallbackRequest
):
    """Handle OAuth callback from Google"""
    try:
        oauth_service = GoogleAdsOAuthService()
        
        result = oauth_service.handle_oauth_callback(request.code, request.state)
        
        return OAuthCallbackResponse(**result)
        
    except ValueError as e:
        logger.error(f"OAuth callback validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"OAuth callback failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"OAuth callback failed: {str(e)}")


@router.get("/status", response_model=ConnectionStatusResponse)
async def get_connection_status(
    user_data = Depends(require_role("brand"))
):
    """Get Google Ads OAuth connection status"""
    try:
        brand_id = user_data["uid"]
        oauth_service = GoogleAdsOAuthService()
        
        status = oauth_service.get_connection_status(brand_id)
        
        return ConnectionStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Failed to get connection status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get connection status: {str(e)}")


@router.post("/disconnect")
async def disconnect_account(
    user_data = Depends(require_role("brand"))
):
    """Disconnect Google Ads account"""
    try:
        brand_id = user_data["uid"]
        oauth_service = GoogleAdsOAuthService()
        
        success = oauth_service.disconnect_account(brand_id)
        
        if success:
            return {
                "success": True,
                "message": "Google Ads account disconnected successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to disconnect account")
        
    except Exception as e:
        logger.error(f"Failed to disconnect account: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to disconnect account: {str(e)}")


@router.post("/refresh-token")
async def refresh_access_token(
    user_data = Depends(require_role("brand"))
):
    """Refresh access token"""
    try:
        brand_id = user_data["uid"]
        oauth_service = GoogleAdsOAuthService()
        
        new_token = oauth_service.refresh_access_token(brand_id)
        
        if new_token:
            return {
                "success": True,
                "message": "Access token refreshed successfully"
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to refresh token")
        
    except Exception as e:
        logger.error(f"Failed to refresh token: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh token: {str(e)}")


# Callback endpoint for frontend redirect (GET method)
@router.get("/callback")
async def oauth_callback_redirect(
    code: str = Query(..., description="Authorization code"),
    state: str = Query(..., description="State parameter"),
    error: Optional[str] = Query(None, description="Error parameter")
):
    """Handle OAuth callback redirect from Google (GET method for frontend)"""
    try:
        if error:
            logger.error(f"OAuth error: {error}")
            # Redirect to frontend with error
            return {
                "error": error,
                "message": "OAuth authorization failed"
            }
        
        oauth_service = GoogleAdsOAuthService()
        result = oauth_service.handle_oauth_callback(code, state)
        
        # In a real implementation, you would redirect to the frontend
        # with success parameters or store the result and redirect to a success page
        return {
            "success": True,
            "message": "OAuth completed successfully",
            "redirect_url": f"/dashboard/brand/google-ads?connected=true"
        }
        
    except Exception as e:
        logger.error(f"OAuth callback redirect failed: {str(e)}")
        return {
            "error": str(e),
            "message": "OAuth callback failed",
            "redirect_url": f"/dashboard/brand/google-ads?error={str(e)}"
        }
