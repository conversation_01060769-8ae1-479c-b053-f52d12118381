"""
Google Ads Data Sync Service for AdMesh
Handles syncing and managing Google Ads account data
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from firebase.config import get_db
from api.services.google_ads_mock_data_service import GoogleAdsMockDataService
from api.services.google_ads_oauth_service import GoogleAdsOAuthService

logger = logging.getLogger(__name__)
db = get_db()


class GoogleAdsDataSyncService:
    """Service class for syncing Google Ads data"""
    
    def __init__(self):
        """Initialize data sync service"""
        self.mock_service = GoogleAdsMockDataService()
        self.oauth_service = GoogleAdsOAuthService()
    
    def sync_account_data(self, brand_id: str, force_refresh: bool = False) -> Dict[str, Any]:
        """Sync all Google Ads account data for a brand"""
        try:
            # Check OAuth connection
            connection_status = self.oauth_service.get_connection_status(brand_id)
            if not connection_status.get("connected"):
                raise ValueError("Google Ads account not connected")
            
            # Check if we need to sync (don't sync more than once per hour unless forced)
            if not force_refresh:
                last_sync = self._get_last_sync_time(brand_id)
                if last_sync and datetime.utcnow() - last_sync < timedelta(hours=1):
                    logger.info(f"Skipping sync for brand {brand_id} - synced recently")
                    return {"status": "skipped", "message": "Data synced recently"}
            
            ads_accounts = connection_status.get("ads_accounts", [])
            sync_results = []
            
            for account in ads_accounts:
                customer_id = account["customer_id"]
                result = self._sync_customer_data(brand_id, customer_id)
                sync_results.append(result)
            
            # Update last sync time
            self._update_last_sync_time(brand_id)
            
            logger.info(f"Data sync completed for brand {brand_id}")
            
            return {
                "status": "success",
                "synced_accounts": len(sync_results),
                "results": sync_results,
                "synced_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Data sync failed for brand {brand_id}: {str(e)}")
            raise
    
    def _sync_customer_data(self, brand_id: str, customer_id: str) -> Dict[str, Any]:
        """Sync data for a specific customer account"""
        try:
            # Generate mock account data
            account_data = self.mock_service.generate_account_data(customer_id)
            
            # Generate campaigns data
            campaigns_data = self.mock_service.generate_campaigns_data(customer_id, count=8)
            
            # Store account data
            account_doc_id = f"{brand_id}_{customer_id}"
            db.collection("google_ads_accounts").document(account_doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "account_data": account_data,
                "synced_at": datetime.utcnow(),
                "status": "active"
            })
            
            # Store campaigns data
            campaigns_synced = 0
            for campaign in campaigns_data:
                campaign_doc_id = f"{brand_id}_{customer_id}_{campaign['campaign_id']}"
                
                # Generate ad groups for this campaign
                ad_groups = self.mock_service.generate_ad_groups_data(campaign["campaign_id"], count=3)
                
                # Generate keywords and ads for each ad group
                for ad_group in ad_groups:
                    ad_group["keywords"] = self.mock_service.generate_keywords_data(ad_group["ad_group_id"], count=12)
                    ad_group["ads"] = self.mock_service.generate_ads_data(ad_group["ad_group_id"], count=3)
                    ad_group["search_terms"] = self.mock_service.generate_search_terms_data(ad_group["ad_group_id"], count=15)
                
                campaign["ad_groups"] = ad_groups
                
                # Store campaign data
                db.collection("google_ads_campaigns_data").document(campaign_doc_id).set({
                    "brand_id": brand_id,
                    "customer_id": customer_id,
                    "campaign_data": campaign,
                    "synced_at": datetime.utcnow()
                })
                
                campaigns_synced += 1
            
            # Generate and store historical performance data
            historical_data = self.mock_service.generate_historical_performance(days=90)
            db.collection("google_ads_performance_history").document(account_doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "historical_data": historical_data,
                "synced_at": datetime.utcnow()
            })
            
            # Generate and store audience insights
            audience_insights = self.mock_service.generate_audience_insights(customer_id)
            db.collection("google_ads_audience_insights").document(account_doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "insights": audience_insights,
                "synced_at": datetime.utcnow()
            })
            
            return {
                "customer_id": customer_id,
                "campaigns_synced": campaigns_synced,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Failed to sync customer {customer_id}: {str(e)}")
            return {
                "customer_id": customer_id,
                "status": "error",
                "error": str(e)
            }
    
    def get_account_overview(self, brand_id: str) -> Dict[str, Any]:
        """Get account overview data"""
        try:
            # Get all accounts for this brand
            accounts_query = db.collection("google_ads_accounts").where("brand_id", "==", brand_id)
            accounts = list(accounts_query.stream())
            
            if not accounts:
                return {"status": "no_data", "message": "No Google Ads data found"}
            
            overview = {
                "total_accounts": len(accounts),
                "accounts": [],
                "aggregate_performance": {
                    "total_impressions": 0,
                    "total_clicks": 0,
                    "total_cost_micros": 0,
                    "total_conversions": 0
                }
            }
            
            for account_doc in accounts:
                account_data = account_doc.to_dict()
                customer_id = account_data["customer_id"]
                
                # Get campaigns for this account
                campaigns_query = db.collection("google_ads_campaigns_data").where("customer_id", "==", customer_id)
                campaigns = list(campaigns_query.stream())
                
                account_performance = {
                    "impressions": 0,
                    "clicks": 0,
                    "cost_micros": 0,
                    "conversions": 0,
                    "campaigns_count": len(campaigns)
                }
                
                # Aggregate performance from campaigns
                for campaign_doc in campaigns:
                    campaign_data = campaign_doc.to_dict()["campaign_data"]
                    perf = campaign_data.get("performance", {})
                    
                    account_performance["impressions"] += perf.get("impressions", 0)
                    account_performance["clicks"] += perf.get("clicks", 0)
                    account_performance["cost_micros"] += perf.get("cost_micros", 0)
                    account_performance["conversions"] += perf.get("conversions", 0)
                
                # Calculate derived metrics
                if account_performance["impressions"] > 0:
                    account_performance["ctr"] = account_performance["clicks"] / account_performance["impressions"]
                if account_performance["clicks"] > 0:
                    account_performance["average_cpc"] = account_performance["cost_micros"] / account_performance["clicks"]
                    account_performance["conversion_rate"] = account_performance["conversions"] / account_performance["clicks"]
                
                overview["accounts"].append({
                    "customer_id": customer_id,
                    "account_name": account_data["account_data"]["descriptive_name"],
                    "performance": account_performance,
                    "last_synced": account_data["synced_at"]
                })
                
                # Add to aggregate
                agg = overview["aggregate_performance"]
                agg["total_impressions"] += account_performance["impressions"]
                agg["total_clicks"] += account_performance["clicks"]
                agg["total_cost_micros"] += account_performance["cost_micros"]
                agg["total_conversions"] += account_performance["conversions"]
            
            # Calculate aggregate derived metrics
            agg = overview["aggregate_performance"]
            if agg["total_impressions"] > 0:
                agg["overall_ctr"] = agg["total_clicks"] / agg["total_impressions"]
            if agg["total_clicks"] > 0:
                agg["overall_average_cpc"] = agg["total_cost_micros"] / agg["total_clicks"]
                agg["overall_conversion_rate"] = agg["total_conversions"] / agg["total_clicks"]
            
            return overview
            
        except Exception as e:
            logger.error(f"Failed to get account overview: {str(e)}")
            raise
    
    def get_campaigns_data(self, brand_id: str, customer_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get campaigns data for a brand"""
        try:
            query = db.collection("google_ads_campaigns_data").where("brand_id", "==", brand_id)
            
            if customer_id:
                query = query.where("customer_id", "==", customer_id)
            
            campaigns_docs = list(query.stream())
            campaigns = []
            
            for doc in campaigns_docs:
                data = doc.to_dict()
                campaigns.append(data["campaign_data"])
            
            return campaigns
            
        except Exception as e:
            logger.error(f"Failed to get campaigns data: {str(e)}")
            raise
    
    def get_real_time_metrics(self, brand_id: str, customer_id: str) -> Dict[str, Any]:
        """Get real-time metrics for an account"""
        try:
            # Generate real-time mock data
            real_time_data = self.mock_service.generate_real_time_metrics(customer_id)
            
            # Store in cache collection for quick access
            cache_doc_id = f"{brand_id}_{customer_id}_realtime"
            db.collection("google_ads_realtime_cache").document(cache_doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "data": real_time_data,
                "generated_at": datetime.utcnow()
            })
            
            return real_time_data
            
        except Exception as e:
            logger.error(f"Failed to get real-time metrics: {str(e)}")
            raise
    
    def get_recommendations(self, brand_id: str, customer_id: str) -> List[Dict[str, Any]]:
        """Get optimization recommendations"""
        try:
            recommendations = self.mock_service.generate_recommendations(customer_id)
            
            # Store recommendations
            rec_doc_id = f"{brand_id}_{customer_id}_recommendations"
            db.collection("google_ads_recommendations").document(rec_doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "recommendations": recommendations,
                "generated_at": datetime.utcnow()
            })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to get recommendations: {str(e)}")
            raise
    
    def _get_last_sync_time(self, brand_id: str) -> Optional[datetime]:
        """Get last sync time for a brand"""
        try:
            sync_doc = db.collection("google_ads_sync_status").document(brand_id).get()
            if sync_doc.exists:
                return sync_doc.to_dict().get("last_sync")
            return None
        except Exception:
            return None
    
    def _update_last_sync_time(self, brand_id: str):
        """Update last sync time for a brand"""
        try:
            db.collection("google_ads_sync_status").document(brand_id).set({
                "brand_id": brand_id,
                "last_sync": datetime.utcnow(),
                "status": "completed"
            })
        except Exception as e:
            logger.error(f"Failed to update sync time: {str(e)}")
