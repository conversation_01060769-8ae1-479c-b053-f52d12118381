"""
Google Ads Automation Service for AdMesh
Provides automated campaign management and optimization
"""

import logging
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from firebase.config import get_db
from api.services.google_ads_data_sync_service import GoogleAdsDataSyncService

logger = logging.getLogger(__name__)
db = get_db()


class GoogleAdsAutomationService:
    """Service class for automated Google Ads management"""
    
    def __init__(self):
        """Initialize automation service"""
        self.sync_service = GoogleAdsDataSyncService()
        
        # Automation thresholds and rules
        self.bid_adjustment_threshold = 0.1  # 10% performance change
        self.budget_reallocation_threshold = 0.2  # 20% performance difference
        self.keyword_quality_threshold = 5  # Minimum quality score
        self.ctr_threshold = 0.02  # 2% minimum CTR
        self.conversion_rate_threshold = 0.01  # 1% minimum conversion rate
    
    def run_automated_optimization(self, brand_id: str, customer_id: str) -> Dict[str, Any]:
        """Run comprehensive automated optimization"""
        try:
            optimization_results = {
                "timestamp": datetime.utcnow().isoformat(),
                "brand_id": brand_id,
                "customer_id": customer_id,
                "optimizations": []
            }
            
            # Get campaign data
            campaigns = self.sync_service.get_campaigns_data(brand_id, customer_id)
            
            if not campaigns:
                return {
                    "status": "no_data",
                    "message": "No campaign data found for optimization"
                }
            
            # Run different optimization strategies
            bid_optimizations = self._optimize_bids(campaigns)
            budget_optimizations = self._optimize_budgets(campaigns)
            keyword_optimizations = self._optimize_keywords(campaigns)
            ad_optimizations = self._optimize_ads(campaigns)
            
            optimization_results["optimizations"].extend([
                {"type": "bid_optimization", "results": bid_optimizations},
                {"type": "budget_optimization", "results": budget_optimizations},
                {"type": "keyword_optimization", "results": keyword_optimizations},
                {"type": "ad_optimization", "results": ad_optimizations}
            ])
            
            # Store optimization results
            self._store_optimization_results(brand_id, customer_id, optimization_results)
            
            # Generate summary
            total_changes = sum(len(opt["results"]) for opt in optimization_results["optimizations"])
            
            return {
                "status": "completed",
                "total_optimizations": total_changes,
                "results": optimization_results,
                "message": f"Applied {total_changes} automated optimizations"
            }
            
        except Exception as e:
            logger.error(f"Automated optimization failed: {str(e)}")
            raise
    
    def _optimize_bids(self, campaigns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize keyword and ad group bids based on performance"""
        bid_optimizations = []
        
        for campaign in campaigns:
            for ad_group in campaign.get("ad_groups", []):
                for keyword in ad_group.get("keywords", []):
                    optimization = self._analyze_keyword_bid(keyword, ad_group, campaign)
                    if optimization:
                        bid_optimizations.append(optimization)
        
        return bid_optimizations
    
    def _analyze_keyword_bid(self, keyword: Dict[str, Any], ad_group: Dict[str, Any], campaign: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze and suggest bid changes for a keyword"""
        perf = keyword.get("performance", {})
        current_bid = keyword.get("cpc_bid_micros", 1000000)
        
        ctr = perf.get("ctr", 0)
        conversion_rate = perf.get("conversion_rate", 0)
        average_cpc = perf.get("average_cpc", 0)
        impressions = perf.get("impressions", 0)
        
        # Skip if insufficient data
        if impressions < 100:
            return None
        
        # Calculate performance score
        performance_score = (ctr * 100) + (conversion_rate * 1000)
        
        # Determine bid adjustment
        if performance_score > 15:  # High performing keyword
            new_bid = int(current_bid * 1.2)  # Increase bid by 20%
            action = "increase"
            reason = "High performance - increasing bid to capture more traffic"
        elif performance_score < 3:  # Low performing keyword
            new_bid = int(current_bid * 0.8)  # Decrease bid by 20%
            action = "decrease"
            reason = "Low performance - decreasing bid to reduce costs"
        elif average_cpc > 0 and average_cpc > current_bid * 1.5:  # CPC too high
            new_bid = int(current_bid * 0.9)  # Decrease bid by 10%
            action = "decrease"
            reason = "Average CPC exceeds bid - reducing to control costs"
        else:
            return None  # No change needed
        
        return {
            "keyword_id": keyword.get("keyword_id"),
            "keyword_text": keyword.get("keyword_text"),
            "campaign_name": campaign.get("campaign_name"),
            "ad_group_name": ad_group.get("ad_group_name"),
            "current_bid_micros": current_bid,
            "new_bid_micros": new_bid,
            "action": action,
            "reason": reason,
            "performance_score": round(performance_score, 2),
            "expected_impact": self._calculate_bid_impact(action, perf)
        }
    
    def _calculate_bid_impact(self, action: str, performance: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate expected impact of bid changes"""
        current_clicks = performance.get("clicks", 0)
        current_cost = performance.get("cost_micros", 0)
        
        if action == "increase":
            return {
                "clicks_change": f"+{int(current_clicks * 0.15)}",
                "cost_change": f"+${(current_cost * 0.2) / 1000000:.2f}",
                "impression_share_change": "+5-10%"
            }
        else:
            return {
                "clicks_change": f"-{int(current_clicks * 0.1)}",
                "cost_change": f"-${(current_cost * 0.15) / 1000000:.2f}",
                "impression_share_change": "-3-7%"
            }
    
    def _optimize_budgets(self, campaigns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize budget allocation across campaigns"""
        budget_optimizations = []
        
        # Calculate performance metrics for each campaign
        campaign_performance = []
        for campaign in campaigns:
            perf = campaign.get("performance", {})
            budget = campaign.get("budget", {}).get("amount_micros", 0)
            
            if budget > 0 and perf.get("cost_micros", 0) > 0:
                roas = (perf.get("conversions", 0) * 50000000) / perf.get("cost_micros", 1)  # Assume $50 per conversion
                efficiency = perf.get("conversions", 0) / (budget / 1000000)  # Conversions per dollar
                
                campaign_performance.append({
                    "campaign": campaign,
                    "roas": roas,
                    "efficiency": efficiency,
                    "current_budget": budget
                })
        
        # Sort by performance
        campaign_performance.sort(key=lambda x: x["efficiency"], reverse=True)
        
        # Suggest budget reallocations
        if len(campaign_performance) >= 2:
            top_performer = campaign_performance[0]
            bottom_performer = campaign_performance[-1]
            
            if top_performer["efficiency"] > bottom_performer["efficiency"] * 2:
                # Reallocate budget from bottom to top performer
                reallocation_amount = int(bottom_performer["current_budget"] * 0.2)
                
                budget_optimizations.append({
                    "type": "budget_reallocation",
                    "from_campaign": bottom_performer["campaign"]["campaign_name"],
                    "to_campaign": top_performer["campaign"]["campaign_name"],
                    "amount_micros": reallocation_amount,
                    "reason": f"Reallocating budget from low-efficiency campaign (efficiency: {bottom_performer['efficiency']:.2f}) to high-efficiency campaign (efficiency: {top_performer['efficiency']:.2f})",
                    "expected_impact": {
                        "additional_conversions": int(reallocation_amount / 1000000 * top_performer["efficiency"]),
                        "cost_savings": f"${reallocation_amount / 1000000:.2f}"
                    }
                })
        
        return budget_optimizations
    
    def _optimize_keywords(self, campaigns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize keywords - suggest additions, pausing, and negative keywords"""
        keyword_optimizations = []
        
        for campaign in campaigns:
            for ad_group in campaign.get("ad_groups", []):
                # Analyze search terms for keyword opportunities
                search_terms = ad_group.get("search_terms", [])
                
                for search_term in search_terms:
                    if not search_term.get("added_as_keyword", False):
                        perf = search_term.get("performance", {})
                        
                        if perf.get("ctr", 0) > 0.03 and perf.get("conversion_rate", 0) > 0.02:
                            # High-performing search term - suggest as new keyword
                            keyword_optimizations.append({
                                "type": "add_keyword",
                                "campaign_name": campaign["campaign_name"],
                                "ad_group_name": ad_group["ad_group_name"],
                                "keyword_text": search_term["search_term"],
                                "match_type": "PHRASE",
                                "suggested_bid_micros": 1500000,  # $1.50
                                "reason": f"High-performing search term (CTR: {perf.get('ctr', 0):.3f}, Conv Rate: {perf.get('conversion_rate', 0):.3f})",
                                "expected_impact": {
                                    "additional_clicks": int(perf.get("clicks", 0) * 1.5),
                                    "additional_conversions": round(perf.get("conversions", 0) * 1.3, 2)
                                }
                            })
                        elif perf.get("clicks", 0) > 10 and perf.get("ctr", 0) < 0.005:
                            # Poor-performing search term - suggest as negative keyword
                            keyword_optimizations.append({
                                "type": "add_negative_keyword",
                                "campaign_name": campaign["campaign_name"],
                                "ad_group_name": ad_group["ad_group_name"],
                                "keyword_text": search_term["search_term"],
                                "match_type": "PHRASE",
                                "reason": f"Poor-performing search term (CTR: {perf.get('ctr', 0):.3f}, {perf.get('clicks', 0)} clicks with no conversions)",
                                "expected_impact": {
                                    "cost_savings": f"${perf.get('cost_micros', 0) / 1000000:.2f}",
                                    "improved_relevance": "Higher overall campaign relevance"
                                }
                            })
                
                # Analyze existing keywords for pausing
                keywords = ad_group.get("keywords", [])
                for keyword in keywords:
                    perf = keyword.get("performance", {})
                    
                    if (perf.get("impressions", 0) > 1000 and 
                        perf.get("ctr", 0) < 0.01 and 
                        perf.get("conversions", 0) == 0):
                        
                        keyword_optimizations.append({
                            "type": "pause_keyword",
                            "campaign_name": campaign["campaign_name"],
                            "ad_group_name": ad_group["ad_group_name"],
                            "keyword_id": keyword["keyword_id"],
                            "keyword_text": keyword["keyword_text"],
                            "reason": f"Underperforming keyword (CTR: {perf.get('ctr', 0):.3f}, no conversions after {perf.get('impressions', 0)} impressions)",
                            "expected_impact": {
                                "cost_savings": f"${perf.get('cost_micros', 0) / 1000000:.2f}",
                                "budget_reallocation": "Budget available for better-performing keywords"
                            }
                        })
        
        return keyword_optimizations
    
    def _optimize_ads(self, campaigns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize ad copy and suggest new ad variations"""
        ad_optimizations = []
        
        for campaign in campaigns:
            for ad_group in campaign.get("ad_groups", []):
                ads = ad_group.get("ads", [])
                
                if len(ads) < 3:
                    # Suggest creating more ad variations
                    ad_optimizations.append({
                        "type": "create_ad_variation",
                        "campaign_name": campaign["campaign_name"],
                        "ad_group_name": ad_group["ad_group_name"],
                        "current_ads_count": len(ads),
                        "suggested_headlines": [
                            "Boost Your Business Results",
                            "Get More Customers Today",
                            "Professional Solutions That Work"
                        ],
                        "suggested_descriptions": [
                            "Proven strategies to grow your business",
                            "Expert help to increase your revenue"
                        ],
                        "reason": "Insufficient ad variations for optimal testing",
                        "expected_impact": {
                            "ctr_improvement": "5-15%",
                            "additional_clicks": "10-25%"
                        }
                    })
                
                # Analyze ad performance
                for ad in ads:
                    perf = ad.get("performance", {})
                    
                    if (perf.get("impressions", 0) > 1000 and 
                        perf.get("ctr", 0) < 0.015):
                        
                        ad_optimizations.append({
                            "type": "pause_underperforming_ad",
                            "campaign_name": campaign["campaign_name"],
                            "ad_group_name": ad_group["ad_group_name"],
                            "ad_id": ad["ad_id"],
                            "current_ctr": perf.get("ctr", 0),
                            "reason": f"Low CTR ({perf.get('ctr', 0):.3f}) after {perf.get('impressions', 0)} impressions",
                            "expected_impact": {
                                "improved_ad_group_ctr": "3-8%",
                                "better_quality_score": "Improved relevance signals"
                            }
                        })
        
        return ad_optimizations
    
    def _store_optimization_results(self, brand_id: str, customer_id: str, results: Dict[str, Any]):
        """Store optimization results in Firestore"""
        try:
            doc_id = f"{brand_id}_{customer_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            db.collection("google_ads_optimizations").document(doc_id).set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "optimization_results": results,
                "created_at": datetime.utcnow(),
                "status": "completed"
            })
            
            # Update latest optimization reference
            db.collection("google_ads_optimization_latest").document(f"{brand_id}_{customer_id}").set({
                "brand_id": brand_id,
                "customer_id": customer_id,
                "latest_optimization_id": doc_id,
                "last_optimized": datetime.utcnow(),
                "total_optimizations": sum(len(opt["results"]) for opt in results["optimizations"])
            })
            
        except Exception as e:
            logger.error(f"Failed to store optimization results: {str(e)}")
    
    def get_optimization_history(self, brand_id: str, customer_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get optimization history for an account"""
        try:
            query = (db.collection("google_ads_optimizations")
                    .where("brand_id", "==", brand_id)
                    .where("customer_id", "==", customer_id)
                    .order_by("created_at", direction="DESCENDING")
                    .limit(limit))
            
            docs = list(query.stream())
            
            history = []
            for doc in docs:
                data = doc.to_dict()
                history.append({
                    "optimization_id": doc.id,
                    "timestamp": data["created_at"],
                    "total_optimizations": sum(len(opt["results"]) for opt in data["optimization_results"]["optimizations"]),
                    "optimization_types": [opt["type"] for opt in data["optimization_results"]["optimizations"]],
                    "status": data["status"]
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get optimization history: {str(e)}")
            return []
