"""
Google Ads Service for AdMesh
Handles Google Ads API operations including campaign management, ad groups, and keywords
"""

import os
import logging
from typing import Dict, Any, List, Optional
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from config.config_manager import get_config

logger = logging.getLogger(__name__)


class GoogleAdsService:
    """Service class for Google Ads API operations"""
    
    def __init__(self):
        """Initialize Google Ads client with configuration"""
        self.config = get_config()
        self.google_ads_config = self.config.google_ads_config
        self._client = None
        
    @property
    def client(self) -> GoogleAdsClient:
        """Get or create Google Ads client"""
        if self._client is None:
            try:
                # Create client configuration
                client_config = {
                    "developer_token": self.google_ads_config["developer_token"],
                    "client_id": self.google_ads_config["client_id"],
                    "client_secret": self.google_ads_config["client_secret"],
                    "refresh_token": self.google_ads_config["refresh_token"],
                    "login_customer_id": self.google_ads_config["login_customer_id"],
                    "use_proto_plus": self.google_ads_config["use_proto_plus"]
                }
                
                self._client = GoogleAdsClient.load_from_dict(client_config)
                logger.info("Google Ads client initialized successfully")
                
            except Exception as e:
                logger.error(f"Failed to initialize Google Ads client: {str(e)}")
                raise
                
        return self._client
    
    def validate_customer_access(self, customer_id: str) -> bool:
        """Validate that we have access to the customer account"""
        try:
            customer_service = self.client.get_service("CustomerService")
            customer_resource_name = customer_service.customer_path(customer_id)
            
            # Try to get customer info to validate access
            request = self.client.get_type("GetCustomerRequest")
            request.resource_name = customer_resource_name
            
            customer = customer_service.get_customer(request=request)
            logger.info(f"Successfully validated access to customer {customer_id}")
            return True
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to validate customer access for {customer_id}: {ex}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error validating customer access: {str(e)}")
            return False
    
    def create_campaign(self, customer_id: str, campaign_data: Dict[str, Any]) -> Optional[str]:
        """Create a new Google Ads campaign"""
        try:
            campaign_service = self.client.get_service("CampaignService")
            campaign_operation = self.client.get_type("CampaignOperation")
            
            # Create campaign
            campaign = campaign_operation.create
            campaign.name = campaign_data["name"]
            campaign.status = self.client.enums.CampaignStatusEnum.PAUSED  # Start paused
            
            # Set campaign type (Search campaigns for now)
            campaign.advertising_channel_type = (
                self.client.enums.AdvertisingChannelTypeEnum.SEARCH
            )
            
            # Set bidding strategy
            campaign.manual_cpc.enhanced_cpc_enabled = True
            
            # Set budget
            campaign.campaign_budget = campaign_data["budget_resource_name"]
            
            # Set network settings
            campaign.network_settings.target_google_search = True
            campaign.network_settings.target_search_network = True
            campaign.network_settings.target_content_network = False
            campaign.network_settings.target_partner_search_network = False
            
            # Execute the operation
            response = campaign_service.mutate_campaigns(
                customer_id=customer_id, operations=[campaign_operation]
            )
            
            campaign_resource_name = response.results[0].resource_name
            logger.info(f"Created campaign: {campaign_resource_name}")
            
            return campaign_resource_name
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to create campaign: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating campaign: {str(e)}")
            return None
    
    def create_campaign_budget(self, customer_id: str, budget_data: Dict[str, Any]) -> Optional[str]:
        """Create a campaign budget"""
        try:
            budget_service = self.client.get_service("CampaignBudgetService")
            budget_operation = self.client.get_type("CampaignBudgetOperation")
            
            # Create budget
            budget = budget_operation.create
            budget.name = budget_data["name"]
            budget.amount_micros = budget_data["amount_micros"]  # Amount in micros (1 USD = 1,000,000 micros)
            budget.delivery_method = self.client.enums.BudgetDeliveryMethodEnum.STANDARD
            
            # Execute the operation
            response = budget_service.mutate_campaign_budgets(
                customer_id=customer_id, operations=[budget_operation]
            )
            
            budget_resource_name = response.results[0].resource_name
            logger.info(f"Created budget: {budget_resource_name}")
            
            return budget_resource_name
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to create budget: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating budget: {str(e)}")
            return None
    
    def create_ad_group(self, customer_id: str, ad_group_data: Dict[str, Any]) -> Optional[str]:
        """Create an ad group within a campaign"""
        try:
            ad_group_service = self.client.get_service("AdGroupService")
            ad_group_operation = self.client.get_type("AdGroupOperation")
            
            # Create ad group
            ad_group = ad_group_operation.create
            ad_group.name = ad_group_data["name"]
            ad_group.campaign = ad_group_data["campaign_resource_name"]
            ad_group.status = self.client.enums.AdGroupStatusEnum.ENABLED
            ad_group.type_ = self.client.enums.AdGroupTypeEnum.SEARCH_STANDARD
            
            # Set CPC bid
            ad_group.cpc_bid_micros = ad_group_data.get("cpc_bid_micros", 1000000)  # Default $1.00
            
            # Execute the operation
            response = ad_group_service.mutate_ad_groups(
                customer_id=customer_id, operations=[ad_group_operation]
            )
            
            ad_group_resource_name = response.results[0].resource_name
            logger.info(f"Created ad group: {ad_group_resource_name}")
            
            return ad_group_resource_name
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to create ad group: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating ad group: {str(e)}")
            return None
    
    def create_keywords(self, customer_id: str, ad_group_resource_name: str, keywords: List[str]) -> List[str]:
        """Add keywords to an ad group"""
        try:
            ad_group_criterion_service = self.client.get_service("AdGroupCriterionService")
            operations = []
            
            for keyword in keywords:
                ad_group_criterion_operation = self.client.get_type("AdGroupCriterionOperation")
                ad_group_criterion = ad_group_criterion_operation.create
                
                ad_group_criterion.ad_group = ad_group_resource_name
                ad_group_criterion.status = self.client.enums.AdGroupCriterionStatusEnum.ENABLED
                
                # Set keyword
                ad_group_criterion.keyword.text = keyword
                ad_group_criterion.keyword.match_type = self.client.enums.KeywordMatchTypeEnum.BROAD
                
                operations.append(ad_group_criterion_operation)
            
            # Execute the operations
            response = ad_group_criterion_service.mutate_ad_group_criteria(
                customer_id=customer_id, operations=operations
            )
            
            keyword_resource_names = [result.resource_name for result in response.results]
            logger.info(f"Created {len(keyword_resource_names)} keywords")
            
            return keyword_resource_names
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to create keywords: {ex}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error creating keywords: {str(e)}")
            return []
    
    def create_responsive_search_ad(self, customer_id: str, ad_group_resource_name: str, ad_data: Dict[str, Any]) -> Optional[str]:
        """Create a responsive search ad"""
        try:
            ad_group_ad_service = self.client.get_service("AdGroupAdService")
            ad_group_ad_operation = self.client.get_type("AdGroupAdOperation")
            
            # Create ad group ad
            ad_group_ad = ad_group_ad_operation.create
            ad_group_ad.ad_group = ad_group_resource_name
            ad_group_ad.status = self.client.enums.AdGroupAdStatusEnum.ENABLED
            
            # Create responsive search ad
            ad_group_ad.ad.responsive_search_ad.headlines.extend([
                self._create_ad_text_asset(headline) for headline in ad_data["headlines"]
            ])
            
            ad_group_ad.ad.responsive_search_ad.descriptions.extend([
                self._create_ad_text_asset(description) for description in ad_data["descriptions"]
            ])
            
            # Set final URLs
            ad_group_ad.ad.final_urls.extend(ad_data["final_urls"])
            
            # Execute the operation
            response = ad_group_ad_service.mutate_ad_group_ads(
                customer_id=customer_id, operations=[ad_group_ad_operation]
            )
            
            ad_resource_name = response.results[0].resource_name
            logger.info(f"Created responsive search ad: {ad_resource_name}")
            
            return ad_resource_name
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to create responsive search ad: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating responsive search ad: {str(e)}")
            return None
    
    def _create_ad_text_asset(self, text: str):
        """Helper method to create ad text asset"""
        ad_text_asset = self.client.get_type("AdTextAsset")
        ad_text_asset.text = text
        return ad_text_asset
    
    def get_campaign_performance(self, customer_id: str, campaign_resource_name: str) -> Optional[Dict[str, Any]]:
        """Get campaign performance metrics"""
        try:
            ga_service = self.client.get_service("GoogleAdsService")
            
            query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.cost_micros,
                    metrics.conversions,
                    metrics.ctr,
                    metrics.average_cpc
                FROM campaign
                WHERE campaign.resource_name = '{campaign_resource_name}'
            """
            
            response = ga_service.search(customer_id=customer_id, query=query)
            
            for row in response:
                return {
                    "campaign_id": row.campaign.id,
                    "campaign_name": row.campaign.name,
                    "status": row.campaign.status.name,
                    "impressions": row.metrics.impressions,
                    "clicks": row.metrics.clicks,
                    "cost_micros": row.metrics.cost_micros,
                    "conversions": row.metrics.conversions,
                    "ctr": row.metrics.ctr,
                    "average_cpc": row.metrics.average_cpc
                }
            
            return None
            
        except GoogleAdsException as ex:
            logger.error(f"Failed to get campaign performance: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting campaign performance: {str(e)}")
            return None

    def get_account_performance(self, customer_id: str, date_range: str = "LAST_30_DAYS") -> Optional[Dict[str, Any]]:
        """Get account-level performance metrics"""
        try:
            ga_service = self.client.get_service("GoogleAdsService")

            query = f"""
                SELECT
                    customer.id,
                    customer.descriptive_name,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.cost_micros,
                    metrics.conversions,
                    metrics.ctr,
                    metrics.average_cpc,
                    metrics.conversion_rate
                FROM customer
                WHERE segments.date DURING {date_range}
            """

            response = ga_service.search(customer_id=customer_id, query=query)

            total_metrics = {
                "customer_id": customer_id,
                "impressions": 0,
                "clicks": 0,
                "cost_micros": 0,
                "conversions": 0.0,
                "ctr": 0.0,
                "average_cpc": 0,
                "conversion_rate": 0.0
            }

            row_count = 0
            for row in response:
                total_metrics["impressions"] += row.metrics.impressions
                total_metrics["clicks"] += row.metrics.clicks
                total_metrics["cost_micros"] += row.metrics.cost_micros
                total_metrics["conversions"] += row.metrics.conversions
                total_metrics["ctr"] += row.metrics.ctr
                total_metrics["average_cpc"] += row.metrics.average_cpc
                total_metrics["conversion_rate"] += row.metrics.conversion_rate
                row_count += 1

            # Calculate averages for rate metrics
            if row_count > 0:
                total_metrics["ctr"] = total_metrics["ctr"] / row_count
                total_metrics["average_cpc"] = total_metrics["average_cpc"] / row_count
                total_metrics["conversion_rate"] = total_metrics["conversion_rate"] / row_count

            return total_metrics

        except GoogleAdsException as ex:
            logger.error(f"Failed to get account performance: {ex}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting account performance: {str(e)}")
            return None

    def get_campaigns_performance(self, customer_id: str, date_range: str = "LAST_30_DAYS") -> List[Dict[str, Any]]:
        """Get performance metrics for all campaigns"""
        try:
            ga_service = self.client.get_service("GoogleAdsService")

            query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    campaign.advertising_channel_type,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.cost_micros,
                    metrics.conversions,
                    metrics.ctr,
                    metrics.average_cpc,
                    metrics.conversion_rate,
                    metrics.search_impression_share,
                    metrics.search_budget_lost_impression_share
                FROM campaign
                WHERE segments.date DURING {date_range}
                ORDER BY metrics.cost_micros DESC
            """

            response = ga_service.search(customer_id=customer_id, query=query)

            campaigns = []
            for row in response:
                campaigns.append({
                    "campaign_id": row.campaign.id,
                    "campaign_name": row.campaign.name,
                    "status": row.campaign.status.name,
                    "channel_type": row.campaign.advertising_channel_type.name,
                    "impressions": row.metrics.impressions,
                    "clicks": row.metrics.clicks,
                    "cost_micros": row.metrics.cost_micros,
                    "conversions": row.metrics.conversions,
                    "ctr": row.metrics.ctr,
                    "average_cpc": row.metrics.average_cpc,
                    "conversion_rate": row.metrics.conversion_rate,
                    "impression_share": row.metrics.search_impression_share,
                    "budget_lost_impression_share": row.metrics.search_budget_lost_impression_share
                })

            return campaigns

        except GoogleAdsException as ex:
            logger.error(f"Failed to get campaigns performance: {ex}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error getting campaigns performance: {str(e)}")
            return []

    def update_campaign_status(self, customer_id: str, campaign_resource_name: str, status: str) -> bool:
        """Update campaign status (ENABLED, PAUSED, REMOVED)"""
        try:
            campaign_service = self.client.get_service("CampaignService")
            campaign_operation = self.client.get_type("CampaignOperation")

            # Update campaign
            campaign = campaign_operation.update
            campaign.resource_name = campaign_resource_name

            # Set status
            if status.upper() == "ENABLED":
                campaign.status = self.client.enums.CampaignStatusEnum.ENABLED
            elif status.upper() == "PAUSED":
                campaign.status = self.client.enums.CampaignStatusEnum.PAUSED
            elif status.upper() == "REMOVED":
                campaign.status = self.client.enums.CampaignStatusEnum.REMOVED
            else:
                raise ValueError(f"Invalid status: {status}")

            # Set field mask
            campaign_operation.update_mask.paths.append("status")

            # Execute the operation
            response = campaign_service.mutate_campaigns(
                customer_id=customer_id, operations=[campaign_operation]
            )

            logger.info(f"Updated campaign status to {status}: {campaign_resource_name}")
            return True

        except GoogleAdsException as ex:
            logger.error(f"Failed to update campaign status: {ex}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating campaign status: {str(e)}")
            return False
