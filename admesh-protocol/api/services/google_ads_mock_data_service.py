"""
Google Ads Mock Data Service for AdMesh
Provides realistic mock data that simulates Google Ads API responses
"""

import random
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import uuid
import json

logger = logging.getLogger(__name__)


class GoogleAdsMockDataService:
    """Service class for generating realistic Google Ads mock data"""
    
    def __init__(self):
        """Initialize mock data service"""
        self.campaign_names = [
            "Brand Awareness Campaign",
            "Product Launch 2024",
            "Holiday Sales Drive",
            "Lead Generation Campaign",
            "Retargeting Campaign",
            "Competitor Targeting",
            "Local Business Promotion",
            "Mobile App Install",
            "Video Marketing Campaign",
            "Search Network Campaign"
        ]
        
        self.keywords = [
            "digital marketing", "online advertising", "ppc management", "google ads",
            "social media marketing", "content marketing", "email marketing", "seo services",
            "web design", "ecommerce", "lead generation", "conversion optimization",
            "brand awareness", "customer acquisition", "marketing automation", "analytics"
        ]
        
        self.ad_headlines = [
            "Boost Your Business Today",
            "Get More Customers Now",
            "Increase Sales by 300%",
            "Professional Marketing Solutions",
            "Grow Your Revenue Fast",
            "Expert Digital Marketing",
            "Transform Your Business",
            "Maximize Your ROI"
        ]
        
        self.ad_descriptions = [
            "Professional marketing services that deliver results",
            "Proven strategies to grow your business online",
            "Get more leads and customers with our expert help",
            "Increase your revenue with targeted advertising"
        ]
    
    def generate_account_data(self, customer_id: str) -> Dict[str, Any]:
        """Generate mock account data"""
        return {
            "customer_id": customer_id,
            "descriptive_name": f"Business Account {customer_id[-4:]}",
            "currency_code": "USD",
            "time_zone": random.choice(["America/New_York", "America/Los_Angeles", "America/Chicago"]),
            "status": "ENABLED",
            "account_type": "STANDARD",
            "manager": False,
            "auto_tagging_enabled": True,
            "test_account": False,
            "created_date": (datetime.now() - timedelta(days=random.randint(30, 365))).isoformat()
        }
    
    def generate_campaigns_data(self, customer_id: str, count: int = 5) -> List[Dict[str, Any]]:
        """Generate mock campaigns data"""
        campaigns = []
        
        for i in range(count):
            campaign_id = str(random.randint(*********, *********))
            created_date = datetime.now() - timedelta(days=random.randint(1, 90))
            
            # Generate realistic performance metrics
            impressions = random.randint(1000, 50000)
            clicks = int(impressions * random.uniform(0.01, 0.08))  # 1-8% CTR
            cost_micros = clicks * random.randint(500000, 5000000)  # $0.50-$5.00 CPC
            conversions = clicks * random.uniform(0.01, 0.15)  # 1-15% conversion rate
            
            campaign = {
                "campaign_id": campaign_id,
                "campaign_name": random.choice(self.campaign_names) + f" #{i+1}",
                "status": random.choice(["ENABLED", "PAUSED", "ENABLED", "ENABLED"]),  # Mostly enabled
                "advertising_channel_type": "SEARCH",
                "bidding_strategy_type": random.choice(["MANUAL_CPC", "TARGET_CPA", "MAXIMIZE_CLICKS"]),
                "budget": {
                    "budget_id": str(random.randint(100000, 999999)),
                    "name": f"Budget for Campaign {i+1}",
                    "amount_micros": random.randint(10000000, *********),  # $10-$100 daily
                    "delivery_method": "STANDARD"
                },
                "targeting": {
                    "geo_targets": ["United States", "Canada"],
                    "language_targets": ["English"],
                    "device_targets": ["DESKTOP", "MOBILE", "TABLET"]
                },
                "created_date": created_date.isoformat(),
                "start_date": created_date.isoformat(),
                "end_date": None,
                # Performance metrics
                "performance": {
                    "impressions": impressions,
                    "clicks": clicks,
                    "cost_micros": cost_micros,
                    "conversions": round(conversions, 2),
                    "ctr": round(clicks / impressions, 4),
                    "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                    "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0,
                    "cost_per_conversion": int(cost_micros / conversions) if conversions > 0 else 0
                }
            }
            campaigns.append(campaign)
        
        return campaigns
    
    def generate_ad_groups_data(self, campaign_id: str, count: int = 3) -> List[Dict[str, Any]]:
        """Generate mock ad groups data"""
        ad_groups = []
        
        for i in range(count):
            ad_group_id = str(random.randint(*********, *********))
            
            # Generate performance metrics
            impressions = random.randint(500, 10000)
            clicks = int(impressions * random.uniform(0.01, 0.08))
            cost_micros = clicks * random.randint(500000, 5000000)
            conversions = clicks * random.uniform(0.01, 0.15)
            
            ad_group = {
                "ad_group_id": ad_group_id,
                "ad_group_name": f"Ad Group {i+1}",
                "campaign_id": campaign_id,
                "status": random.choice(["ENABLED", "PAUSED", "ENABLED"]),
                "type": "SEARCH_STANDARD",
                "cpc_bid_micros": random.randint(500000, 3000000),  # $0.50-$3.00
                "target_cpa_micros": random.randint(5000000, 50000000),  # $5-$50
                "performance": {
                    "impressions": impressions,
                    "clicks": clicks,
                    "cost_micros": cost_micros,
                    "conversions": round(conversions, 2),
                    "ctr": round(clicks / impressions, 4),
                    "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                    "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0
                }
            }
            ad_groups.append(ad_group)
        
        return ad_groups
    
    def generate_keywords_data(self, ad_group_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """Generate mock keywords data"""
        keywords = []
        selected_keywords = random.sample(self.keywords, min(count, len(self.keywords)))
        
        for i, keyword_text in enumerate(selected_keywords):
            keyword_id = str(random.randint(*********, *********))
            
            # Generate performance metrics
            impressions = random.randint(100, 5000)
            clicks = int(impressions * random.uniform(0.005, 0.12))  # 0.5-12% CTR
            cost_micros = clicks * random.randint(300000, 8000000)  # $0.30-$8.00 CPC
            conversions = clicks * random.uniform(0.005, 0.20)  # 0.5-20% conversion rate
            
            keyword = {
                "keyword_id": keyword_id,
                "keyword_text": keyword_text,
                "ad_group_id": ad_group_id,
                "match_type": random.choice(["BROAD", "PHRASE", "EXACT"]),
                "status": random.choice(["ENABLED", "PAUSED", "ENABLED", "ENABLED"]),
                "cpc_bid_micros": random.randint(300000, 5000000),
                "quality_score": random.randint(3, 10),
                "first_page_cpc_micros": random.randint(200000, 3000000),
                "top_of_page_cpc_micros": random.randint(500000, 6000000),
                "performance": {
                    "impressions": impressions,
                    "clicks": clicks,
                    "cost_micros": cost_micros,
                    "conversions": round(conversions, 2),
                    "ctr": round(clicks / impressions, 4) if impressions > 0 else 0,
                    "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                    "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0,
                    "search_impression_share": random.uniform(0.1, 0.9),
                    "search_rank_lost_impression_share": random.uniform(0.05, 0.3)
                }
            }
            keywords.append(keyword)
        
        return keywords
    
    def generate_ads_data(self, ad_group_id: str, count: int = 3) -> List[Dict[str, Any]]:
        """Generate mock ads data"""
        ads = []
        
        for i in range(count):
            ad_id = str(random.randint(*********, *********))
            
            # Generate performance metrics
            impressions = random.randint(200, 3000)
            clicks = int(impressions * random.uniform(0.01, 0.10))
            cost_micros = clicks * random.randint(500000, 4000000)
            conversions = clicks * random.uniform(0.01, 0.18)
            
            ad = {
                "ad_id": ad_id,
                "ad_group_id": ad_group_id,
                "type": "RESPONSIVE_SEARCH_AD",
                "status": random.choice(["ENABLED", "PAUSED", "ENABLED"]),
                "responsive_search_ad": {
                    "headlines": random.sample(self.ad_headlines, 3),
                    "descriptions": random.sample(self.ad_descriptions, 2),
                    "path1": "marketing",
                    "path2": "services"
                },
                "final_urls": ["https://example.com/landing-page"],
                "performance": {
                    "impressions": impressions,
                    "clicks": clicks,
                    "cost_micros": cost_micros,
                    "conversions": round(conversions, 2),
                    "ctr": round(clicks / impressions, 4) if impressions > 0 else 0,
                    "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                    "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0
                }
            }
            ads.append(ad)
        
        return ads
    
    def generate_historical_performance(self, days: int = 30) -> List[Dict[str, Any]]:
        """Generate historical performance data"""
        historical_data = []
        
        for i in range(days):
            date = datetime.now() - timedelta(days=days - i - 1)
            
            # Generate daily metrics with some trends
            base_impressions = 1000 + (i * 50)  # Growing trend
            daily_variation = random.uniform(0.8, 1.2)  # ±20% daily variation
            
            impressions = int(base_impressions * daily_variation)
            clicks = int(impressions * random.uniform(0.02, 0.06))
            cost_micros = clicks * random.randint(800000, 3000000)
            conversions = clicks * random.uniform(0.02, 0.12)
            
            daily_data = {
                "date": date.strftime("%Y-%m-%d"),
                "impressions": impressions,
                "clicks": clicks,
                "cost_micros": cost_micros,
                "conversions": round(conversions, 2),
                "ctr": round(clicks / impressions, 4) if impressions > 0 else 0,
                "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0
            }
            historical_data.append(daily_data)
        
        return historical_data
    
    def generate_recommendations(self, customer_id: str) -> List[Dict[str, Any]]:
        """Generate mock optimization recommendations"""
        recommendations = [
            {
                "type": "KEYWORD_RECOMMENDATION",
                "title": "Add high-performing keywords",
                "description": "We found 15 keywords that could increase your traffic by 25%",
                "impact": "HIGH",
                "effort": "LOW",
                "estimated_impact": {
                    "clicks_increase": 250,
                    "impressions_increase": 5000,
                    "cost_increase_micros": 1250000
                }
            },
            {
                "type": "BID_OPTIMIZATION",
                "title": "Optimize keyword bids",
                "description": "Adjust bids on 8 keywords to improve ad position and reduce costs",
                "impact": "MEDIUM",
                "effort": "LOW",
                "estimated_impact": {
                    "cost_reduction_micros": 500000,
                    "position_improvement": 0.5
                }
            },
            {
                "type": "AD_COPY_SUGGESTION",
                "title": "Test new ad variations",
                "description": "Create 3 new ad variations to improve click-through rates",
                "impact": "MEDIUM",
                "effort": "MEDIUM",
                "estimated_impact": {
                    "ctr_improvement": 0.015,
                    "clicks_increase": 150
                }
            },
            {
                "type": "BUDGET_OPTIMIZATION",
                "title": "Reallocate budget across campaigns",
                "description": "Move budget from low-performing to high-performing campaigns",
                "impact": "HIGH",
                "effort": "LOW",
                "estimated_impact": {
                    "conversion_increase": 12,
                    "cost_per_conversion_reduction_micros": 2000000
                }
            }
        ]
        
        return random.sample(recommendations, random.randint(2, 4))
    
    def generate_real_time_metrics(self, customer_id: str) -> Dict[str, Any]:
        """Generate real-time performance metrics"""
        now = datetime.now()
        
        # Simulate real-time data with some randomness
        base_hour = now.hour
        time_factor = 1.0 + (0.3 * (base_hour - 12) / 12)  # Peak around noon
        
        impressions_today = int(random.randint(800, 1500) * time_factor)
        clicks_today = int(impressions_today * random.uniform(0.025, 0.055))
        cost_today_micros = clicks_today * random.randint(900000, 2500000)
        conversions_today = clicks_today * random.uniform(0.03, 0.10)
        
        return {
            "timestamp": now.isoformat(),
            "today_metrics": {
                "impressions": impressions_today,
                "clicks": clicks_today,
                "cost_micros": cost_today_micros,
                "conversions": round(conversions_today, 2),
                "ctr": round(clicks_today / impressions_today, 4) if impressions_today > 0 else 0,
                "average_cpc": int(cost_today_micros / clicks_today) if clicks_today > 0 else 0
            },
            "hourly_trend": [
                {
                    "hour": i,
                    "impressions": int(random.randint(50, 150) * (1.0 + 0.3 * (i - 12) / 12)),
                    "clicks": int(random.randint(2, 8) * (1.0 + 0.3 * (i - 12) / 12)),
                    "cost_micros": random.randint(50000, 200000)
                }
                for i in range(24)
            ]
        }

    def generate_search_terms_data(self, ad_group_id: str, count: int = 20) -> List[Dict[str, Any]]:
        """Generate mock search terms data"""
        search_terms = []
        base_terms = [
            "digital marketing agency", "online advertising services", "ppc management company",
            "google ads expert", "social media marketing", "content marketing services",
            "email marketing automation", "seo optimization", "web design company",
            "ecommerce marketing", "lead generation services", "conversion optimization",
            "brand awareness campaign", "customer acquisition", "marketing analytics"
        ]

        for i, term in enumerate(random.sample(base_terms, min(count, len(base_terms)))):
            impressions = random.randint(50, 1000)
            clicks = int(impressions * random.uniform(0.01, 0.15))
            cost_micros = clicks * random.randint(400000, 6000000)
            conversions = clicks * random.uniform(0.01, 0.20)

            search_term = {
                "search_term": term,
                "ad_group_id": ad_group_id,
                "match_type": random.choice(["BROAD", "PHRASE", "EXACT"]),
                "added_as_keyword": random.choice([True, False]),
                "performance": {
                    "impressions": impressions,
                    "clicks": clicks,
                    "cost_micros": cost_micros,
                    "conversions": round(conversions, 2),
                    "ctr": round(clicks / impressions, 4) if impressions > 0 else 0,
                    "average_cpc": int(cost_micros / clicks) if clicks > 0 else 0,
                    "conversion_rate": round(conversions / clicks, 4) if clicks > 0 else 0
                }
            }
            search_terms.append(search_term)

        return search_terms

    def generate_audience_insights(self, customer_id: str) -> Dict[str, Any]:
        """Generate mock audience insights data"""
        return {
            "demographics": {
                "age_ranges": [
                    {"range": "18-24", "percentage": 15.2, "performance_index": 0.85},
                    {"range": "25-34", "percentage": 32.1, "performance_index": 1.15},
                    {"range": "35-44", "percentage": 28.7, "performance_index": 1.25},
                    {"range": "45-54", "percentage": 16.3, "performance_index": 0.95},
                    {"range": "55-64", "percentage": 6.2, "performance_index": 0.75},
                    {"range": "65+", "percentage": 1.5, "performance_index": 0.60}
                ],
                "genders": [
                    {"gender": "Male", "percentage": 58.3, "performance_index": 1.05},
                    {"gender": "Female", "percentage": 41.7, "performance_index": 1.12}
                ]
            },
            "geographic": {
                "top_locations": [
                    {"location": "New York, NY", "percentage": 18.5, "performance_index": 1.20},
                    {"location": "Los Angeles, CA", "percentage": 12.3, "performance_index": 1.10},
                    {"location": "Chicago, IL", "percentage": 8.7, "performance_index": 0.95},
                    {"location": "Houston, TX", "percentage": 7.2, "performance_index": 1.05},
                    {"location": "Phoenix, AZ", "percentage": 5.8, "performance_index": 0.90}
                ]
            },
            "devices": [
                {"device": "Mobile", "percentage": 65.2, "performance_index": 1.08},
                {"device": "Desktop", "percentage": 28.5, "performance_index": 1.15},
                {"device": "Tablet", "percentage": 6.3, "performance_index": 0.85}
            ],
            "time_of_day": [
                {"hour": f"{i:02d}:00", "percentage": random.uniform(2, 8), "performance_index": random.uniform(0.7, 1.3)}
                for i in range(24)
            ]
        }
