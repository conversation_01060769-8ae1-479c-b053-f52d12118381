"""
Google Ads OAuth Service for AdMesh
Handles OAuth 2.0 flow for Google Ads account connection
"""

import os
import logging
import secrets
import base64
import json
from typing import Dict, Any, Optional
from urllib.parse import urlencode, parse_qs
import requests
from datetime import datetime, timedelta
from config.config_manager import get_config
from firebase.config import get_db

logger = logging.getLogger(__name__)
db = get_db()


class GoogleAdsOAuthService:
    """Service class for Google Ads OAuth operations"""
    
    def __init__(self):
        """Initialize OAuth service with configuration"""
        self.config = get_config()
        self.client_id = os.getenv("GOOGLE_ADS_CLIENT_ID")
        self.client_secret = os.getenv("GOOGLE_ADS_CLIENT_SECRET")
        self.redirect_uri = os.getenv("GOOGLE_ADS_REDIRECT_URI", f"{self.config.frontend_url}/auth/google-ads/callback")
        self.scope = "https://www.googleapis.com/auth/adwords"
        
        # OAuth endpoints
        self.auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"

        # Enable mock mode for development
        self.mock_mode = True
        logger.info("Google Ads OAuth service running in mock mode")
        
    def generate_auth_url(self, brand_id: str) -> Dict[str, str]:
        """Generate OAuth authorization URL"""
        try:
            if self.mock_mode:
                # Mock OAuth flow - simulate immediate success
                state = secrets.token_urlsafe(32)

                # Store mock state in Firestore for validation
                db.collection("oauth_states").document(state).set({
                    "brand_id": brand_id,
                    "service": "google_ads",
                    "created_at": datetime.utcnow(),
                    "expires_at": datetime.utcnow() + timedelta(minutes=10),
                    "mock_mode": True
                })

                # Immediately simulate successful OAuth and store connection
                self._simulate_successful_oauth(brand_id, state)

                # Return a mock success URL
                return {
                    "auth_url": "mock://oauth-success",
                    "state": state,
                    "mock_mode": True,
                    "message": "Mock OAuth completed successfully"
                }

            # Real OAuth flow (for future use)
            # Generate state parameter for security
            state = secrets.token_urlsafe(32)

            # Store state in Firestore for validation
            db.collection("oauth_states").document(state).set({
                "brand_id": brand_id,
                "service": "google_ads",
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=10)
            })

            # Build authorization URL
            params = {
                "client_id": self.client_id,
                "redirect_uri": self.redirect_uri,
                "scope": self.scope,
                "response_type": "code",
                "state": state,
                "access_type": "offline",
                "prompt": "consent"  # Force consent to get refresh token
            }

            auth_url = f"{self.auth_url}?{urlencode(params)}"

            logger.info(f"Generated OAuth URL for brand {brand_id}")

            return {
                "auth_url": auth_url,
                "state": state
            }

        except Exception as e:
            logger.error(f"Failed to generate auth URL: {str(e)}")
            raise

    def _simulate_successful_oauth(self, brand_id: str, state: str):
        """Simulate successful OAuth connection for mock mode"""
        try:
            # Mock user info
            mock_user_info = {
                "id": "mock_user_123456789",
                "email": "<EMAIL>",
                "name": "Demo User",
                "picture": "https://via.placeholder.com/150",
                "verified_email": True
            }

            # Mock Google Ads accounts
            mock_ads_accounts = [
                {
                    "customer_id": "123-456-7890",
                    "descriptive_name": "Demo Marketing Account",
                    "currency_code": "USD",
                    "time_zone": "America/New_York",
                    "status": "ENABLED",
                    "manager": False
                },
                {
                    "customer_id": "987-654-3210",
                    "descriptive_name": "Demo E-commerce Account",
                    "currency_code": "USD",
                    "time_zone": "America/Los_Angeles",
                    "status": "ENABLED",
                    "manager": False
                }
            ]

            # Store mock OAuth connection
            db.collection("google_ads_oauth_connections").document(brand_id).set({
                "brand_id": brand_id,
                "access_token": "mock_access_token_" + secrets.token_urlsafe(32),
                "refresh_token": "mock_refresh_token_" + secrets.token_urlsafe(32),
                "token_type": "Bearer",
                "expires_at": datetime.utcnow() + timedelta(hours=1),
                "scope": self.scope,
                "user_info": mock_user_info,
                "ads_accounts": mock_ads_accounts,
                "connected_at": datetime.utcnow(),
                "mock_mode": True
            })

            logger.info(f"Mock OAuth connection created for brand {brand_id}")

        except Exception as e:
            logger.error(f"Failed to simulate OAuth connection: {str(e)}")
            raise

    def handle_oauth_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle OAuth callback and exchange code for tokens"""
        try:
            # Validate state parameter
            state_doc = db.collection("oauth_states").document(state).get()

            if not state_doc.exists:
                raise ValueError("Invalid state parameter")

            state_data = state_doc.to_dict()

            # Check if state has expired
            if datetime.utcnow() > state_data["expires_at"]:
                raise ValueError("State parameter has expired")

            brand_id = state_data["brand_id"]

            if self.mock_mode or state_data.get("mock_mode"):
                # Mock mode - return success immediately
                logger.info(f"Mock OAuth callback for brand {brand_id}")

                # Check if connection already exists (from simulate method)
                connection_doc = db.collection("google_ads_oauth_connections").document(brand_id).get()
                if connection_doc.exists:
                    connection_data = connection_doc.to_dict()
                    return {
                        "success": True,
                        "brand_id": brand_id,
                        "user_info": connection_data["user_info"],
                        "ads_accounts": connection_data["ads_accounts"],
                        "mock_mode": True,
                        "message": "Mock OAuth connection successful"
                    }
                else:
                    # Create mock connection if it doesn't exist
                    self._simulate_successful_oauth(brand_id, state)
                    connection_doc = db.collection("google_ads_oauth_connections").document(brand_id).get()
                    connection_data = connection_doc.to_dict()
                    return {
                        "success": True,
                        "brand_id": brand_id,
                        "user_info": connection_data["user_info"],
                        "ads_accounts": connection_data["ads_accounts"],
                        "mock_mode": True,
                        "message": "Mock OAuth connection created"
                    }

            # Real OAuth flow (for future use)
            # Exchange authorization code for tokens
            token_data = self._exchange_code_for_tokens(code)

            # Get user info from Google
            user_info = self._get_user_info(token_data["access_token"])

            # Get Google Ads accounts for this user
            ads_accounts = self._get_ads_accounts_mock(token_data["access_token"])
            
            # Store OAuth data in Firestore
            oauth_data = {
                "brand_id": brand_id,
                "service": "google_ads",
                "access_token": token_data["access_token"],
                "refresh_token": token_data.get("refresh_token"),
                "token_type": token_data.get("token_type", "Bearer"),
                "expires_at": datetime.utcnow() + timedelta(seconds=token_data.get("expires_in", 3600)),
                "scope": token_data.get("scope", self.scope),
                "user_info": user_info,
                "ads_accounts": ads_accounts,
                "connected_at": datetime.utcnow(),
                "last_sync": datetime.utcnow()
            }
            
            # Store in oauth_connections collection
            db.collection("oauth_connections").document(f"{brand_id}_google_ads").set(oauth_data)
            
            # Update brand document
            db.collection("brands").document(brand_id).update({
                "google_ads_connected": True,
                "google_ads_connected_at": datetime.utcnow(),
                "google_ads_accounts": ads_accounts
            })
            
            # Clean up state document
            db.collection("oauth_states").document(state).delete()
            
            logger.info(f"OAuth connection successful for brand {brand_id}")
            
            return {
                "success": True,
                "brand_id": brand_id,
                "user_info": user_info,
                "ads_accounts": ads_accounts,
                "message": "Google Ads account connected successfully"
            }
            
        except Exception as e:
            logger.error(f"OAuth callback failed: {str(e)}")
            raise
    
    def _exchange_code_for_tokens(self, code: str) -> Dict[str, Any]:
        """Exchange authorization code for access and refresh tokens"""
        try:
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": self.redirect_uri
            }
            
            response = requests.post(self.token_url, data=data)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Token exchange failed: {str(e)}")
            raise
    
    def _get_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user information from Google"""
        try:
            headers = {"Authorization": f"Bearer {access_token}"}
            response = requests.get(self.userinfo_url, headers=headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get user info: {str(e)}")
            raise
    
    def _get_ads_accounts_mock(self, access_token: str) -> list:
        """Get Google Ads accounts (mock data for now)"""
        # In real implementation, this would call Google Ads API
        # For now, return realistic mock data
        return [
            {
                "customer_id": "123-456-7890",
                "descriptive_name": "Main Advertising Account",
                "currency_code": "USD",
                "time_zone": "America/New_York",
                "status": "ENABLED",
                "account_type": "STANDARD",
                "manager": False
            },
            {
                "customer_id": "987-654-3210", 
                "descriptive_name": "Secondary Campaign Account",
                "currency_code": "USD",
                "time_zone": "America/Los_Angeles",
                "status": "ENABLED",
                "account_type": "STANDARD",
                "manager": False
            }
        ]
    
    def refresh_access_token(self, brand_id: str) -> Optional[str]:
        """Refresh access token using refresh token"""
        try:
            # Get OAuth connection
            oauth_doc = db.collection("oauth_connections").document(f"{brand_id}_google_ads").get()
            
            if not oauth_doc.exists:
                raise ValueError("No OAuth connection found")
            
            oauth_data = oauth_doc.to_dict()
            refresh_token = oauth_data.get("refresh_token")
            
            if not refresh_token:
                raise ValueError("No refresh token available")
            
            # Request new access token
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token"
            }
            
            response = requests.post(self.token_url, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            new_access_token = token_data["access_token"]
            
            # Update stored OAuth data
            oauth_doc.reference.update({
                "access_token": new_access_token,
                "expires_at": datetime.utcnow() + timedelta(seconds=token_data.get("expires_in", 3600)),
                "last_refreshed": datetime.utcnow()
            })
            
            logger.info(f"Access token refreshed for brand {brand_id}")
            
            return new_access_token
            
        except Exception as e:
            logger.error(f"Token refresh failed: {str(e)}")
            return None
    
    def get_valid_access_token(self, brand_id: str) -> Optional[str]:
        """Get valid access token, refreshing if necessary"""
        try:
            oauth_doc = db.collection("oauth_connections").document(f"{brand_id}_google_ads").get()
            
            if not oauth_doc.exists:
                return None
            
            oauth_data = oauth_doc.to_dict()
            access_token = oauth_data.get("access_token")
            expires_at = oauth_data.get("expires_at")
            
            # Check if token is still valid (with 5 minute buffer)
            if expires_at and datetime.utcnow() < (expires_at - timedelta(minutes=5)):
                return access_token
            
            # Token expired, try to refresh
            return self.refresh_access_token(brand_id)
            
        except Exception as e:
            logger.error(f"Failed to get valid access token: {str(e)}")
            return None
    
    def disconnect_account(self, brand_id: str) -> bool:
        """Disconnect Google Ads account"""
        try:
            # Delete OAuth connection
            db.collection("oauth_connections").document(f"{brand_id}_google_ads").delete()
            
            # Update brand document
            db.collection("brands").document(brand_id).update({
                "google_ads_connected": False,
                "google_ads_disconnected_at": datetime.utcnow()
            })
            
            logger.info(f"Google Ads account disconnected for brand {brand_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to disconnect account: {str(e)}")
            return False
    
    def get_connection_status(self, brand_id: str) -> Dict[str, Any]:
        """Get OAuth connection status"""
        try:
            # Check mock connections first
            if self.mock_mode:
                oauth_doc = db.collection("google_ads_oauth_connections").document(brand_id).get()
            else:
                oauth_doc = db.collection("oauth_connections").document(f"{brand_id}_google_ads").get()

            if not oauth_doc.exists:
                return {
                    "connected": False,
                    "message": "No Google Ads account connected"
                }

            oauth_data = oauth_doc.to_dict()
            expires_at = oauth_data.get("expires_at")

            return {
                "connected": True,
                "user_info": oauth_data.get("user_info", {}),
                "ads_accounts": oauth_data.get("ads_accounts", []),
                "connected_at": oauth_data.get("connected_at"),
                "last_sync": oauth_data.get("last_sync"),
                "token_expires_at": expires_at,
                "token_valid": expires_at and datetime.utcnow() < expires_at,
                "mock_mode": oauth_data.get("mock_mode", False)
            }

        except Exception as e:
            logger.error(f"Failed to get connection status: {str(e)}")
            return {
                "connected": False,
                "error": str(e)
            }
